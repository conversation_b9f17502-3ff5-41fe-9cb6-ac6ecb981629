<template>
  <div class="dashboard-page">
    <div class="page-header">
      <h1>欢迎回来，{{ username }}！</h1>
      <p>软件网络授权系统管理仪表板</p>
      <div>
        <a-button type="primary" @click="$router.push('/licenses/create')" style="margin-right: 12px;">
          创建许可证
        </a-button>
        <a-button @click="handleLogout">
          退出登录
        </a-button>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-section">
      <a-row :gutter="24">
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="总许可证数"
              :value="dashboardData.totalLicenses"
              :value-style="{ color: '#1890ff' }"
            >
              <template #prefix>
                <icon-key />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="活跃许可证"
              :value="dashboardData.activeLicenses"
              :value-style="{ color: '#52c41a' }"
            >
              <template #prefix>
                <icon-check-circle />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="今日验证"
              :value="dashboardData.todayVerifications"
              :value-style="{ color: '#722ed1' }"
            >
              <template #prefix>
                <icon-shield-check />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
        
        <a-col :span="6">
          <a-card class="stat-card">
            <a-statistic
              title="在线设备"
              :value="dashboardData.onlineDevices"
              :value-style="{ color: '#fa8c16' }"
            >
              <template #prefix>
                <icon-desktop />
              </template>
            </a-statistic>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <a-row :gutter="24">
        <!-- 左侧：最近活动 -->
        <a-col :span="16">
          <a-card title="最近活动" class="activity-card">
            <template #extra>
              <a-button type="text" @click="$router.push('/reports')">
                查看全部
              </a-button>
            </template>
            
            <div class="activity-list">
              <div 
                v-for="activity in recentActivities" 
                :key="activity.id"
                class="activity-item"
              >
                <div class="activity-icon">
                  <component :is="getActivityIcon(activity.type)" />
                </div>
                <div class="activity-content">
                  <div class="activity-title">{{ activity.title }}</div>
                  <div class="activity-time">{{ formatTime(activity.time) }}</div>
                </div>
                <div class="activity-status">
                  <StatusTag :status="activity.status" :type="activity.type" />
                </div>
              </div>
            </div>
            
            <EmptyState 
              v-if="recentActivities.length === 0"
              type="no-data"
              title="暂无活动记录"
              description="系统活动记录将在这里显示"
            />
          </a-card>
        </a-col>

        <!-- 右侧：快捷操作和系统状态 -->
        <a-col :span="8">
          <!-- 快捷操作 -->
          <a-card title="快捷操作" class="quick-actions-card">
            <div class="quick-actions">
              <a-button 
                type="outline" 
                long 
                @click="$router.push('/licenses')"
                class="action-btn"
              >
                <template #icon>
                  <icon-key />
                </template>
                许可证管理
              </a-button>
              
              <a-button 
                type="outline" 
                long 
                @click="$router.push('/users')"
                v-if="authStore.isAdmin"
                class="action-btn"
              >
                <template #icon>
                  <icon-user />
                </template>
                用户管理
              </a-button>
              
              <a-button 
                type="outline" 
                long 
                @click="$router.push('/reports')"
                v-if="authStore.isAdmin"
                class="action-btn"
              >
                <template #icon>
                  <icon-bar-chart />
                </template>
                统计报告
              </a-button>
            </div>
          </a-card>

          <!-- 系统状态 -->
          <a-card title="系统状态" class="system-status-card">
            <div class="status-list">
              <div class="status-item">
                <span class="status-label">数据库</span>
                <a-tag color="green">正常</a-tag>
              </div>
              <div class="status-item">
                <span class="status-label">缓存服务</span>
                <a-tag color="green">正常</a-tag>
              </div>
              <div class="status-item">
                <span class="status-label">API服务</span>
                <a-tag color="green">正常</a-tag>
              </div>
              <div class="status-item">
                <span class="status-label">系统负载</span>
                <a-tag color="blue">低</a-tag>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Message } from '@arco-design/web-vue'
import { 
  IconKey, 
  IconCheckCircle, 
  IconShieldCheck, 
  IconDesktop,
  IconUser,
  IconBarChart,
  IconInfoCircle
} from '@arco-design/web-vue/es/icon'
import StatusTag from '@/components/common/StatusTag.vue'
import EmptyState from '@/components/common/EmptyState.vue'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

// 用户信息
const username = ref(localStorage.getItem('username') || '用户')

// 仪表板数据
const dashboardData = reactive({
  totalLicenses: 0,
  activeLicenses: 0,
  todayVerifications: 0,
  onlineDevices: 0
})

// 最近活动数据
const recentActivities = ref([
  {
    id: 1,
    type: 'license',
    title: '创建了新的许可证 TEST-LICENSE-001',
    time: new Date(Date.now() - 1000 * 60 * 30), // 30分钟前
    status: 'active'
  },
  {
    id: 2,
    type: 'verification',
    title: '许可证 DEMO-LICENSE-002 验证成功',
    time: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2小时前
    status: 'success'
  },
  {
    id: 3,
    type: 'user',
    title: '用户 testuser 登录系统',
    time: new Date(Date.now() - 1000 * 60 * 60 * 4), // 4小时前
    status: 'active'
  }
])

// 获取活动图标
const getActivityIcon = (type: string) => {
  const iconMap = {
    license: IconKey,
    verification: IconShieldCheck,
    user: IconUser,
    device: IconDesktop
  }
  return iconMap[type] || IconInfoCircle
}

// 格式化时间
const formatTime = (time: Date) => {
  const now = new Date()
  const diff = now.getTime() - time.getTime()
  const minutes = Math.floor(diff / (1000 * 60))
  const hours = Math.floor(diff / (1000 * 60 * 60))
  const days = Math.floor(diff / (1000 * 60 * 60 * 24))
  
  if (minutes < 60) {
    return `${minutes}分钟前`
  } else if (hours < 24) {
    return `${hours}小时前`
  } else {
    return `${days}天前`
  }
}

// 加载仪表板数据
const loadDashboardData = async () => {
  try {
    // TODO: 调用实际的API获取数据
    // 模拟数据
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    dashboardData.totalLicenses = 156
    dashboardData.activeLicenses = 142
    dashboardData.todayVerifications = 1247
    dashboardData.onlineDevices = 89
  } catch (error) {
    console.error('Failed to load dashboard data:', error)
  }
}

// 退出登录
const handleLogout = () => {
  localStorage.removeItem('isLoggedIn')
  localStorage.removeItem('username')
  Message.success('已退出登录')
  router.push('/login')
}

onMounted(() => {
  loadDashboardData()
})
</script>

<style scoped>
.dashboard-page {
  padding: 24px;
}

.page-header {
  background: #fff;
  padding: 24px;
  margin-bottom: 24px;
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.page-header h1 {
  margin: 0;
  color: #262626;
  font-size: 24px;
}

.page-header p {
  margin: 4px 0 0 0;
  color: #8c8c8c;
}

.stats-section {
  margin-bottom: 24px;
}

.stat-card {
  text-align: center;
}

.main-content {
  margin-top: 24px;
}

.activity-card,
.quick-actions-card,
.system-status-card {
  margin-bottom: 24px;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 18px;
  color: #1890ff;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 14px;
  color: #262626;
  margin-bottom: 4px;
}

.activity-time {
  font-size: 12px;
  color: #8c8c8c;
}

.activity-status {
  margin-left: 12px;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-btn {
  justify-content: flex-start;
}

.status-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.status-label {
  font-size: 14px;
  color: #595959;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-section .arco-col {
    margin-bottom: 16px;
  }
}

@media (max-width: 768px) {
  .dashboard-page {
    padding: 16px;
  }
  
  .stats-section .arco-row {
    flex-direction: column;
  }
  
  .main-content .arco-row {
    flex-direction: column;
  }
  
  .main-content .arco-col {
    width: 100%;
  }
}
</style>