package model

import (
	"time"
	"gorm.io/gorm"
)

// DeviceBinding 设备绑定模型
type DeviceBinding struct {
	ID          uint           `json:"id" gorm:"primarykey"`
	LicenseKey  string         `json:"license_key" gorm:"size:255;not null;index"`
	DeviceID    string         `json:"device_id" gorm:"size:255;not null"`
	DeviceName  string         `json:"device_name" gorm:"size:100"`
	FirstSeenAt time.Time      `json:"first_seen_at"`
	LastSeenAt  time.Time      `json:"last_seen_at"`
	Status      string         `json:"status" gorm:"size:20;default:active"`
	CreatedAt   time.Time      `json:"created_at"`
	UpdatedAt   time.Time      `json:"updated_at"`
	DeletedAt   gorm.DeletedAt `json:"-" gorm:"index"`
	
	// 关联关系
	License License `json:"license,omitempty" gorm:"foreignKey:LicenseKey;references:LicenseKey"`
}

// TableName 指定表名
func (DeviceBinding) TableName() string {
	return "device_bindings"
}

// DeviceBindingStatus 设备绑定状态常量
const (
	DeviceBindingStatusActive   = "active"
	DeviceBindingStatusInactive = "inactive"
	DeviceBindingStatusBlocked  = "blocked"
)

// BeforeCreate GORM钩子：创建前设置时间
func (d *DeviceBinding) BeforeCreate(tx *gorm.DB) error {
	now := time.Now()
	if d.FirstSeenAt.IsZero() {
		d.FirstSeenAt = now
	}
	if d.LastSeenAt.IsZero() {
		d.LastSeenAt = now
	}
	return nil
}

// UpdateLastSeen 更新最后见到时间
func (d *DeviceBinding) UpdateLastSeen() {
	d.LastSeenAt = time.Now()
}

// IsActive 检查设备绑定是否激活
func (d *DeviceBinding) IsActive() bool {
	return d.Status == DeviceBindingStatusActive
}

// GetInactiveDays 获取设备不活跃天数
func (d *DeviceBinding) GetInactiveDays() int {
	return int(time.Since(d.LastSeenAt).Hours() / 24)
}