<template>
  <div class="simple-dashboard">
    <div class="header">
      <div class="header-left">
        <h1>欢迎回来，{{ username }}！</h1>
        <p class="subtitle">软件网络授权系统管理仪表板</p>
        <p class="current-time">{{ currentTime }}</p>
      </div>
      <div class="actions">
        <button @click="$router.push('/licenses/create')" class="create-btn">
          ➕ 创建许可证
        </button>
        <button @click="handleLogout" class="logout-btn">
          🚪 退出登录
        </button>
      </div>
    </div>

    <div class="content">
      <div class="stats-grid">
        <div class="stat-card" :class="{ loading }">
          <div class="stat-icon">📊</div>
          <h3>总许可证数</h3>
          <div class="stat-value">{{ loading ? '...' : stats.totalLicenses }}</div>
          <div class="stat-trend">+12% 较上月</div>
        </div>
        <div class="stat-card" :class="{ loading }">
          <div class="stat-icon">✅</div>
          <h3>活跃许可证</h3>
          <div class="stat-value">{{ loading ? '...' : stats.activeLicenses }}</div>
          <div class="stat-trend">+8% 较上月</div>
        </div>
        <div class="stat-card" :class="{ loading }">
          <div class="stat-icon">🔍</div>
          <h3>今日验证</h3>
          <div class="stat-value">{{ loading ? '...' : stats.todayVerifications }}</div>
          <div class="stat-trend">+15% 较昨日</div>
        </div>
        <div class="stat-card" :class="{ loading }">
          <div class="stat-icon">💻</div>
          <h3>在线设备</h3>
          <div class="stat-value">{{ loading ? '...' : stats.onlineDevices }}</div>
          <div class="stat-trend">+3% 较昨日</div>
        </div>
      </div>

      <div class="main-content">
        <div class="left-panel">
          <div class="card">
            <div class="card-header">
              <h3>最近活动</h3>
              <button @click="loadData" class="refresh-btn" :disabled="loading">
                {{ loading ? '🔄' : '↻' }} 刷新
              </button>
            </div>
            <div class="activity-list">
              <div v-for="activity in activities" :key="activity.id" class="activity-item">
                <div class="activity-icon" :class="activity.type">
                  {{ getActivityIcon(activity.type) }}
                </div>
                <div class="activity-content">
                  <div class="activity-title">{{ activity.title }}</div>
                  <div class="activity-time">{{ activity.time }}</div>
                </div>
                <div class="activity-status" :class="activity.status">
                  {{ getStatusText(activity.status) }}
                </div>
              </div>
            </div>
          </div>
        </div>

        <div class="right-panel">
          <div class="card">
            <h3>快捷操作</h3>
            <div class="quick-actions">
              <button @click="$router.push('/licenses')" class="action-btn">
                📄 许可证管理
              </button>
              <button @click="$router.push('/reports')" class="action-btn">
                📊 统计报告
              </button>
              <button @click="$router.push('/profile')" class="action-btn">
                👤 个人资料
              </button>
            </div>
          </div>

          <div class="card">
            <h3>系统状态</h3>
            <div class="status-list">
              <div class="status-item">
                <span>数据库</span>
                <span class="status-ok">正常</span>
              </div>
              <div class="status-item">
                <span>API服务</span>
                <span class="status-ok">正常</span>
              </div>
              <div class="status-item">
                <span>缓存服务</span>
                <span class="status-warning">离线</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const username = ref(localStorage.getItem('username') || '用户')

const stats = reactive({
  totalLicenses: 0,
  activeLicenses: 0,
  todayVerifications: 0,
  onlineDevices: 0
})

const currentTime = ref('')
const activities = ref([
  {
    id: 1,
    title: '创建了新的许可证 TEST-LICENSE-001',
    time: '30分钟前',
    type: 'create',
    status: 'success'
  },
  {
    id: 2,
    title: '许可证 DEMO-LICENSE-002 验证成功',
    time: '2小时前',
    type: 'verify',
    status: 'success'
  },
  {
    id: 3,
    title: '用户 testuser 登录系统',
    time: '4小时前',
    type: 'login',
    status: 'info'
  },
  {
    id: 4,
    title: '许可证 OLD-LICENSE-003 已过期',
    time: '6小时前',
    type: 'expire',
    status: 'warning'
  },
  {
    id: 5,
    title: '设备 Device-001 首次连接',
    time: '8小时前',
    type: 'device',
    status: 'info'
  }
])

const updateTime = () => {
  currentTime.value = new Date().toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
}

const getActivityIcon = (type) => {
  const icons = {
    create: '➕',
    verify: '✅',
    login: '👤',
    expire: '⚠️',
    device: '💻'
  }
  return icons[type] || '📋'
}

const getStatusText = (status) => {
  const statusMap = {
    success: '成功',
    info: '信息',
    warning: '警告',
    error: '错误'
  }
  return statusMap[status] || '未知'
}

const loading = ref(true)

const loadData = async () => {
  try {
    loading.value = true
    
    // 模拟加载数据
    await new Promise(resolve => setTimeout(resolve, 800))
    
    // 动画效果：逐步增加数字
    const animateNumber = (target, key) => {
      let current = 0
      const increment = target / 20
      const timer = setInterval(() => {
        current += increment
        if (current >= target) {
          stats[key] = target
          clearInterval(timer)
        } else {
          stats[key] = Math.floor(current)
        }
      }, 50)
    }
    
    animateNumber(156, 'totalLicenses')
    animateNumber(142, 'activeLicenses')
    animateNumber(1247, 'todayVerifications')
    animateNumber(89, 'onlineDevices')
    
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

const handleLogout = () => {
  localStorage.removeItem('isLoggedIn')
  localStorage.removeItem('username')
  alert('已退出登录')
  router.push('/login')
}

onMounted(() => {
  loadData()
  updateTime()
  
  // 每秒更新时间
  setInterval(updateTime, 1000)
})
</script>

<style scoped>
.simple-dashboard {
  min-height: 100vh;
  background: #f5f5f5;
  padding: 20px;
}

.header {
  background: white;
  padding: 24px;
  border-radius: 8px;
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-left h1 {
  margin: 0 0 4px 0;
  color: #1890ff;
  font-size: 24px;
}

.subtitle {
  margin: 0 0 4px 0;
  color: #8c8c8c;
  font-size: 14px;
}

.current-time {
  margin: 0;
  color: #1890ff;
  font-size: 12px;
  font-family: 'Courier New', monospace;
}

.actions {
  display: flex;
  gap: 12px;
}

.create-btn {
  padding: 10px 20px;
  background: #52c41a;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.create-btn:hover {
  background: #73d13d;
}

.logout-btn {
  padding: 10px 20px;
  background: #ff4d4f;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.logout-btn:hover {
  background: #ff7875;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.stat-card {
  background: white;
  padding: 24px;
  border-radius: 8px;
  text-align: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s, box-shadow 0.2s;
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.stat-card.loading {
  opacity: 0.7;
}

.stat-icon {
  font-size: 24px;
  margin-bottom: 8px;
}

.stat-card h3 {
  margin: 0 0 12px 0;
  color: #666;
  font-size: 14px;
  font-weight: 500;
}

.stat-value {
  font-size: 36px;
  font-weight: bold;
  color: #1890ff;
  margin-bottom: 8px;
  transition: all 0.3s ease;
}

.stat-trend {
  font-size: 12px;
  color: #52c41a;
  font-weight: 500;
}

.main-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 20px;
}

.card {
  background: white;
  padding: 20px;
  border-radius: 8px;
  margin-bottom: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.card h3 {
  margin: 0 0 15px 0;
  color: #262626;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.card-header h3 {
  margin: 0;
}

.refresh-btn {
  padding: 4px 12px;
  background: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  color: #666;
}

.refresh-btn:hover:not(:disabled) {
  background: #e6f7ff;
  border-color: #1890ff;
  color: #1890ff;
}

.refresh-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.activity-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  width: 40px;
  height: 40px;
  background: #f5f5f5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 12px;
  font-size: 18px;
}

.activity-icon.create {
  background: #f6ffed;
  color: #52c41a;
}

.activity-icon.verify {
  background: #e6f7ff;
  color: #1890ff;
}

.activity-icon.login {
  background: #f9f0ff;
  color: #722ed1;
}

.activity-icon.expire {
  background: #fff7e6;
  color: #faad14;
}

.activity-icon.device {
  background: #fff1f0;
  color: #ff4d4f;
}

.activity-status {
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 12px;
  font-weight: 500;
}

.activity-status.success {
  background: #f6ffed;
  color: #52c41a;
}

.activity-status.info {
  background: #e6f7ff;
  color: #1890ff;
}

.activity-status.warning {
  background: #fff7e6;
  color: #faad14;
}

.activity-status.error {
  background: #fff1f0;
  color: #ff4d4f;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-size: 14px;
  color: #262626;
  margin-bottom: 4px;
}

.activity-time {
  font-size: 12px;
  color: #8c8c8c;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-btn {
  padding: 12px;
  background: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  cursor: pointer;
  text-align: left;
  font-size: 14px;
}

.action-btn:hover {
  background: #e6f7ff;
  border-color: #1890ff;
}

.status-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.status-ok {
  color: #52c41a;
  font-weight: bold;
}

.status-warning {
  color: #faad14;
  font-weight: bold;
}

@media (max-width: 768px) {
  .simple-dashboard {
    padding: 16px;
  }
  
  .header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .main-content {
    grid-template-columns: 1fr;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
  
  .stat-card {
    padding: 16px;
  }
  
  .stat-value {
    font-size: 28px;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .actions {
    flex-direction: column;
    width: 100%;
  }
  
  .create-btn,
  .logout-btn {
    width: 100%;
  }
}
</style>