# Software Network Authorization System - Backend

这是一个完整的软件网络授权系统后端，提供许可证管理、用户认证、设备绑定和离线验证等功能。

## 🚀 已完成功能

### 1. 核心基础设施
- ✅ **数据库模型和连接** - SQLite数据库，GORM ORM
- ✅ **Redis缓存集成** - 缓存管理，会话存储
- ✅ **JWT认证中间件** - 令牌生成、验证、刷新

### 2. 用户认证和管理
- ✅ **用户认证API** - 登录、登出、令牌刷新、密码修改
- ✅ **用户管理API** - CRUD操作、角色管理、状态管理

### 3. 许可证核心功能
- ✅ **许可证生成和管理** - 创建、更新、删除、查询许可证
- ✅ **许可证验证服务** - 在线验证、设备绑定、使用记录
- ✅ **离线验证机制** - 离线令牌生成、验证、数据同步

### 4. API安全和监控
- ✅ **API安全中间件** - 速率限制、IP白/黑名单、API密钥认证
- ✅ **安全管理API** - 安全统计、IP管理、API密钥管理

## 📁 项目结构

```
backend/
├── cmd/                          # 可执行程序和测试工具
│   ├── server/                   # 主服务器程序
│   ├── migrate/                  # 数据库迁移工具
│   ├── auth-api-test/           # 认证API测试工具
│   ├── license-api-test/        # 许可证API测试工具
│   ├── user-api-test/           # 用户管理API测试工具
│   ├── verify-api-test/         # 验证API测试工具
│   ├── offline-api-test/        # 离线验证API测试工具
│   └── comprehensive-test/      # 综合测试工具
├── internal/                    # 内部包
│   ├── api/                     # API处理器
│   │   ├── auth.go             # 认证API
│   │   ├── user.go             # 用户管理API
│   │   ├── license.go          # 许可证管理API
│   │   ├── verify.go           # 验证API
│   │   ├── offline.go          # 离线验证API
│   │   ├── security.go         # 安全管理API
│   │   └── response.go         # 统一响应格式
│   ├── auth/                   # 认证服务
│   │   ├── jwt.go              # JWT服务
│   │   └── password.go         # 密码服务
│   ├── cache/                  # 缓存服务
│   │   ├── redis.go            # Redis连接
│   │   ├── manager.go          # 缓存管理器
│   │   └── keys.go             # 缓存键管理
│   ├── config/                 # 配置管理
│   │   └── config.go           # 配置结构
│   ├── database/               # 数据库服务
│   │   ├── database.go         # 数据库连接
│   │   ├── migration.go        # 数据库迁移
│   │   └── seed.go             # 初始数据
│   ├── middleware/             # 中间件
│   │   ├── auth.go             # 认证中间件
│   │   ├── cache.go            # 缓存中间件
│   │   └── security.go         # 安全中间件
│   ├── model/                  # 数据模型
│   │   ├── user.go             # 用户模型
│   │   ├── license.go          # 许可证模型
│   │   ├── device_binding.go   # 设备绑定模型
│   │   └── verification_log.go # 验证日志模型
│   ├── server/                 # 服务器配置
│   │   └── server.go           # HTTP服务器
│   └── service/                # 业务服务
│       └── auth.go             # 认证服务
├── scripts/                    # 脚本文件
│   ├── redis.bat              # Redis启动脚本
│   └── setup-redis.bat        # Redis安装脚本
├── docs/                       # 文档
│   ├── jwt-authentication.md   # JWT认证文档
│   └── redis-integration.md    # Redis集成文档
├── go.mod                      # Go模块文件
├── go.sum                      # Go依赖校验
└── README.md                   # 项目说明
```

## 🔧 技术栈

- **语言**: Go 1.21+
- **Web框架**: Gin
- **数据库**: SQLite (GORM)
- **缓存**: Redis
- **认证**: JWT
- **配置**: Viper
- **日志**: Gin Logger

## 🚀 快速开始

### 1. 安装依赖

```bash
go mod download
```

### 2. 配置环境

创建 `config.yaml` 文件：

```yaml
server:
  port: 8080
  mode: debug
  read_timeout: 30s
  write_timeout: 30s

database:
  driver: sqlite
  dsn: "./data/app.db"

redis:
  addr: "localhost:6379"
  password: ""
  db: 0

jwt:
  secret: "your-jwt-secret-key"
  access_token_duration: 24h
  refresh_token_duration: 168h
```

### 3. 初始化数据库

```bash
go run cmd/migrate/main.go
```

### 4. 启动服务器

```bash
go run cmd/server/main.go
```

### 5. 运行测试

```bash
# 综合测试
go run cmd/comprehensive-test/main.go

# 单独功能测试
go run cmd/auth-api-test/main.go
go run cmd/license-api-test/main.go
go run cmd/user-api-test/main.go
go run cmd/verify-api-test/main.go
go run cmd/offline-api-test/main.go
```

## 📚 API文档

### 认证API
- `POST /api/v1/auth/login` - 用户登录
- `POST /api/v1/auth/refresh` - 刷新令牌
- `POST /api/v1/auth/logout` - 用户登出
- `GET /api/v1/auth/profile` - 获取用户资料
- `PUT /api/v1/auth/profile` - 更新用户资料
- `POST /api/v1/auth/change-password` - 修改密码

### 用户管理API (管理员)
- `GET /api/v1/users` - 获取用户列表
- `POST /api/v1/users` - 创建用户
- `GET /api/v1/users/:id` - 获取用户详情
- `PUT /api/v1/users/:id` - 更新用户
- `DELETE /api/v1/users/:id` - 删除用户
- `GET /api/v1/users/stats` - 获取用户统计

### 许可证管理API
- `GET /api/v1/licenses` - 获取许可证列表
- `POST /api/v1/licenses` - 创建许可证
- `GET /api/v1/licenses/:id` - 获取许可证详情
- `PUT /api/v1/licenses/:id` - 更新许可证
- `DELETE /api/v1/licenses/:id` - 删除许可证
- `GET /api/v1/licenses/stats` - 获取许可证统计

### 验证API (需要API密钥)
- `POST /api/v1/verify/license` - 验证许可证
- `POST /api/v1/verify/bind` - 绑定设备
- `POST /api/v1/verify/unbind` - 解绑设备
- `GET /api/v1/verify/devices` - 获取设备列表

### 离线验证API
- `POST /api/v1/offline/generate-token` - 生成离线令牌 (需要API密钥)
- `POST /api/v1/offline/verify` - 验证离线令牌
- `POST /api/v1/offline/sync` - 同步离线数据 (需要API密钥)
- `GET /api/v1/offline/token-info` - 获取令牌信息

### 安全管理API (管理员)
- `GET /api/v1/security/stats` - 获取安全统计
- `GET /api/v1/security/whitelist` - 获取IP白名单
- `POST /api/v1/security/whitelist` - 添加IP到白名单
- `DELETE /api/v1/security/whitelist/:ip` - 从白名单移除IP
- `GET /api/v1/security/blacklist` - 获取IP黑名单
- `POST /api/v1/security/blacklist` - 添加IP到黑名单
- `DELETE /api/v1/security/blacklist/:ip` - 从黑名单移除IP
- `GET /api/v1/security/api-keys` - 获取API密钥列表
- `POST /api/v1/security/api-keys` - 创建API密钥
- `DELETE /api/v1/security/api-keys/:key` - 撤销API密钥

## 🔒 安全特性

### 1. 认证和授权
- JWT令牌认证
- 角色基础访问控制 (RBAC)
- 密码强度验证
- 会话管理

### 2. API安全
- 速率限制 (每秒10个请求，突发20个)
- IP白名单和黑名单
- API密钥认证
- CORS配置
- 安全头设置

### 3. 数据保护
- 密码哈希 (bcrypt)
- JWT签名验证
- 离线令牌HMAC签名
- 敏感数据脱敏

## 📊 监控和日志

### 1. 健康检查
- `GET /health` - 系统健康状态
- 数据库连接检查
- Redis连接检查

### 2. 缓存监控
- 缓存命中率统计
- 连接池状态
- 性能指标

### 3. 安全监控
- 请求频率统计
- IP访问记录
- API密钥使用统计

## 🔧 配置说明

### 环境变量
- `CONFIG_PATH` - 配置文件路径
- `GIN_MODE` - Gin运行模式 (debug/release)
- `DATABASE_URL` - 数据库连接字符串
- `REDIS_URL` - Redis连接字符串
- `JWT_SECRET` - JWT签名密钥

### 默认账户
- 用户名: `admin`
- 密码: `AdminPass123!`
- 角色: `admin`

### 默认API密钥
- 测试密钥: `test-api-key-123`
- 管理密钥: `admin-api-key-456`

## 🚧 开发计划

### 下一步功能
- [ ] 审计日志系统
- [ ] 统计数据收集
- [ ] 报告生成API
- [ ] Swagger API文档
- [ ] 单元测试
- [ ] 性能优化

### 部署相关
- [ ] Docker容器化
- [ ] 生产环境配置
- [ ] 监控和告警
- [ ] 备份和恢复

## 📝 许可证

MIT License - 详见 LICENSE 文件

## 🤝 贡献

欢迎提交 Issue 和 Pull Request！

## 📞 支持

如有问题，请创建 Issue 或联系开发团队。