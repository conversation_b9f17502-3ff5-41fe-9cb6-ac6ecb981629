@echo off
echo ========================================
echo 数据库管理工具
echo ========================================

echo.
echo 1. 运行数据库迁移
echo 2. 填充测试数据
echo 3. 重置数据库
echo 4. 查看数据库状态
echo 5. 退出
echo.

set /p choice=请选择操作 (1-5): 

if "%choice%"=="1" (
    echo 运行数据库迁移...
    go run cmd/migrate/main.go -migrate
) else if "%choice%"=="2" (
    echo 填充测试数据...
    go run cmd/migrate/main.go -seed
) else if "%choice%"=="3" (
    echo 警告：这将删除所有数据！
    set /p confirm=确认重置数据库？(y/N): 
    if /i "%confirm%"=="y" (
        go run cmd/migrate/main.go -reset
        echo 重置完成，正在重新初始化...
        go run cmd/migrate/main.go -migrate
    ) else (
        echo 操作已取消
    )
) else if "%choice%"=="4" (
    echo 检查数据库状态...
    echo 数据库文件位置: ./data/database.db
    if exist "data\database.db" (
        echo 数据库文件存在
        for %%A in (data\database.db) do echo 文件大小: %%~zA bytes
    ) else (
        echo 数据库文件不存在
    )
) else if "%choice%"=="5" (
    exit
) else (
    echo 无效选择，请重新运行脚本
)

pause