package auth

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/golang-jwt/jwt/v5"
	"software-auth/internal/cache"
	"software-auth/internal/config"
	"software-auth/internal/model"
)

// JWTService JWT服务接口
type JWTService interface {
	GenerateToken(user *model.User) (*TokenPair, error)
	ValidateToken(tokenString string) (*Claims, error)
	RefreshToken(refreshToken string) (*TokenPair, error)
	RevokeToken(tokenID string) error
	IsTokenRevoked(tokenID string) bool
}

// jwtService JWT服务实现
type jwtService struct {
	config *config.JWTConfig
}

// Claims JWT声明
type Claims struct {
	UserID   uint   `json:"user_id"`
	Username string `json:"username"`
	Role     string `json:"role"`
	TokenID  string `json:"token_id"`
	jwt.RegisteredClaims
}

// TokenPair 令牌对
type TokenPair struct {
	AccessToken  string `json:"access_token"`
	RefreshToken string `json:"refresh_token"`
	TokenType    string `json:"token_type"`
	ExpiresIn    int64  `json:"expires_in"`
}

// TokenType 令牌类型
const (
	TokenTypeAccess  = "access"
	TokenTypeRefresh = "refresh"
)

// 错误定义
var (
	ErrInvalidToken     = errors.New("invalid token")
	ErrTokenExpired     = errors.New("token expired")
	ErrTokenNotFound    = errors.New("token not found")
	ErrInvalidTokenType = errors.New("invalid token type")
	ErrUserNotFound     = errors.New("user not found")
)

// NewJWTService 创建JWT服务
func NewJWTService(config *config.JWTConfig) JWTService {
	return &jwtService{
		config: config,
	}
}

// GenerateToken 生成令牌对
func (j *jwtService) GenerateToken(user *model.User) (*TokenPair, error) {
	now := time.Now()
	tokenID := generateTokenID()

	// 生成访问令牌
	accessClaims := &Claims{
		UserID:   user.ID,
		Username: user.Username,
		Role:     user.Role,
		TokenID:  tokenID,
		RegisteredClaims: jwt.RegisteredClaims{
			Issuer:    j.config.Issuer,
			Subject:   fmt.Sprintf("%d", user.ID),
			Audience:  []string{"software-auth"},
			ExpiresAt: jwt.NewNumericDate(now.Add(time.Duration(j.config.ExpireHours) * time.Hour)),
			NotBefore: jwt.NewNumericDate(now),
			IssuedAt:  jwt.NewNumericDate(now),
			ID:        tokenID,
		},
	}

	accessToken := jwt.NewWithClaims(jwt.SigningMethodHS256, accessClaims)
	accessTokenString, err := accessToken.SignedString([]byte(j.config.Secret))
	if err != nil {
		return nil, fmt.Errorf("failed to sign access token: %v", err)
	}

	// 生成刷新令牌
	refreshClaims := &Claims{
		UserID:   user.ID,
		Username: user.Username,
		Role:     user.Role,
		TokenID:  tokenID + "_refresh",
		RegisteredClaims: jwt.RegisteredClaims{
			Issuer:    j.config.Issuer,
			Subject:   fmt.Sprintf("%d", user.ID),
			Audience:  []string{"software-auth-refresh"},
			ExpiresAt: jwt.NewNumericDate(now.Add(time.Duration(j.config.RefreshExpireHours) * time.Hour)),
			NotBefore: jwt.NewNumericDate(now),
			IssuedAt:  jwt.NewNumericDate(now),
			ID:        tokenID + "_refresh",
		},
	}

	refreshToken := jwt.NewWithClaims(jwt.SigningMethodHS256, refreshClaims)
	refreshTokenString, err := refreshToken.SignedString([]byte(j.config.Secret))
	if err != nil {
		return nil, fmt.Errorf("failed to sign refresh token: %v", err)
	}

	return &TokenPair{
		AccessToken:  accessTokenString,
		RefreshToken: refreshTokenString,
		TokenType:    "Bearer",
		ExpiresIn:    int64(j.config.ExpireHours * 3600),
	}, nil
}

// ValidateToken 验证令牌
func (j *jwtService) ValidateToken(tokenString string) (*Claims, error) {
	token, err := jwt.ParseWithClaims(tokenString, &Claims{}, func(token *jwt.Token) (interface{}, error) {
		// 验证签名方法
		if _, ok := token.Method.(*jwt.SigningMethodHMAC); !ok {
			return nil, fmt.Errorf("unexpected signing method: %v", token.Header["alg"])
		}
		return []byte(j.config.Secret), nil
	})

	if err != nil {
		if errors.Is(err, jwt.ErrTokenExpired) {
			return nil, ErrTokenExpired
		}
		return nil, ErrInvalidToken
	}

	claims, ok := token.Claims.(*Claims)
	if !ok || !token.Valid {
		return nil, ErrInvalidToken
	}

	return claims, nil
}

// RefreshToken 刷新令牌
func (j *jwtService) RefreshToken(refreshToken string) (*TokenPair, error) {
	// 验证刷新令牌
	claims, err := j.ValidateToken(refreshToken)
	if err != nil {
		return nil, err
	}

	// 检查是否为刷新令牌
	if !contains(claims.Audience, "software-auth-refresh") {
		return nil, ErrInvalidTokenType
	}

	// 这里应该从数据库获取用户信息，但为了简化，我们创建一个临时用户对象
	user := &model.User{
		ID:       claims.UserID,
		Username: claims.Username,
		Role:     claims.Role,
	}

	// 生成新的令牌对
	return j.GenerateToken(user)
}

// RevokeToken 撤销令牌
func (j *jwtService) RevokeToken(tokenID string) error {
	// 将令牌ID添加到黑名单缓存
	ctx := context.Background()
	
	// 设置黑名单缓存，TTL设置为令牌的最大有效期
	blacklistKey := fmt.Sprintf("blacklist:token:%s", tokenID)
	ttl := time.Duration(j.config.RefreshExpireHours) * time.Hour
	
	service := cache.NewCacheService()
	if err := service.Set(ctx, blacklistKey, "revoked", ttl); err != nil {
		// 如果缓存不可用，记录错误但不阻止操作
		fmt.Printf("Failed to add token to blacklist cache: %v\n", err)
	}
	
	return nil
}

// IsTokenRevoked 检查令牌是否已被撤销
func (j *jwtService) IsTokenRevoked(tokenID string) bool {
	service := cache.NewCacheService()
	ctx, cancel := context.WithTimeout(context.Background(), 1*time.Second)
	defer cancel()
	
	blacklistKey := fmt.Sprintf("blacklist:token:%s", tokenID)
	_, err := service.Get(ctx, blacklistKey)
	
	// 如果找到了黑名单记录，说明令牌已被撤销
	return err == nil
}

// generateTokenID 生成令牌ID
func generateTokenID() string {
	return fmt.Sprintf("%d", time.Now().UnixNano())
}

// contains 检查切片是否包含指定元素
func contains(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}