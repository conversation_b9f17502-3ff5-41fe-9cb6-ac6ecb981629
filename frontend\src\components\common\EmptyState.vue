<template>
  <div class="empty-state">
    <div class="empty-icon">
      <component :is="iconComponent" />
    </div>
    <h3 class="empty-title">{{ title }}</h3>
    <p v-if="description" class="empty-description">{{ description }}</p>
    <div v-if="$slots.action" class="empty-action">
      <slot name="action"></slot>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  type?: 'no-data' | 'no-result' | 'error' | 'network-error'
  title?: string
  description?: string
  icon?: string
}

const props = withDefaults(defineProps<Props>(), {
  type: 'no-data'
})

// 默认配置
const defaultConfig = {
  'no-data': {
    icon: 'icon-empty',
    title: '暂无数据',
    description: '当前没有任何数据'
  },
  'no-result': {
    icon: 'icon-search',
    title: '无搜索结果',
    description: '没有找到符合条件的数据，请尝试其他搜索条件'
  },
  'error': {
    icon: 'icon-exclamation-circle',
    title: '出现错误',
    description: '数据加载失败，请稍后重试'
  },
  'network-error': {
    icon: 'icon-wifi-off',
    title: '网络连接失败',
    description: '请检查网络连接后重试'
  }
}

// 计算图标组件
const iconComponent = computed(() => {
  return props.icon || defaultConfig[props.type].icon
})

// 计算标题
const title = computed(() => {
  return props.title || defaultConfig[props.type].title
})

// 计算描述
const description = computed(() => {
  return props.description || defaultConfig[props.type].description
})
</script>

<style scoped>
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 64px;
  color: #d9d9d9;
  margin-bottom: 16px;
}

.empty-title {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 500;
  color: #262626;
}

.empty-description {
  margin: 0 0 24px 0;
  color: #8c8c8c;
  font-size: 14px;
  max-width: 400px;
  line-height: 1.5;
}

.empty-action {
  margin-top: 16px;
}
</style>