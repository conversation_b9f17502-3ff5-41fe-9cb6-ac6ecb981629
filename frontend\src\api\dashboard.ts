import { http } from './http'

// 仪表板概览数据接口
export interface DashboardOverview {
  overview: {
    total_users: number
    total_licenses: number
    active_devices: number
    expiring_soon: number
  }
  license_status: {
    active: number
    expired: number
    suspended: number
  }
  recent_activity: {
    verifications: number
    logins: number
  }
}

// 仪表板统计数据接口
export interface DashboardStats {
  daily_verifications: Array<{
    date: string
    count: number
  }>
  daily_logins: Array<{
    date: string
    count: number
  }>
  product_stats: Array<{
    product_name: string
    count: number
  }>
  period_days: number
}

// 获取仪表板概览数据
export const getDashboardOverview = async (): Promise<DashboardOverview> => {
  const response = await http.get('/api/v1/dashboard/overview')
  return response.data.data
}

// 获取仪表板统计数据
export const getDashboardStats = async (days: number = 30): Promise<DashboardStats> => {
  const response = await http.get(`/api/v1/dashboard/stats?days=${days}`)
  return response.data.data
}

// 获取最近活动（模拟数据，后续可以从审计日志获取）
export const getRecentActivities = async () => {
  // 这里可以调用审计日志 API 获取真实数据
  // 暂时返回模拟数据
  return [
    {
      id: 1,
      type: 'create',
      title: '创建新许可证',
      description: '为客户创建了新的企业版许可证',
      time: '30分钟前',
      status: 'success'
    },
    {
      id: 2,
      type: 'verify',
      title: '许可证验证',
      description: '许可证验证成功',
      time: '2小时前',
      status: 'success'
    },
    {
      id: 3,
      type: 'login',
      title: '用户登录',
      description: '用户登录系统',
      time: '4小时前',
      status: 'info'
    }
  ]
}
