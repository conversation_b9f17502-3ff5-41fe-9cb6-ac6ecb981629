package middleware

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"software-auth/internal/database"
	"software-auth/internal/model"
)

// AuditLogger 审计日志记录器
type AuditLogger struct {
	skipPaths []string
}

// NewAuditLogger 创建审计日志记录器
func NewAuditLogger() *AuditLogger {
	return &AuditLogger{
		skipPaths: []string{
			"/health",
			"/api/v1/auth/validate", // 跳过频繁的令牌验证请求
		},
	}
}

// AuditMiddleware 审计日志中间件
func AuditMiddleware() gin.HandlerFunc {
	logger := NewAuditLogger()
	return logger.Handler()
}

// Handler 处理审计日志
func (a *AuditLogger) Handler() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 检查是否需要跳过
		if a.shouldSkip(c.Request.RequestURI) {
			c.Next()
			return
		}

		// 记录开始时间
		startTime := time.Now()

		// 读取请求体
		var requestBody []byte
		if c.Request.Body != nil {
			requestBody, _ = io.ReadAll(c.Request.Body)
			c.Request.Body = io.NopCloser(bytes.NewBuffer(requestBody))
		}

		// 创建响应写入器包装器
		writer := &responseWriter{
			ResponseWriter: c.Writer,
			body:          &bytes.Buffer{},
		}
		c.Writer = writer

		// 处理请求
		c.Next()

		// 计算处理时间
		duration := time.Since(startTime)

		// 记录审计日志
		a.logRequest(c, requestBody, duration, writer.statusCode)
	}
}

// shouldSkip 检查是否应该跳过记录
func (a *AuditLogger) shouldSkip(path string) bool {
	for _, skipPath := range a.skipPaths {
		if strings.HasPrefix(path, skipPath) {
			return true
		}
	}
	return false
}

// logRequest 记录请求日志
func (a *AuditLogger) logRequest(c *gin.Context, requestBody []byte, duration time.Duration, statusCode int) {
	// 获取用户信息
	var userID uint
	var username string
	if uid, exists := c.Get("user_id"); exists {
		userID = uid.(uint)
	}
	if uname, exists := c.Get("username"); exists {
		username = uname.(string)
	}

	// 确定操作类型和资源
	action, resource, resourceID := a.parseActionAndResource(c)

	// 处理请求数据
	requestData := a.sanitizeRequestData(string(requestBody))

	// 确定是否成功
	success := statusCode >= 200 && statusCode < 400

	// 生成消息
	message := a.generateMessage(c, action, success, statusCode)

	// 创建审计日志记录
	auditLog := model.AuditLog{
		UserID:      userID,
		Username:    username,
		Action:      action,
		Resource:    resource,
		ResourceID:  resourceID,
		Method:      c.Request.Method,
		Path:        c.Request.RequestURI,
		ClientIP:    getClientIP(c),
		UserAgent:   c.Request.UserAgent(),
		StatusCode:  statusCode,
		Success:     success,
		Message:     message,
		RequestData: requestData,
		Duration:    duration.Milliseconds(),
		CreatedAt:   time.Now(),
	}

	// 异步保存到数据库
	go a.saveAuditLog(auditLog)
}

// parseActionAndResource 解析操作类型和资源
func (a *AuditLogger) parseActionAndResource(c *gin.Context) (string, string, string) {
	method := c.Request.Method
	path := c.FullPath()
	
	// 根据路径和方法确定操作类型
	var action, resource, resourceID string

	switch {
	// 认证相关
	case strings.Contains(path, "/auth/login"):
		action = "LOGIN"
		resource = "auth"
	case strings.Contains(path, "/auth/logout"):
		action = "LOGOUT"
		resource = "auth"
	case strings.Contains(path, "/auth/refresh"):
		action = "REFRESH_TOKEN"
		resource = "auth"
	case strings.Contains(path, "/auth/change-password"):
		action = "CHANGE_PASSWORD"
		resource = "auth"
	case strings.Contains(path, "/auth/profile"):
		if method == "GET" {
			action = "VIEW_PROFILE"
		} else {
			action = "UPDATE_PROFILE"
		}
		resource = "user"

	// 用户管理
	case strings.Contains(path, "/users"):
		resource = "user"
		resourceID = c.Param("id")
		switch method {
		case "GET":
			if resourceID != "" {
				action = "VIEW_USER"
			} else {
				action = "LIST_USERS"
			}
		case "POST":
			action = "CREATE_USER"
		case "PUT":
			action = "UPDATE_USER"
		case "DELETE":
			action = "DELETE_USER"
		}

	// 许可证管理
	case strings.Contains(path, "/licenses"):
		resource = "license"
		resourceID = c.Param("id")
		switch method {
		case "GET":
			if strings.Contains(path, "/stats") {
				action = "VIEW_LICENSE_STATS"
			} else if resourceID != "" {
				action = "VIEW_LICENSE"
			} else {
				action = "LIST_LICENSES"
			}
		case "POST":
			action = "CREATE_LICENSE"
		case "PUT":
			action = "UPDATE_LICENSE"
		case "DELETE":
			action = "DELETE_LICENSE"
		}

	// 验证相关
	case strings.Contains(path, "/verify/license"):
		action = "VERIFY_LICENSE"
		resource = "license"
	case strings.Contains(path, "/verify/bind"):
		action = "BIND_DEVICE"
		resource = "device"
	case strings.Contains(path, "/verify/unbind"):
		action = "UNBIND_DEVICE"
		resource = "device"
	case strings.Contains(path, "/verify/devices"):
		action = "LIST_DEVICES"
		resource = "device"

	// 离线验证
	case strings.Contains(path, "/offline/generate-token"):
		action = "GENERATE_OFFLINE_TOKEN"
		resource = "offline_token"
	case strings.Contains(path, "/offline/verify"):
		action = "VERIFY_OFFLINE_TOKEN"
		resource = "offline_token"
	case strings.Contains(path, "/offline/sync"):
		action = "SYNC_OFFLINE_DATA"
		resource = "offline_data"

	// 安全管理
	case strings.Contains(path, "/security"):
		resource = "security"
		if strings.Contains(path, "/whitelist") {
			if method == "GET" {
				action = "VIEW_IP_WHITELIST"
			} else if method == "POST" {
				action = "ADD_IP_WHITELIST"
			} else if method == "DELETE" {
				action = "REMOVE_IP_WHITELIST"
			}
		} else if strings.Contains(path, "/blacklist") {
			if method == "GET" {
				action = "VIEW_IP_BLACKLIST"
			} else if method == "POST" {
				action = "ADD_IP_BLACKLIST"
			} else if method == "DELETE" {
				action = "REMOVE_IP_BLACKLIST"
			}
		} else if strings.Contains(path, "/api-keys") {
			if method == "GET" {
				action = "VIEW_API_KEYS"
			} else if method == "POST" {
				action = "CREATE_API_KEY"
			} else if method == "DELETE" {
				action = "REVOKE_API_KEY"
			}
		} else if strings.Contains(path, "/stats") {
			action = "VIEW_SECURITY_STATS"
		}

	default:
		action = method + "_" + strings.ToUpper(strings.ReplaceAll(path, "/", "_"))
		resource = "unknown"
	}

	return action, resource, resourceID
}

// sanitizeRequestData 清理请求数据（移除敏感信息）
func (a *AuditLogger) sanitizeRequestData(data string) string {
	if data == "" {
		return ""
	}

	// 尝试解析为JSON
	var jsonData map[string]interface{}
	if err := json.Unmarshal([]byte(data), &jsonData); err != nil {
		// 如果不是JSON，直接返回（可能需要进一步处理）
		return data
	}

	// 移除敏感字段
	sensitiveFields := []string{
		"password",
		"old_password",
		"new_password",
		"token",
		"refresh_token",
		"access_token",
		"api_key",
	}

	for _, field := range sensitiveFields {
		if _, exists := jsonData[field]; exists {
			jsonData[field] = "[REDACTED]"
		}
	}

	// 重新序列化
	sanitized, err := json.Marshal(jsonData)
	if err != nil {
		return "[PARSE_ERROR]"
	}

	return string(sanitized)
}

// generateMessage 生成日志消息
func (a *AuditLogger) generateMessage(c *gin.Context, action string, success bool, statusCode int) string {
	if success {
		return fmt.Sprintf("%s completed successfully", action)
	} else {
		return fmt.Sprintf("%s failed with status %d", action, statusCode)
	}
}

// saveAuditLog 保存审计日志到数据库
func (a *AuditLogger) saveAuditLog(log model.AuditLog) {
	db := database.GetDB()
	if db == nil {
		return // 数据库未初始化
	}

	// 忽略保存错误，避免影响主要业务流程
	db.Create(&log)
}

// responseWriter 响应写入器包装器
type responseWriter struct {
	gin.ResponseWriter
	body       *bytes.Buffer
	statusCode int
}

func (w *responseWriter) Write(b []byte) (int, error) {
	w.body.Write(b)
	return w.ResponseWriter.Write(b)
}

func (w *responseWriter) WriteHeader(statusCode int) {
	w.statusCode = statusCode
	w.ResponseWriter.WriteHeader(statusCode)
}