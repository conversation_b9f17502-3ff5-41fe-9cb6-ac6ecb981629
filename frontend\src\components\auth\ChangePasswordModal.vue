<template>
  <a-modal
    v-model:visible="visible"
    title="修改密码"
    :ok-loading="loading"
    @ok="handleSubmit"
    @cancel="handleCancel"
  >
    <a-form
      ref="formRef"
      :model="form"
      :rules="rules"
      layout="vertical"
      @submit="handleSubmit"
    >
      <a-form-item field="oldPassword" label="当前密码">
        <a-input-password
          v-model="form.oldPassword"
          placeholder="请输入当前密码"
          allow-clear
        />
      </a-form-item>

      <a-form-item field="newPassword" label="新密码">
        <a-input-password
          v-model="form.newPassword"
          placeholder="请输入新密码"
          allow-clear
        />
        <template #help>
          <div class="password-help">
            密码要求：至少8位，包含大小写字母、数字和特殊字符中的3种
          </div>
        </template>
      </a-form-item>

      <a-form-item field="confirmPassword" label="确认新密码">
        <a-input-password
          v-model="form.confirmPassword"
          placeholder="请再次输入新密码"
          allow-clear
        />
      </a-form-item>
    </a-form>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, reactive, watch } from 'vue'
import { Message } from '@arco-design/web-vue'
import type { FormInstance } from '@arco-design/web-vue'
import { useAuthStore } from '@/stores/auth'

interface Props {
  visible: boolean
}

interface Emits {
  (e: 'update:visible', visible: boolean): void
  (e: 'success'): void
}

const props = defineProps<Props>()
const emit = defineEmits<Emits>()

const authStore = useAuthStore()
const formRef = ref<FormInstance>()
const loading = ref(false)

// 表单数据
const form = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// 表单验证规则
const rules = {
  oldPassword: [
    { required: true, message: '请输入当前密码' }
  ],
  newPassword: [
    { required: true, message: '请输入新密码' },
    { 
      validator: (value: string, callback: (error?: string) => void) => {
        if (value.length < 8) {
          callback('密码长度至少8位')
          return
        }
        
        const hasUpper = /[A-Z]/.test(value)
        const hasLower = /[a-z]/.test(value)
        const hasNumber = /\d/.test(value)
        const hasSpecial = /[!@#$%^&*()_+\-=\[\]{}|;:,.<>?]/.test(value)
        
        const typeCount = [hasUpper, hasLower, hasNumber, hasSpecial].filter(Boolean).length
        
        if (typeCount < 3) {
          callback('密码必须包含大小写字母、数字和特殊字符中的至少3种')
          return
        }
        
        callback()
      }
    }
  ],
  confirmPassword: [
    { required: true, message: '请确认新密码' },
    {
      validator: (value: string, callback: (error?: string) => void) => {
        if (value !== form.newPassword) {
          callback('两次输入的密码不一致')
          return
        }
        callback()
      }
    }
  ]
}

// 计算属性：控制模态框显示
const visible = computed({
  get: () => props.visible,
  set: (value: boolean) => emit('update:visible', value)
})

// 提交表单
const handleSubmit = async () => {
  try {
    const valid = await formRef.value?.validate()
    if (!valid) return

    loading.value = true
    
    const result = await authStore.changePassword(form.oldPassword, form.newPassword)
    
    if (result.success) {
      Message.success(result.message)
      emit('success')
      resetForm()
    } else {
      Message.error(result.message)
    }
  } catch (error: any) {
    console.error('Change password error:', error)
    Message.error(error.message || '密码修改失败')
  } finally {
    loading.value = false
  }
}

// 取消操作
const handleCancel = () => {
  resetForm()
  visible.value = false
}

// 重置表单
const resetForm = () => {
  form.oldPassword = ''
  form.newPassword = ''
  form.confirmPassword = ''
  formRef.value?.resetFields()
}

// 监听模态框显示状态
watch(
  () => props.visible,
  (newVisible) => {
    if (!newVisible) {
      resetForm()
    }
  }
)
</script>

<style scoped>
.password-help {
  font-size: 12px;
  color: #999;
  margin-top: 4px;
}
</style>