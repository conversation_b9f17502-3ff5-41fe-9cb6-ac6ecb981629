package model

import (
	"encoding/json"
	"time"
	"gorm.io/gorm"
)

// License 许可证模型
type License struct {
	ID            uint           `json:"id" gorm:"primarykey"`
	LicenseKey    string         `json:"license_key" gorm:"uniqueIndex;size:255;not null"`
	ProductName   string         `json:"product_name" gorm:"size:100;not null"`
	CustomerName  string         `json:"customer_name" gorm:"size:100"`
	CustomerEmail string         `json:"customer_email" gorm:"size:100"`
	MaxDevices    int            `json:"max_devices" gorm:"default:1"`
	Features      string         `json:"-" gorm:"type:text"` // JSON格式存储
	FeaturesJSON  []string       `json:"features" gorm:"-"`  // 用于JSON序列化
	ExpiresAt     *time.Time     `json:"expires_at"`
	Status        string         `json:"status" gorm:"size:20;default:active"`
	CreatedBy     uint           `json:"created_by"`
	CreatedAt     time.Time      `json:"created_at"`
	UpdatedAt     time.Time      `json:"updated_at"`
	DeletedAt     gorm.DeletedAt `json:"-" gorm:"index"`
	
	// 关联关系
	Creator        User             `json:"creator,omitempty" gorm:"foreignKey:CreatedBy"`
	DeviceBindings []DeviceBinding  `json:"device_bindings,omitempty" gorm:"foreignKey:LicenseKey;references:LicenseKey"`
	VerifyLogs     []VerificationLog `json:"verify_logs,omitempty" gorm:"foreignKey:LicenseKey;references:LicenseKey"`
}

// TableName 指定表名
func (License) TableName() string {
	return "licenses"
}

// LicenseStatus 许可证状态常量
const (
	LicenseStatusActive   = "active"
	LicenseStatusExpired  = "expired"
	LicenseStatusDisabled = "disabled"
	LicenseStatusSuspended = "suspended"
)

// BeforeSave GORM钩子：保存前处理Features字段
func (l *License) BeforeSave(tx *gorm.DB) error {
	if len(l.FeaturesJSON) > 0 {
		featuresBytes, err := json.Marshal(l.FeaturesJSON)
		if err != nil {
			return err
		}
		l.Features = string(featuresBytes)
	}
	return nil
}

// AfterFind GORM钩子：查询后处理Features字段
func (l *License) AfterFind(tx *gorm.DB) error {
	if l.Features != "" {
		return json.Unmarshal([]byte(l.Features), &l.FeaturesJSON)
	}
	return nil
}

// IsExpired 检查许可证是否过期
func (l *License) IsExpired() bool {
	if l.ExpiresAt == nil {
		return false // 永久许可证
	}
	return time.Now().After(*l.ExpiresAt)
}

// IsActive 检查许可证是否激活
func (l *License) IsActive() bool {
	return l.Status == LicenseStatusActive && !l.IsExpired()
}

// GetDeviceCount 获取绑定设备数量
func (l *License) GetDeviceCount() int {
	count := 0
	for _, binding := range l.DeviceBindings {
		if binding.Status == DeviceBindingStatusActive {
			count++
		}
	}
	return count
}

// CanBindDevice 检查是否可以绑定新设备
func (l *License) CanBindDevice() bool {
	return l.GetDeviceCount() < l.MaxDevices
}

// HasFeature 检查是否包含指定功能
func (l *License) HasFeature(feature string) bool {
	for _, f := range l.FeaturesJSON {
		if f == feature {
			return true
		}
	}
	return false
}