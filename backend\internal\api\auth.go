package api

import (
	"net/http"

	"software-auth/internal/auth"
	"software-auth/internal/config"
	"software-auth/internal/middleware"
	"software-auth/internal/service"

	"github.com/gin-gonic/gin"
)

// AuthHandler 认证处理器
type AuthHandler struct {
	authService service.AuthService
	jwtService  auth.JWTService
}

// NewAuthHandler 创建认证处理器
func NewAuthHandler(config *config.JWTConfig) *AuthHandler {
	return &AuthHandler{
		authService: service.NewAuthService(config),
		jwtService:  auth.NewJWTService(config),
	}
}

// LoginRequest 登录请求
type LoginRequest struct {
	Username string `json:"username" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// RefreshTokenRequest 刷新令牌请求
type RefreshTokenRequest struct {
	RefreshToken string `json:"refresh_token" binding:"required"`
}

// ChangePasswordRequest 修改密码请求
type ChangePasswordRequest struct {
	OldPassword string `json:"old_password" binding:"required"`
	NewPassword string `json:"new_password" binding:"required"`
}

// Login 用户登录
// @Summary 用户登录
// @Description 用户使用用户名和密码登录系统
// @Tags 认证
// @Accept json
// @Produce json
// @Param request body LoginRequest true "登录请求"
// @Success 200 {object} service.LoginResponse "登录成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 401 {object} ErrorResponse "认证失败"
// @Router /auth/login [post]
func (h *AuthHandler) Login(c *gin.Context) {
	var req LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ErrorResponseWithMessage(c, http.StatusBadRequest, "Invalid request parameters", err.Error(), "INVALID_PARAMETERS")
		return
	}

	// 执行登录
	response, err := h.authService.Login(c.Request.Context(), req.Username, req.Password)
	if err != nil {
		var statusCode int
		var errorCode string

		switch err {
		case service.ErrInvalidCredentials:
			statusCode = http.StatusUnauthorized
			errorCode = "INVALID_CREDENTIALS"
		case service.ErrUserInactive:
			statusCode = http.StatusForbidden
			errorCode = "USER_INACTIVE"
		case service.ErrUserBlocked:
			statusCode = http.StatusForbidden
			errorCode = "USER_BLOCKED"
		default:
			statusCode = http.StatusInternalServerError
			errorCode = "LOGIN_FAILED"
		}

		ErrorResponseWithMessage(c, statusCode, err.Error(), "Login failed", errorCode)
		return
	}

	SuccessResponse(c, "Login successful", response)
}

// Logout 用户登出
// @Summary 用户登出
// @Description 用户登出系统，撤销当前令牌
// @Tags 认证
// @Security BearerAuth
// @Produce json
// @Success 200 {object} SuccessResponse "登出成功"
// @Failure 401 {object} ErrorResponse "未授权"
// @Router /auth/logout [post]
func (h *AuthHandler) Logout(c *gin.Context) {
	// 获取当前用户的令牌ID
	tokenID, exists := c.Get("token_id")
	if !exists {
		ErrorResponseWithMessage(c, http.StatusUnauthorized, "Token ID not found", "Invalid authentication context", "MISSING_TOKEN_ID")
		return
	}

	// 执行登出
	if err := h.authService.Logout(c.Request.Context(), tokenID.(string)); err != nil {
		ErrorResponseWithMessage(c, http.StatusInternalServerError, err.Error(), "Logout failed", "LOGOUT_FAILED")
		return
	}

	SuccessResponse(c, "Logout successful", nil)
}

// RefreshToken 刷新令牌
// @Summary 刷新访问令牌
// @Description 使用刷新令牌获取新的访问令牌
// @Tags 认证
// @Accept json
// @Produce json
// @Param request body RefreshTokenRequest true "刷新令牌请求"
// @Success 200 {object} SuccessResponse "刷新成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 401 {object} ErrorResponse "刷新令牌无效"
// @Router /auth/refresh [post]
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	var req RefreshTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ErrorResponseWithMessage(c, http.StatusBadRequest, "Invalid request parameters", err.Error(), "INVALID_PARAMETERS")
		return
	}

	// 刷新令牌
	tokens, err := h.authService.RefreshToken(c.Request.Context(), req.RefreshToken)
	if err != nil {
		ErrorResponseWithMessage(c, http.StatusUnauthorized, err.Error(), "Token refresh failed", "REFRESH_FAILED")
		return
	}

	SuccessResponse(c, "Token refreshed successfully", tokens)
}

// GetProfile 获取用户资料
// @Summary 获取当前用户资料
// @Description 获取当前登录用户的详细信息
// @Tags 认证
// @Security BearerAuth
// @Produce json
// @Success 200 {object} SuccessResponse "获取成功"
// @Failure 401 {object} ErrorResponse "未授权"
// @Failure 404 {object} ErrorResponse "用户不存在"
// @Router /auth/profile [get]
func (h *AuthHandler) GetProfile(c *gin.Context) {
	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		ErrorResponseWithMessage(c, http.StatusUnauthorized, "User ID not found", "Invalid authentication context", "MISSING_USER_ID")
		return
	}

	// 获取用户资料
	user, err := h.authService.GetProfile(c.Request.Context(), userID.(uint))
	if err != nil {
		ErrorResponseWithMessage(c, http.StatusNotFound, err.Error(), "Failed to get user profile", "PROFILE_NOT_FOUND")
		return
	}

	SuccessResponse(c, "Profile retrieved successfully", user)
}

// ChangePassword 修改密码
// @Summary 修改用户密码
// @Description 修改当前用户的登录密码
// @Tags 认证
// @Security BearerAuth
// @Accept json
// @Produce json
// @Param request body ChangePasswordRequest true "修改密码请求"
// @Success 200 {object} SuccessResponse "修改成功"
// @Failure 400 {object} ErrorResponse "请求参数错误"
// @Failure 401 {object} ErrorResponse "旧密码错误"
// @Router /auth/change-password [post]
func (h *AuthHandler) ChangePassword(c *gin.Context) {
	var req ChangePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ErrorResponseWithMessage(c, http.StatusBadRequest, "Invalid request parameters", err.Error(), "INVALID_PARAMETERS")
		return
	}

	// 获取当前用户ID
	userID, exists := c.Get("user_id")
	if !exists {
		ErrorResponseWithMessage(c, http.StatusUnauthorized, "User ID not found", "Invalid authentication context", "MISSING_USER_ID")
		return
	}

	// 修改密码
	err := h.authService.ChangePassword(c.Request.Context(), userID.(uint), req.OldPassword, req.NewPassword)
	if err != nil {
		var statusCode int
		var errorCode string

		switch err {
		case service.ErrInvalidCredentials:
			statusCode = http.StatusUnauthorized
			errorCode = "INVALID_OLD_PASSWORD"
		default:
			statusCode = http.StatusInternalServerError
			errorCode = "PASSWORD_CHANGE_FAILED"
		}

		ErrorResponseWithMessage(c, statusCode, err.Error(), "Password change failed", errorCode)
		return
	}

	SuccessResponse(c, "Password changed successfully", nil)
}

// GetCurrentUser 获取当前用户信息（中间件辅助函数）
func (h *AuthHandler) GetCurrentUser(c *gin.Context) {
	currentUser := middleware.GetCurrentUser(c)
	if currentUser == nil {
		ErrorResponseWithMessage(c, http.StatusUnauthorized, "User not authenticated", "Authentication required", "NOT_AUTHENTICATED")
		return
	}

	SuccessResponse(c, "Current user retrieved successfully", currentUser)
}
