<template>
  <div>
    <h1>仪表板页面</h1>
    <p>如果你能看到这个页面，说明路由工作正常！</p>
    <p>用户: {{ username }}</p>
    <button @click="goBack">返回登录</button>
  </div>
</template>

<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const username = ref(localStorage.getItem('username') || '未知')

const goBack = () => {
  localStorage.removeItem('isLoggedIn')
  localStorage.removeItem('username')
  router.push('/login')
}

console.log('MinimalDashboard 组件已加载')
</script>

<style scoped>
div {
  padding: 20px;
  font-family: Arial, sans-serif;
}

h1 {
  color: #1890ff;
}

button {
  padding: 10px 20px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}
</style>