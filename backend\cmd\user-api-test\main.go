package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"time"
)

const baseURL = "http://localhost:8080/api/v1"

type LoginRequest struct {
	Username   string `json:"username"`
	Password   string `json:"password"`
	Remember<PERSON>e bool   `json:"remember_me"`
}

type LoginResponse struct {
	Message string `json:"message"`
	Data    struct {
		User struct {
			ID       uint   `json:"id"`
			Username string `json:"username"`
			Email    string `json:"email"`
			Role     string `json:"role"`
			Status   string `json:"status"`
		} `json:"user"`
		Tokens struct {
			AccessToken  string `json:"access_token"`
			RefreshToken string `json:"refresh_token"`
			TokenType    string `json:"token_type"`
			ExpiresIn    int    `json:"expires_in"`
		} `json:"tokens"`
	} `json:"data"`
}

type CreateUserRequest struct {
	Username string `json:"username"`
	Email    string `json:"email"`
	Password string `json:"password"`
	Role     string `json:"role"`
	Status   string `json:"status"`
}

type UpdateUserRequest struct {
	Username string `json:"username,omitempty"`
	Email    string `json:"email,omitempty"`
	Role     string `json:"role,omitempty"`
	Status   string `json:"status,omitempty"`
}

type ChangeUserPasswordRequest struct {
	NewPassword string `json:"new_password"`
}

func main() {
	fmt.Println("User Management API Test Tool")
	fmt.Println("============================")

	// 1. 登录获取访问令牌
	fmt.Println("\n1. Testing Login...")
	accessToken, err := login()
	if err != nil {
		log.Fatalf("❌ Login failed: %v", err)
	}
	fmt.Println("✅ Login successful")

	// 2. 测试创建用户
	fmt.Println("\n2. Testing Create User...")
	userID, err := testCreateUser(accessToken)
	if err != nil {
		log.Printf("❌ Create user failed: %v", err)
	} else {
		fmt.Printf("✅ User created with ID: %d\n", userID)
	}

	// 3. 测试获取用户列表
	fmt.Println("\n3. Testing Get User List...")
	if err := testGetUserList(accessToken); err != nil {
		log.Printf("❌ Get user list failed: %v", err)
	} else {
		fmt.Println("✅ User list retrieved successfully")
	}

	// 4. 测试获取单个用户
	if userID > 0 {
		fmt.Println("\n4. Testing Get Single User...")
		if err := testGetUser(accessToken, userID); err != nil {
			log.Printf("❌ Get user failed: %v", err)
		} else {
			fmt.Println("✅ User retrieved successfully")
		}

		// 5. 测试更新用户
		fmt.Println("\n5. Testing Update User...")
		if err := testUpdateUser(accessToken, userID); err != nil {
			log.Printf("❌ Update user failed: %v", err)
		} else {
			fmt.Println("✅ User updated successfully")
		}

		// 6. 测试修改用户密码
		fmt.Println("\n6. Testing Change User Password...")
		if err := testChangeUserPassword(accessToken, userID); err != nil {
			log.Printf("❌ Change user password failed: %v", err)
		} else {
			fmt.Println("✅ User password changed successfully")
		}
	}

	// 7. 测试获取用户统计
	fmt.Println("\n7. Testing Get User Stats...")
	if err := testGetUserStats(accessToken); err != nil {
		log.Printf("❌ Get user stats failed: %v", err)
	} else {
		fmt.Println("✅ User stats retrieved successfully")
	}

	// 8. 测试删除用户（可选）
	if userID > 0 {
		fmt.Println("\n8. Testing Delete User...")
		if err := testDeleteUser(accessToken, userID); err != nil {
			log.Printf("❌ Delete user failed: %v", err)
		} else {
			fmt.Println("✅ User deleted successfully")
		}
	}

	fmt.Println("\n🎉 All user management API tests completed!")
}

func login() (string, error) {
	req := LoginRequest{
		Username:   "admin",
		Password:   "AdminPass123!",
		RememberMe: true,
	}

	jsonData, _ := json.Marshal(req)
	resp, err := http.Post(baseURL+"/auth/login", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("login failed with status %d: %s", resp.StatusCode, string(body))
	}

	var loginResp LoginResponse
	if err := json.Unmarshal(body, &loginResp); err != nil {
		return "", err
	}

	fmt.Printf("   User: %s (%s)\n", loginResp.Data.User.Username, loginResp.Data.User.Role)
	return loginResp.Data.Tokens.AccessToken, nil
}

func testCreateUser(accessToken string) (int, error) {
	req := CreateUserRequest{
		Username: "testuser",
		Email:    "<EMAIL>",
		Password: "TestPass123!",
		Role:     "user",
		Status:   "active",
	}

	jsonData, _ := json.Marshal(req)
	client := &http.Client{}
	httpReq, _ := http.NewRequest("POST", baseURL+"/users", bytes.NewBuffer(jsonData))
	httpReq.Header.Set("Authorization", "Bearer "+accessToken)
	httpReq.Header.Set("Content-Type", "application/json")

	resp, err := client.Do(httpReq)
	if err != nil {
		return 0, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return 0, err
	}

	if resp.StatusCode != http.StatusOK {
		return 0, fmt.Errorf("create user failed with status %d: %s", resp.StatusCode, string(body))
	}

	var response struct {
		Message string `json:"message"`
		Data    struct {
			ID       int    `json:"id"`
			Username string `json:"username"`
			Email    string `json:"email"`
			Role     string `json:"role"`
			Status   string `json:"status"`
		} `json:"data"`
	}

	if err := json.Unmarshal(body, &response); err != nil {
		return 0, err
	}

	fmt.Printf("   Username: %s\n", response.Data.Username)
	fmt.Printf("   Email: %s\n", response.Data.Email)
	fmt.Printf("   Role: %s\n", response.Data.Role)
	fmt.Printf("   Status: %s\n", response.Data.Status)
	return response.Data.ID, nil
}

func testGetUserList(accessToken string) error {
	client := &http.Client{}
	req, _ := http.NewRequest("GET", baseURL+"/users?page=1&page_size=10", nil)
	req.Header.Set("Authorization", "Bearer "+accessToken)

	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("get user list failed with status %d: %s", resp.StatusCode, string(body))
	}

	var response struct {
		Message string `json:"message"`
		Data    struct {
			Total      int64 `json:"total"`
			Page       int   `json:"page"`
			PageSize   int   `json:"page_size"`
			TotalPages int   `json:"total_pages"`
			Users      []struct {
				ID       int    `json:"id"`
				Username string `json:"username"`
				Email    string `json:"email"`
				Role     string `json:"role"`
				Status   string `json:"status"`
			} `json:"users"`
		} `json:"data"`
	}

	if err := json.Unmarshal(body, &response); err != nil {
		return err
	}

	fmt.Printf("   Total Users: %d\n", response.Data.Total)
	fmt.Printf("   Page: %d/%d\n", response.Data.Page, response.Data.TotalPages)
	fmt.Printf("   Users on this page: %d\n", len(response.Data.Users))
	
	for i, user := range response.Data.Users {
		if i < 3 { // 只显示前3个用户
			fmt.Printf("     %d. %s (%s) - %s\n", i+1, user.Username, user.Role, user.Status)
		}
	}
	
	return nil
}

func testGetUser(accessToken string, userID int) error {
	client := &http.Client{}
	req, _ := http.NewRequest("GET", fmt.Sprintf("%s/users/%d", baseURL, userID), nil)
	req.Header.Set("Authorization", "Bearer "+accessToken)

	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("get user failed with status %d: %s", resp.StatusCode, string(body))
	}

	var response struct {
		Message string `json:"message"`
		Data    struct {
			ID          int       `json:"id"`
			Username    string    `json:"username"`
			Email       string    `json:"email"`
			Role        string    `json:"role"`
			Status      string    `json:"status"`
			CreatedAt   time.Time `json:"created_at"`
			LastLoginAt time.Time `json:"last_login_at"`
		} `json:"data"`
	}

	if err := json.Unmarshal(body, &response); err != nil {
		return err
	}

	fmt.Printf("   ID: %d\n", response.Data.ID)
	fmt.Printf("   Username: %s\n", response.Data.Username)
	fmt.Printf("   Email: %s\n", response.Data.Email)
	fmt.Printf("   Role: %s\n", response.Data.Role)
	fmt.Printf("   Status: %s\n", response.Data.Status)
	fmt.Printf("   Created: %s\n", response.Data.CreatedAt.Format("2006-01-02 15:04:05"))
	return nil
}

func testUpdateUser(accessToken string, userID int) error {
	req := UpdateUserRequest{
		Email:  "<EMAIL>",
		Status: "inactive",
	}

	jsonData, _ := json.Marshal(req)
	client := &http.Client{}
	httpReq, _ := http.NewRequest("PUT", fmt.Sprintf("%s/users/%d", baseURL, userID), bytes.NewBuffer(jsonData))
	httpReq.Header.Set("Authorization", "Bearer "+accessToken)
	httpReq.Header.Set("Content-Type", "application/json")

	resp, err := client.Do(httpReq)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("update user failed with status %d: %s", resp.StatusCode, string(body))
	}

	fmt.Printf("   User updated successfully\n")
	return nil
}

func testChangeUserPassword(accessToken string, userID int) error {
	req := ChangeUserPasswordRequest{
		NewPassword: "NewTestPass123!",
	}

	jsonData, _ := json.Marshal(req)
	client := &http.Client{}
	httpReq, _ := http.NewRequest("POST", fmt.Sprintf("%s/users/%d/change-password", baseURL, userID), bytes.NewBuffer(jsonData))
	httpReq.Header.Set("Authorization", "Bearer "+accessToken)
	httpReq.Header.Set("Content-Type", "application/json")

	resp, err := client.Do(httpReq)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("change user password failed with status %d: %s", resp.StatusCode, string(body))
	}

	fmt.Printf("   Password changed successfully\n")
	return nil
}

func testGetUserStats(accessToken string) error {
	client := &http.Client{}
	req, _ := http.NewRequest("GET", baseURL+"/users/stats", nil)
	req.Header.Set("Authorization", "Bearer "+accessToken)

	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("get user stats failed with status %d: %s", resp.StatusCode, string(body))
	}

	var response struct {
		Message string `json:"message"`
		Data    struct {
			Total     int64 `json:"total"`
			Active    int64 `json:"active"`
			Inactive  int64 `json:"inactive"`
			Blocked   int64 `json:"blocked"`
			Suspended int64 `json:"suspended"`
			Admins    int64 `json:"admins"`
			Users     int64 `json:"users"`
		} `json:"data"`
	}

	if err := json.Unmarshal(body, &response); err != nil {
		return err
	}

	fmt.Printf("   Total: %d\n", response.Data.Total)
	fmt.Printf("   Active: %d\n", response.Data.Active)
	fmt.Printf("   Inactive: %d\n", response.Data.Inactive)
	fmt.Printf("   Blocked: %d\n", response.Data.Blocked)
	fmt.Printf("   Suspended: %d\n", response.Data.Suspended)
	fmt.Printf("   Admins: %d\n", response.Data.Admins)
	fmt.Printf("   Regular Users: %d\n", response.Data.Users)
	return nil
}

func testDeleteUser(accessToken string, userID int) error {
	client := &http.Client{}
	req, _ := http.NewRequest("DELETE", fmt.Sprintf("%s/users/%d", baseURL, userID), nil)
	req.Header.Set("Authorization", "Bearer "+accessToken)

	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("delete user failed with status %d: %s", resp.StatusCode, string(body))
	}

	fmt.Printf("   User deleted successfully\n")
	return nil
}