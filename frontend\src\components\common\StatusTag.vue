<template>
  <a-tag :color="tagColor" :size="size">
    <template #icon v-if="showIcon">
      <component :is="iconComponent" />
    </template>
    {{ displayText }}
  </a-tag>
</template>

<script setup lang="ts">
import { computed } from 'vue'

interface Props {
  status: string
  type?: 'license' | 'user' | 'device' | 'verification'
  size?: 'small' | 'medium' | 'large'
  showIcon?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  type: 'license',
  size: 'medium',
  showIcon: true
})

// 状态配置映射
const statusConfig = {
  license: {
    active: { color: 'green', text: '有效', icon: 'icon-check-circle' },
    expired: { color: 'red', text: '已过期', icon: 'icon-close-circle' },
    disabled: { color: 'gray', text: '已禁用', icon: 'icon-minus-circle' },
    suspended: { color: 'orange', text: '已暂停', icon: 'icon-exclamation-circle' }
  },
  user: {
    active: { color: 'green', text: '活跃', icon: 'icon-check-circle' },
    inactive: { color: 'gray', text: '未激活', icon: 'icon-minus-circle' },
    blocked: { color: 'red', text: '已封禁', icon: 'icon-close-circle' }
  },
  device: {
    active: { color: 'green', text: '在线', icon: 'icon-check-circle' },
    inactive: { color: 'gray', text: '离线', icon: 'icon-minus-circle' },
    blocked: { color: 'red', text: '已封禁', icon: 'icon-close-circle' }
  },
  verification: {
    success: { color: 'green', text: '成功', icon: 'icon-check-circle' },
    failed: { color: 'red', text: '失败', icon: 'icon-close-circle' },
    expired: { color: 'orange', text: '过期', icon: 'icon-clock-circle' },
    invalid: { color: 'red', text: '无效', icon: 'icon-exclamation-circle' }
  }
}

// 计算标签颜色
const tagColor = computed(() => {
  const config = statusConfig[props.type]?.[props.status]
  return config?.color || 'gray'
})

// 计算显示文本
const displayText = computed(() => {
  const config = statusConfig[props.type]?.[props.status]
  return config?.text || props.status
})

// 计算图标组件
const iconComponent = computed(() => {
  const config = statusConfig[props.type]?.[props.status]
  return config?.icon || 'icon-question-circle'
})
</script>