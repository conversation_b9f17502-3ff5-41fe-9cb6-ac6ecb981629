@echo off
echo ========================================
echo 认证系统管理工具
echo ========================================

echo.
echo 1. 测试JWT认证功能
echo 2. 测试密码强度
echo 3. 生成测试令牌
echo 4. 验证令牌
echo 5. 退出
echo.

set /p choice=请选择操作 (1-5): 

if "%choice%"=="1" (
    echo 测试JWT认证功能...
    go run cmd/jwt-test/main.go
) else if "%choice%"=="2" (
    echo 测试密码强度...
    echo 请输入要测试的密码：
    set /p password=
    echo 测试密码: %password%
    echo 注意：密码至少8位，包含大小写字母、数字和特殊字符中的3种
) else if "%choice%"=="3" (
    echo 生成测试令牌...
    echo 这需要启动完整的服务器来测试
    echo 请使用以下curl命令测试登录：
    echo curl -X POST http://localhost:8080/api/v1/auth/login ^
    echo      -H "Content-Type: application/json" ^
    echo      -d "{\"username\":\"admin\",\"password\":\"password\"}"
) else if "%choice%"=="4" (
    echo 验证令牌...
    echo 请输入要验证的JWT令牌：
    set /p token=
    echo 令牌: %token%
    echo 请使用以下curl命令验证：
    echo curl -X GET http://localhost:8080/api/v1/auth/profile ^
    echo      -H "Authorization: Bearer %token%"
) else if "%choice%"=="5" (
    exit
) else (
    echo 无效选择，请重新运行脚本
)

pause