<template>
  <div class="test-page">
    <h1>软件网络授权系统</h1>
    <p>系统正在运行中...</p>
    <a-button type="primary" @click="handleClick">测试按钮</a-button>
  </div>
</template>

<script setup lang="ts">
import { Message } from '@arco-design/web-vue'

const handleClick = () => {
  Message.success('系统运行正常！')
}
</script>

<style scoped>
.test-page {
  padding: 50px;
  text-align: center;
}

h1 {
  color: #1890ff;
  margin-bottom: 20px;
}

p {
  margin-bottom: 30px;
  font-size: 16px;
}
</style>