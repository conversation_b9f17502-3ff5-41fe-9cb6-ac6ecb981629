package auth

import (
	"errors"
	"golang.org/x/crypto/bcrypt"
)

// PasswordService 密码服务接口
type PasswordService interface {
	HashPassword(password string) (string, error)
	VerifyPassword(hashedPassword, password string) error
	IsStrongPassword(password string) error
}

// passwordService 密码服务实现
type passwordService struct {
	cost int
}

// 密码强度要求
const (
	MinPasswordLength = 8
	MaxPasswordLength = 128
)

// 错误定义
var (
	ErrPasswordTooShort = errors.New("password too short")
	ErrPasswordTooLong  = errors.New("password too long")
	ErrPasswordWeak     = errors.New("password too weak")
	ErrPasswordMismatch = errors.New("password mismatch")
)

// NewPasswordService 创建密码服务
func NewPasswordService() PasswordService {
	return &passwordService{
		cost: bcrypt.DefaultCost,
	}
}

// HashPassword 加密密码
func (p *passwordService) HashPassword(password string) (string, error) {
	// 验证密码强度
	if err := p.IsStrongPassword(password); err != nil {
		return "", err
	}

	// 生成哈希
	hashedBytes, err := bcrypt.GenerateFromPassword([]byte(password), p.cost)
	if err != nil {
		return "", err
	}

	return string(hashedBytes), nil
}

// VerifyPassword 验证密码
func (p *passwordService) VerifyPassword(hashedPassword, password string) error {
	err := bcrypt.CompareHashAndPassword([]byte(hashedPassword), []byte(password))
	if err != nil {
		if errors.Is(err, bcrypt.ErrMismatchedHashAndPassword) {
			return ErrPasswordMismatch
		}
		return err
	}
	return nil
}

// IsStrongPassword 检查密码强度
func (p *passwordService) IsStrongPassword(password string) error {
	// 检查长度
	if len(password) < MinPasswordLength {
		return ErrPasswordTooShort
	}
	if len(password) > MaxPasswordLength {
		return ErrPasswordTooLong
	}

	// 检查复杂性
	var (
		hasUpper   = false
		hasLower   = false
		hasNumber  = false
		hasSpecial = false
	)

	for _, char := range password {
		switch {
		case char >= 'A' && char <= 'Z':
			hasUpper = true
		case char >= 'a' && char <= 'z':
			hasLower = true
		case char >= '0' && char <= '9':
			hasNumber = true
		case isSpecialChar(char):
			hasSpecial = true
		}
	}

	// 至少需要包含3种类型的字符
	count := 0
	if hasUpper {
		count++
	}
	if hasLower {
		count++
	}
	if hasNumber {
		count++
	}
	if hasSpecial {
		count++
	}

	if count < 3 {
		return ErrPasswordWeak
	}

	return nil
}

// isSpecialChar 检查是否为特殊字符
func isSpecialChar(char rune) bool {
	specialChars := "!@#$%^&*()_+-=[]{}|;:,.<>?"
	for _, special := range specialChars {
		if char == special {
			return true
		}
	}
	return false
}