import { http } from './http'

// 用户接口定义
export interface User {
  id: number
  username: string
  email: string
  role: string
  status: string
  created_at: string
  updated_at: string
  last_login_at?: string
}

// 用户列表请求参数
export interface UserListParams {
  page?: number
  page_size?: number
  keyword?: string
  role?: string
  status?: string
}

// 用户列表响应
export interface UserListResponse {
  users: User[]
  total: number
  page: number
  page_size: number
  total_pages: number
}

// 创建用户请求
export interface CreateUserRequest {
  username: string
  email: string
  password: string
  role?: string
  status?: string
}

// 更新用户请求
export interface UpdateUserRequest {
  email?: string
  role?: string
  status?: string
}

// 用户统计
export interface UserStats {
  total_users: number
  active_users: number
  admin_users: number
  recent_logins: number
}

// 获取用户列表
export const getUserList = async (params: UserListParams = {}): Promise<UserListResponse> => {
  const response = await http.get('/api/v1/users', { params })
  return response.data.data
}

// 获取用户详情
export const getUserDetail = async (id: number): Promise<User> => {
  const response = await http.get(`/api/v1/users/${id}`)
  return response.data.data
}

// 创建用户
export const createUser = async (data: CreateUserRequest): Promise<User> => {
  const response = await http.post('/api/v1/users', data)
  return response.data.data
}

// 更新用户
export const updateUser = async (id: number, data: UpdateUserRequest): Promise<User> => {
  const response = await http.put(`/api/v1/users/${id}`, data)
  return response.data.data
}

// 删除用户
export const deleteUser = async (id: number): Promise<void> => {
  await http.delete(`/api/v1/users/${id}`)
}

// 获取用户统计
export const getUserStats = async (): Promise<UserStats> => {
  const response = await http.get('/api/v1/users/stats')
  return response.data.data
}

// 重置用户密码
export const resetUserPassword = async (id: number, newPassword: string): Promise<void> => {
  await http.post(`/api/v1/users/${id}/reset-password`, { new_password: newPassword })
}
