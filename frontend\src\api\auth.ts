import { request } from './http'
import type { 
  LoginRequest, 
  LoginResponse, 
  RefreshTokenRequest, 
  ChangePasswordRequest,
  UserInfo,
  TokenPair
} from '@/types/auth'

export const authApi = {
  // 用户登录
  login: (data: LoginRequest) => {
    return request.post<LoginResponse>('/auth/login', data)
  },

  // 用户登出
  logout: () => {
    return request.post('/auth/logout')
  },

  // 刷新令牌
  refreshToken: (data: RefreshTokenRequest) => {
    return request.post<TokenPair>('/auth/refresh', data)
  },

  // 获取用户资料
  getProfile: () => {
    return request.get<UserInfo>('/auth/profile')
  },

  // 获取当前用户信息
  getCurrentUser: () => {
    return request.get<UserInfo>('/auth/me')
  },

  // 修改密码
  changePassword: (data: ChangePasswordRequest) => {
    return request.post('/auth/change-password', data)
  }
}