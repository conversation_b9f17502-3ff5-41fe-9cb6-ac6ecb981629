<template>
  <div class="login-container">
    <div class="login-content">
      <!-- 左侧介绍 -->
      <div class="login-intro">
        <div class="intro-content">
          <h1 class="intro-title">软件网络授权系统</h1>
          <p class="intro-subtitle">安全、高效的软件许可证管理平台</p>
          <div class="intro-features">
            <div class="feature-item">
              <icon-shield-check class="feature-icon" />
              <span>安全可靠的许可证验证</span>
            </div>
            <div class="feature-item">
              <icon-dashboard class="feature-icon" />
              <span>直观的管理仪表板</span>
            </div>
            <div class="feature-item">
              <icon-bar-chart class="feature-icon" />
              <span>详细的使用统计报告</span>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧登录表单 -->
      <div class="login-form-container">
        <div class="login-box">
          <div class="login-header">
            <h2>欢迎回来</h2>
            <p>请登录您的账户以继续</p>
          </div>
          
          <a-form
            ref="formRef"
            :model="loginForm"
            :rules="rules"
            layout="vertical"
            class="login-form"
          >
            <a-form-item field="username" label="用户名">
              <a-input
                v-model="loginForm.username"
                placeholder="请输入用户名"
                size="large"
                allow-clear
                @keyup.enter="handleLogin"
              >
                <template #prefix>
                  <icon-user />
                </template>
              </a-input>
            </a-form-item>
            
            <a-form-item field="password" label="密码">
              <a-input-password
                v-model="loginForm.password"
                placeholder="请输入密码"
                size="large"
                allow-clear
                @keyup.enter="handleLogin"
              >
                <template #prefix>
                  <icon-lock />
                </template>
              </a-input-password>
            </a-form-item>

            <a-form-item>
              <div class="login-options">
                <a-checkbox v-model="rememberMe">记住我</a-checkbox>
              </div>
            </a-form-item>
            
            <a-form-item>
              <a-button
                type="primary"
                size="large"
                long
                :loading="loading"
                @click="handleLogin"
              >
                登录
              </a-button>
            </a-form-item>
          </a-form>

          <!-- 默认账户提示 -->
          <div class="login-tips">
            <a-alert type="info" show-icon>
              <template #title>默认账户</template>
              <div class="tips-content">
                <p><strong>管理员：</strong>admin / AdminPass123!</p>
                <p><strong>普通用户：</strong>testuser / TestPass123!</p>
              </div>
            </a-alert>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { Message } from '@arco-design/web-vue'
import type { FormInstance } from '@arco-design/web-vue'

const router = useRouter()
const formRef = ref<FormInstance>()
const loading = ref(false)
const rememberMe = ref(false)

const loginForm = reactive({
  username: 'admin',
  password: 'AdminPass123!'
})

const rules = {
  username: [
    { required: true, message: '请输入用户名' }
  ],
  password: [
    { required: true, message: '请输入密码' }
  ]
}

const handleLogin = async () => {
  console.log('登录按钮被点击')
  console.log('表单数据:', loginForm)
  
  try {
    loading.value = true
    console.log('开始验证表单')
    
    // 简化验证，直接检查用户名密码
    if (!loginForm.username || !loginForm.password) {
      Message.error('请输入用户名和密码')
      return
    }
    
    console.log('表单验证通过，开始登录验证')
    
    // 模拟登录验证
    if (loginForm.username === 'admin' && loginForm.password === 'AdminPass123!') {
      console.log('登录验证成功')
      Message.success('登录成功')
      
      // 保存登录状态
      localStorage.setItem('isLoggedIn', 'true')
      localStorage.setItem('username', loginForm.username)
      console.log('登录状态已保存')
      
      // 跳转到仪表板
      console.log('准备跳转到仪表板')
      await router.push('/dashboard')
      console.log('跳转完成')
    } else {
      console.log('登录验证失败')
      Message.error('用户名或密码错误')
    }
  } catch (error: any) {
    console.error('Login error:', error)
    Message.error('登录失败，请稍后重试')
  } finally {
    loading.value = false
    console.log('登录流程结束')
  }
}
</script>

<style scoped>
.login-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.login-content {
  display: flex;
  background: white;
  border-radius: 12px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  max-width: 1000px;
  width: 100%;
  min-height: 600px;
}

.login-intro {
  flex: 1;
  background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
  color: white;
  padding: 60px 40px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.intro-content {
  max-width: 400px;
}

.intro-title {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 16px 0;
  line-height: 1.2;
}

.intro-subtitle {
  font-size: 18px;
  margin: 0 0 40px 0;
  opacity: 0.9;
  line-height: 1.5;
}

.intro-features {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 16px;
}

.feature-icon {
  font-size: 20px;
  opacity: 0.9;
}

.login-form-container {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.login-box {
  width: 100%;
  max-width: 400px;
}

.login-header {
  text-align: center;
  margin-bottom: 32px;
}

.login-header h2 {
  margin: 0 0 8px 0;
  color: #262626;
  font-size: 28px;
  font-weight: 600;
}

.login-header p {
  margin: 0;
  color: #8c8c8c;
  font-size: 16px;
}

.login-form {
  margin-top: 24px;
}

.login-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.login-tips {
  margin-top: 24px;
}

.tips-content p {
  margin: 4px 0;
  font-size: 13px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .login-content {
    flex-direction: column;
    max-width: 400px;
  }
  
  .login-intro {
    padding: 40px 20px;
    text-align: center;
  }
  
  .intro-title {
    font-size: 24px;
  }
  
  .intro-subtitle {
    font-size: 16px;
    margin-bottom: 24px;
  }
  
  .login-form-container {
    padding: 20px;
  }
}

/* 深色主题适配 */
.login-container[data-theme="dark"] .login-content {
  background: #1f1f1f;
}

.login-container[data-theme="dark"] .login-header h2 {
  color: #fff;
}

.login-container[data-theme="dark"] .login-header p {
  color: #bfbfbf;
}
</style>