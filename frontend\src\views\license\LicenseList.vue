<template>
  <div class="license-list-page">
    <PageHeader 
      title="许可证管理" 
      description="管理和监控所有软件许可证"
    >
      <template #extra>
        <a-space>
          <a-button @click="handleRefresh" :loading="loading">
            <template #icon>
              <icon-refresh />
            </template>
            刷新
          </a-button>
          <a-button type="primary" @click="$router.push('/licenses/create')">
            <template #icon>
              <icon-plus />
            </template>
            创建许可证
          </a-button>
        </a-space>
      </template>
    </PageHeader>

    <!-- 筛选器 -->
    <a-card class="filter-card">
      <a-form :model="filters" layout="inline" @submit="handleSearch">
        <a-form-item label="搜索">
          <a-input
            v-model="filters.search"
            placeholder="许可证密钥、产品名称或客户名称"
            style="width: 300px"
            allow-clear
            @clear="handleSearch"
          >
            <template #prefix>
              <icon-search />
            </template>
          </a-input>
        </a-form-item>

        <a-form-item label="状态">
          <a-select
            v-model="filters.status"
            placeholder="选择状态"
            style="width: 120px"
            allow-clear
            @change="handleSearch"
          >
            <a-option value="active">有效</a-option>
            <a-option value="expired">已过期</a-option>
            <a-option value="disabled">已禁用</a-option>
            <a-option value="suspended">已暂停</a-option>
          </a-select>
        </a-form-item>

        <a-form-item label="产品">
          <a-select
            v-model="filters.product_name"
            placeholder="选择产品"
            style="width: 150px"
            allow-clear
            @change="handleSearch"
          >
            <a-option v-for="product in productList" :key="product" :value="product">
              {{ product }}
            </a-option>
          </a-select>
        </a-form-item>

        <a-form-item label="创建时间">
          <a-range-picker
            v-model="filters.dateRange"
            style="width: 250px"
            @change="handleSearch"
          />
        </a-form-item>

        <a-form-item>
          <a-button type="primary" html-type="submit">
            <template #icon>
              <icon-search />
            </template>
            搜索
          </a-button>
        </a-form-item>

        <a-form-item>
          <a-button @click="handleResetFilters">
            重置
          </a-button>
        </a-form-item>
      </a-form>
    </a-card>

    <!-- 数据表格 -->
    <DataTable
      :data="licenseList"
      :columns="columns"
      :loading="loading"
      :pagination="{
        current: pagination.page,
        pageSize: pagination.limit,
        total: pagination.total
      }"
      :row-selection="rowSelection"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
      @selection-change="handleSelectionChange"
      @sorter-change="handleSorterChange"
    >
      <template #toolbar-left>
        <a-space>
          <a-button 
            type="outline" 
            status="danger"
            :disabled="selectedRowKeys.length === 0"
            @click="handleBatchDisable"
          >
            <template #icon>
              <icon-minus-circle />
            </template>
            批量禁用
          </a-button>
          
          <a-button 
            type="outline"
            :disabled="selectedRowKeys.length === 0"
            @click="handleBatchExport"
          >
            <template #icon>
              <icon-download />
            </template>
            导出选中
          </a-button>
        </a-space>
      </template>

      <!-- 许可证密钥列 -->
      <template #license_key="{ record }">
        <div class="license-key-cell">
          <a-typography-text copyable>{{ record.license_key }}</a-typography-text>
        </div>
      </template>

      <!-- 状态列 -->
      <template #status="{ record }">
        <StatusTag :status="record.status" type="license" />
      </template>

      <!-- 过期时间列 -->
      <template #expires_at="{ record }">
        <div class="expires-cell">
          <span v-if="record.expires_at">
            {{ formatDate(record.expires_at) }}
          </span>
          <a-tag v-else color="blue">永久</a-tag>
          
          <a-tag 
            v-if="record.expires_at && isExpiringSoon(record.expires_at)"
            color="orange"
            size="small"
            class="expiring-tag"
          >
            即将过期
          </a-tag>
        </div>
      </template>

      <!-- 设备数量列 -->
      <template #device_count="{ record }">
        <div class="device-count-cell">
          <span>{{ record.device_bindings?.length || 0 }} / {{ record.max_devices }}</span>
          <a-progress
            :percent="getDeviceUsagePercent(record)"
            :size="'mini'"
            :show-text="false"
            :color="getDeviceUsageColor(record)"
            class="device-progress"
          />
        </div>
      </template>

      <!-- 操作列 -->
      <template #actions="{ record }">
        <a-space>
          <a-button 
            type="text" 
            size="small"
            @click="handleView(record)"
          >
            查看
          </a-button>
          
          <a-button 
            type="text" 
            size="small"
            @click="handleEdit(record)"
          >
            编辑
          </a-button>
          
          <a-dropdown>
            <a-button type="text" size="small">
              更多
              <icon-down />
            </a-button>
            <template #content>
              <a-doption @click="handleToggleStatus(record)">
                {{ record.status === 'active' ? '禁用' : '启用' }}
              </a-doption>
              <a-doption @click="handleCopyKey(record)">
                复制密钥
              </a-doption>
              <a-doption @click="handleExportSingle(record)">
                导出数据
              </a-doption>
              <a-doption 
                class="danger-option"
                @click="handleDelete(record)"
              >
                删除
              </a-doption>
            </template>
          </a-dropdown>
        </a-space>
      </template>
    </DataTable>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Message, Modal } from '@arco-design/web-vue'
import type { TableColumnData } from '@arco-design/web-vue'
import PageHeader from '@/components/common/PageHeader.vue'
import DataTable from '@/components/common/DataTable.vue'
import StatusTag from '@/components/common/StatusTag.vue'
import type { License } from '@/types/license'
import { licenseApi } from '@/api/license'
import dayjs from 'dayjs'

const router = useRouter()

// 数据状态
const loading = ref(false)
const licenseList = ref<License[]>([])
const selectedRowKeys = ref<(string | number)[]>([])

// 分页状态
const pagination = reactive({
  page: 1,
  limit: 20,
  total: 0
})

// 筛选器状态
const filters = reactive({
  search: '',
  status: '',
  product_name: '',
  dateRange: [],
  sort_by: 'created_at',
  sort_order: 'desc' as 'asc' | 'desc'
})

// 产品列表（用于筛选）
const productList = ref<string[]>([
  'Demo Software',
  'Pro Software',
  'Enterprise Suite'
])

// 表格列配置
const columns: TableColumnData[] = [
  {
    title: '许可证密钥',
    dataIndex: 'license_key',
    slotName: 'license_key',
    width: 200,
    ellipsis: true,
    tooltip: true
  },
  {
    title: '产品名称',
    dataIndex: 'product_name',
    width: 150,
    sortable: {
      sortDirections: ['ascend', 'descend']
    }
  },
  {
    title: '客户名称',
    dataIndex: 'customer_name',
    width: 120,
    ellipsis: true,
    tooltip: true
  },
  {
    title: '状态',
    dataIndex: 'status',
    slotName: 'status',
    width: 100,
    filterable: {
      filters: [
        { text: '有效', value: 'active' },
        { text: '已过期', value: 'expired' },
        { text: '已禁用', value: 'disabled' },
        { text: '已暂停', value: 'suspended' }
      ]
    }
  },
  {
    title: '设备使用',
    dataIndex: 'device_count',
    slotName: 'device_count',
    width: 120
  },
  {
    title: '过期时间',
    dataIndex: 'expires_at',
    slotName: 'expires_at',
    width: 150,
    sortable: {
      sortDirections: ['ascend', 'descend']
    }
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    width: 150,
    render: ({ record }) => formatDate(record.created_at),
    sortable: {
      sortDirections: ['ascend', 'descend']
    }
  },
  {
    title: '操作',
    slotName: 'actions',
    width: 150,
    fixed: 'right'
  }
]

// 行选择配置
const rowSelection = computed(() => ({
  type: 'checkbox' as const,
  showCheckedAll: true,
  onlyCurrent: false
}))

// 格式化日期
const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm')
}

// 检查是否即将过期
const isExpiringSoon = (expiresAt: string) => {
  const expireDate = dayjs(expiresAt)
  const now = dayjs()
  const daysUntilExpire = expireDate.diff(now, 'day')
  return daysUntilExpire <= 30 && daysUntilExpire > 0
}

// 获取设备使用百分比
const getDeviceUsagePercent = (record: License) => {
  const used = record.device_bindings?.length || 0
  return Math.round((used / record.max_devices) * 100)
}

// 获取设备使用颜色
const getDeviceUsageColor = (record: License) => {
  const percent = getDeviceUsagePercent(record)
  if (percent >= 90) return '#ff4d4f'
  if (percent >= 70) return '#faad14'
  return '#52c41a'
}

// 加载许可证列表
const loadLicenseList = async () => {
  try {
    loading.value = true
    
    const params = {
      page: pagination.page,
      limit: pagination.limit,
      search: filters.search || undefined,
      status: filters.status || undefined,
      product_name: filters.product_name || undefined,
      sort_by: filters.sort_by,
      sort_order: filters.sort_order
    }

    // TODO: 调用实际API
    // const response = await licenseApi.getList(params)
    
    // 模拟数据
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const mockData: License[] = [
      {
        id: 1,
        license_key: 'TEST-DEMO-LICENSE-001',
        product_name: 'Demo Software',
        customer_name: 'Test Customer 1',
        customer_email: '<EMAIL>',
        max_devices: 3,
        features: ['basic', 'advanced'],
        expires_at: dayjs().add(30, 'day').toISOString(),
        status: 'active',
        created_by: 1,
        created_at: dayjs().subtract(10, 'day').toISOString(),
        updated_at: dayjs().subtract(1, 'day').toISOString(),
        device_bindings: [
          {
            id: 1,
            license_key: 'TEST-DEMO-LICENSE-001',
            device_id: 'device-001',
            device_name: 'PC-001',
            first_seen_at: dayjs().subtract(5, 'day').toISOString(),
            last_seen_at: dayjs().subtract(1, 'hour').toISOString(),
            status: 'active',
            created_at: dayjs().subtract(5, 'day').toISOString(),
            updated_at: dayjs().subtract(1, 'hour').toISOString()
          }
        ]
      },
      {
        id: 2,
        license_key: 'TEST-DEMO-LICENSE-002',
        product_name: 'Pro Software',
        customer_name: 'Test Customer 2',
        customer_email: '<EMAIL>',
        max_devices: 5,
        features: ['basic', 'advanced', 'premium'],
        expires_at: dayjs().add(90, 'day').toISOString(),
        status: 'active',
        created_by: 1,
        created_at: dayjs().subtract(20, 'day').toISOString(),
        updated_at: dayjs().subtract(2, 'day').toISOString(),
        device_bindings: []
      }
    ]
    
    licenseList.value = mockData
    pagination.total = 2
    
  } catch (error) {
    console.error('Failed to load license list:', error)
    Message.error('加载许可证列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索处理
const handleSearch = () => {
  pagination.page = 1
  loadLicenseList()
}

// 重置筛选器
const handleResetFilters = () => {
  filters.search = ''
  filters.status = ''
  filters.product_name = ''
  filters.dateRange = []
  handleSearch()
}

// 刷新数据
const handleRefresh = () => {
  loadLicenseList()
}

// 分页处理
const handlePageChange = (page: number) => {
  pagination.page = page
  loadLicenseList()
}

const handlePageSizeChange = (pageSize: number) => {
  pagination.limit = pageSize
  pagination.page = 1
  loadLicenseList()
}

// 选择处理
const handleSelectionChange = (selectedKeys: (string | number)[]) => {
  selectedRowKeys.value = selectedKeys
}

// 排序处理
const handleSorterChange = (dataIndex: string, direction: string) => {
  filters.sort_by = dataIndex
  filters.sort_order = direction === 'ascend' ? 'asc' : 'desc'
  loadLicenseList()
}

// 查看许可证
const handleView = (record: License) => {
  router.push(`/licenses/${record.id}`)
}

// 编辑许可证
const handleEdit = (record: License) => {
  router.push(`/licenses/${record.id}/edit`)
}

// 切换状态
const handleToggleStatus = async (record: License) => {
  try {
    const action = record.status === 'active' ? '禁用' : '启用'
    
    Modal.confirm({
      title: `确认${action}许可证？`,
      content: `您确定要${action}许可证 ${record.license_key} 吗？`,
      onOk: async () => {
        // TODO: 调用实际API
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        record.status = record.status === 'active' ? 'disabled' : 'active'
        Message.success(`许可证${action}成功`)
      }
    })
  } catch (error) {
    console.error('Toggle status error:', error)
    Message.error('操作失败')
  }
}

// 复制密钥
const handleCopyKey = async (record: License) => {
  try {
    await navigator.clipboard.writeText(record.license_key)
    Message.success('许可证密钥已复制到剪贴板')
  } catch (error) {
    console.error('Copy error:', error)
    Message.error('复制失败')
  }
}

// 删除许可证
const handleDelete = (record: License) => {
  Modal.confirm({
    title: '确认删除许可证？',
    content: `您确定要删除许可证 ${record.license_key} 吗？此操作不可撤销。`,
    okButtonProps: { status: 'danger' },
    onOk: async () => {
      try {
        // TODO: 调用实际API
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        const index = licenseList.value.findIndex(item => item.id === record.id)
        if (index > -1) {
          licenseList.value.splice(index, 1)
          pagination.total--
        }
        
        Message.success('许可证删除成功')
      } catch (error) {
        console.error('Delete error:', error)
        Message.error('删除失败')
      }
    }
  })
}

// 批量禁用
const handleBatchDisable = () => {
  Modal.confirm({
    title: '确认批量禁用？',
    content: `您确定要禁用选中的 ${selectedRowKeys.value.length} 个许可证吗？`,
    onOk: async () => {
      try {
        // TODO: 调用实际API
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        Message.success(`成功禁用 ${selectedRowKeys.value.length} 个许可证`)
        selectedRowKeys.value = []
        loadLicenseList()
      } catch (error) {
        console.error('Batch disable error:', error)
        Message.error('批量禁用失败')
      }
    }
  })
}

// 导出单个
const handleExportSingle = async (record: License) => {
  try {
    // TODO: 调用实际API
    await new Promise(resolve => setTimeout(resolve, 1000))
    Message.success('导出成功')
  } catch (error) {
    console.error('Export error:', error)
    Message.error('导出失败')
  }
}

// 批量导出
const handleBatchExport = async () => {
  try {
    // TODO: 调用实际API
    await new Promise(resolve => setTimeout(resolve, 1000))
    Message.success(`成功导出 ${selectedRowKeys.value.length} 个许可证`)
  } catch (error) {
    console.error('Batch export error:', error)
    Message.error('批量导出失败')
  }
}

onMounted(() => {
  loadLicenseList()
})
</script>

<style scoped>
.license-list-page {
  padding: 24px;
}

.filter-card {
  margin-bottom: 24px;
}

.license-key-cell {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
}

.expires-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.expiring-tag {
  align-self: flex-start;
}

.device-count-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.device-progress {
  width: 100%;
}

.danger-option {
  color: #ff4d4f;
}

.danger-option:hover {
  background-color: #fff2f0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .license-list-page {
    padding: 16px;
  }
  
  .filter-card .arco-form {
    flex-direction: column;
  }
  
  .filter-card .arco-form-item {
    margin-right: 0;
    margin-bottom: 16px;
  }
}
</style>