import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'

const routes: RouteRecordRaw[] = [
  {
    path: '/',
    redirect: '/login'
  },
  {
    path: '/test',
    name: 'Test',
    component: () => import('@/views/Test.vue')
  },
  {
    path: '/login',
    name: 'Login',
    component: () => import('@/views/auth/UnifiedLogin.vue'),
    meta: {
      title: '登录'
    }
  },
  {
    path: '/simple-login',
    name: 'SimpleLogin',
    component: () => import('@/views/SimpleLogin.vue'),
    meta: {
      title: '简单登录'
    }
  },
  {
    path: '/dashboard',
    name: 'Dashboard',
    component: () => import('@/views/UnifiedDashboard.vue'),
    meta: {
      title: '仪表板'
    }
  },
  {
    path: '/licenses',
    name: 'Licenses',
    component: () => import('@/views/license/UnifiedLicenseList.vue'),
    meta: {
      title: '许可证管理'
    }
  },
  {
    path: '/licenses/create',
    name: 'CreateLicense',
    component: () => import('@/views/license/CreateLicense.vue'),
    meta: {
      title: '创建许可证'
    }
  },
  {
    path: '/licenses/:id',
    name: 'LicenseDetail',
    component: () => import('@/views/license/LicenseDetail.vue'),
    meta: {
      title: '许可证详情'
    }
  },
  {
    path: '/licenses/:id/edit',
    name: 'EditLicense',
    component: () => import('@/views/license/EditLicense.vue'),
    meta: {
      title: '编辑许可证'
    }
  },
  {
    path: '/profile',
    name: 'Profile',
    component: () => import('@/views/auth/Profile.vue'),
    meta: {
      title: '个人资料'
    }
  },
  {
    path: '/users',
    name: 'Users',
    component: () => import('@/views/user/UserList.vue'),
    meta: {
      title: '用户管理'
    }
  },
  {
    path: '/reports',
    name: 'Reports',
    component: () => import('@/views/report/ReportList.vue'),
    meta: {
      title: '统计报告'
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'NotFound',
    component: () => import('@/views/error/NotFound.vue'),
    meta: {
      title: '页面不存在'
    }
  }
]

const router = createRouter({
  history: createWebHistory(),
  routes
})

// 简化的路由守卫
router.beforeEach((to, from, next) => {
  console.log('路由守卫:', { to: to.path, from: from.path })
  
  // 设置页面标题
  if (to.meta?.title) {
    document.title = `${to.meta.title} - 软件网络授权系统`
  }
  
  // 检查登录状态
  const isLoggedIn = localStorage.getItem('isLoggedIn')
  console.log('登录状态:', isLoggedIn)
  
  const publicPages = ['/login', '/simple-login', '/test']
  const authRequired = !publicPages.includes(to.path)
  
  console.log('需要认证:', authRequired)
  
  if (authRequired && !isLoggedIn) {
    console.log('未登录，跳转到登录页')
    next('/login')
  } else if ((to.path === '/login' || to.path === '/simple-login') && isLoggedIn) {
    console.log('已登录，跳转到仪表板')
    next('/dashboard')
  } else {
    console.log('正常通过')
    next()
  }
})

export default router