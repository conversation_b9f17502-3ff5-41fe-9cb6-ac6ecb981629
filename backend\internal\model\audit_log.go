package model

import (
	"time"
)

// AuditLog 审计日志模型
type AuditLog struct {
	ID          uint      `gorm:"primaryKey" json:"id"`
	UserID      uint      `gorm:"index" json:"user_id"`
	Username    string    `gorm:"size:100;index" json:"username"`
	Action      string    `gorm:"size:100;not null;index" json:"action"`
	Resource    string    `gorm:"size:100;index" json:"resource"`
	ResourceID  string    `gorm:"size:100;index" json:"resource_id"`
	Method      string    `gorm:"size:10" json:"method"`
	Path        string    `gorm:"size:500" json:"path"`
	ClientIP    string    `gorm:"size:45;index" json:"client_ip"`
	UserAgent   string    `gorm:"size:500" json:"user_agent"`
	StatusCode  int       `gorm:"index" json:"status_code"`
	Success     bool      `gorm:"index" json:"success"`
	Message     string    `gorm:"size:1000" json:"message"`
	RequestData string    `gorm:"type:text" json:"request_data"`
	Duration    int64     `json:"duration"` // 请求处理时间（毫秒）
	CreatedAt   time.Time `gorm:"index" json:"created_at"`
}

// TableName 指定表名
func (AuditLog) TableName() string {
	return "audit_logs"
}