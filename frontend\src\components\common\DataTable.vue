<template>
  <div class="data-table">
    <!-- 表格工具栏 -->
    <div v-if="showToolbar" class="table-toolbar">
      <div class="toolbar-left">
        <slot name="toolbar-left"></slot>
      </div>
      <div class="toolbar-right">
        <a-input-search
          v-if="searchable"
          v-model="searchValue"
          :placeholder="searchPlaceholder"
          style="width: 250px"
          @search="handleSearch"
          @clear="handleSearch"
          allow-clear
        />
        <slot name="toolbar-right"></slot>
      </div>
    </div>

    <!-- 数据表格 -->
    <a-table
      :data="data"
      :columns="columns"
      :loading="loading"
      :pagination="paginationConfig"
      :row-selection="rowSelection"
      :scroll="scroll"
      :size="size"
      @page-change="handlePageChange"
      @page-size-change="handlePageSizeChange"
      @selection-change="handleSelectionChange"
      @sorter-change="handleSorterChange"
      v-bind="$attrs"
    >
      <!-- 动态插槽 -->
      <template v-for="(_, name) in $slots" :key="name" #[name]="slotData">
        <slot :name="name" v-bind="slotData"></slot>
      </template>
    </a-table>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch } from 'vue'
import type { TableColumnData, TableRowSelection } from '@arco-design/web-vue'

interface Props {
  data: any[]
  columns: TableColumnData[]
  loading?: boolean
  pagination?: {
    current: number
    pageSize: number
    total: number
    showTotal?: boolean
    showJumper?: boolean
    showPageSize?: boolean
  }
  rowSelection?: TableRowSelection
  scroll?: { x?: number | string; y?: number | string }
  size?: 'mini' | 'small' | 'medium' | 'large'
  searchable?: boolean
  searchPlaceholder?: string
  showToolbar?: boolean
}

interface Emits {
  (e: 'page-change', page: number): void
  (e: 'page-size-change', pageSize: number): void
  (e: 'selection-change', selectedRowKeys: (string | number)[]): void
  (e: 'search', keyword: string): void
  (e: 'sorter-change', dataIndex: string, direction: string): void
}

const props = withDefaults(defineProps<Props>(), {
  loading: false,
  size: 'medium',
  searchable: true,
  searchPlaceholder: '请输入搜索关键词',
  showToolbar: true
})

const emit = defineEmits<Emits>()

// 搜索值
const searchValue = ref('')

// 分页配置
const paginationConfig = computed(() => {
  if (!props.pagination) return false
  
  return {
    current: props.pagination.current,
    pageSize: props.pagination.pageSize,
    total: props.pagination.total,
    showTotal: props.pagination.showTotal ?? true,
    showJumper: props.pagination.showJumper ?? true,
    showPageSize: props.pagination.showPageSize ?? true,
    pageSizeOptions: ['10', '20', '50', '100']
  }
})

// 页码变化处理
const handlePageChange = (page: number) => {
  emit('page-change', page)
}

// 页面大小变化处理
const handlePageSizeChange = (pageSize: number) => {
  emit('page-size-change', pageSize)
}

// 选择变化处理
const handleSelectionChange = (selectedRowKeys: (string | number)[]) => {
  emit('selection-change', selectedRowKeys)
}

// 搜索处理
const handleSearch = (keyword?: string) => {
  emit('search', keyword || searchValue.value)
}

// 排序变化处理
const handleSorterChange = (dataIndex: string, direction: string) => {
  emit('sorter-change', dataIndex, direction)
}

// 监听搜索值变化（防抖）
let searchTimer: NodeJS.Timeout
watch(searchValue, (newValue) => {
  clearTimeout(searchTimer)
  searchTimer = setTimeout(() => {
    handleSearch(newValue)
  }, 500)
})
</script>

<style scoped>
.data-table {
  background: #fff;
  border-radius: 8px;
  overflow: hidden;
}

.table-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
}

.toolbar-left {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toolbar-right {
  display: flex;
  align-items: center;
  gap: 12px;
}
</style>