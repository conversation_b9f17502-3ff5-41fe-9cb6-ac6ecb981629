<template>
  <MainLayout>
    <div class="license-list-container">
      <!-- 页面头部 -->
      <div class="page-header">
        <div class="header-content">
          <h1>许可证管理</h1>
          <p>管理和监控所有软件许可证</p>
        </div>
        <div class="header-actions">
          <button @click="$router.push('/licenses/create')" class="primary-btn">
            ➕ 创建许可证
          </button>
          <button @click="handleRefresh" class="secondary-btn" :disabled="loading">
            {{ loading ? '🔄' : '↻' }} 刷新
          </button>
        </div>
      </div>

      <!-- 搜索和筛选 -->
      <div class="search-card">
        <div class="search-form">
          <div class="form-row">
            <div class="form-group">
              <label>搜索</label>
              <input
                v-model="searchForm.keyword"
                type="text"
                placeholder="许可证密钥或客户名称"
                @keyup.enter="handleSearch"
              />
            </div>
            <div class="form-group">
              <label>状态</label>
              <select v-model="searchForm.status" @change="handleSearch">
                <option value="">全部状态</option>
                <option value="active">有效</option>
                <option value="expired">已过期</option>
                <option value="disabled">已禁用</option>
              </select>
            </div>
            <div class="form-group">
              <label>产品</label>
              <select v-model="searchForm.product" @change="handleSearch">
                <option value="">全部产品</option>
                <option value="basic">基础版</option>
                <option value="professional">专业版</option>
                <option value="enterprise">企业版</option>
              </select>
            </div>
            <div class="form-group">
              <label>操作</label>
              <div class="button-group">
                <button @click="handleSearch" class="search-btn">🔍 搜索</button>
                <button @click="handleReset" class="reset-btn">🔄 重置</button>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 统计卡片 -->
      <div class="stats-row">
        <div class="stat-card">
          <div class="stat-icon">📄</div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.total }}</div>
            <div class="stat-label">总许可证</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">✅</div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.active }}</div>
            <div class="stat-label">有效许可证</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">⚠️</div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.expiring }}</div>
            <div class="stat-label">即将过期</div>
          </div>
        </div>
        <div class="stat-card">
          <div class="stat-icon">❌</div>
          <div class="stat-content">
            <div class="stat-value">{{ stats.expired }}</div>
            <div class="stat-label">已过期</div>
          </div>
        </div>
      </div>

      <!-- 许可证列表 -->
      <div class="table-card">
        <div class="table-header">
          <h3>许可证列表</h3>
          <div class="table-actions">
            <button @click="handleExport" class="export-btn">📊 导出</button>
          </div>
        </div>

        <div class="table-container" v-if="!loading">
          <div class="license-grid">
            <div v-for="license in licenses" :key="license.id" class="license-card">
              <div class="license-header">
                <div class="license-key">{{ license.licenseKey }}</div>
                <div class="license-status" :class="license.status">
                  {{ getStatusText(license.status) }}
                </div>
              </div>
              
              <div class="license-body">
                <div class="license-info">
                  <div class="info-item">
                    <span class="info-label">客户</span>
                    <span class="info-value">{{ license.customerName }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">产品</span>
                    <span class="info-value">{{ license.productName }}</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">设备限制</span>
                    <span class="info-value">{{ license.maxDevices }} 台</span>
                  </div>
                  <div class="info-item">
                    <span class="info-label">过期时间</span>
                    <span class="info-value">{{ formatDate(license.expiresAt) }}</span>
                  </div>
                </div>
              </div>
              
              <div class="license-footer">
                <div class="license-date">
                  创建于 {{ formatDate(license.createdAt) }}
                </div>
                <div class="license-actions">
                  <button @click="$router.push(`/licenses/${license.id}`)" class="action-btn view">
                    👁️ 查看
                  </button>
                  <button @click="$router.push(`/licenses/${license.id}/edit`)" class="action-btn edit">
                    ✏️ 编辑
                  </button>
                  <button @click="handleDelete(license.id)" class="action-btn delete">
                    🗑️ 删除
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 加载状态 -->
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner">🔄</div>
          <div class="loading-text">加载中...</div>
        </div>

        <!-- 空状态 -->
        <div v-if="!loading && licenses.length === 0" class="empty-container">
          <div class="empty-icon">📄</div>
          <div class="empty-text">暂无许可证数据</div>
          <button @click="$router.push('/licenses/create')" class="empty-action">
            创建第一个许可证
          </button>
        </div>

        <!-- 分页 -->
        <div v-if="!loading && licenses.length > 0" class="pagination">
          <div class="pagination-info">
            显示 {{ (pagination.current - 1) * pagination.pageSize + 1 }} - 
            {{ Math.min(pagination.current * pagination.pageSize, pagination.total) }} 
            条，共 {{ pagination.total }} 条
          </div>
          <div class="pagination-controls">
            <button 
              @click="handlePageChange(pagination.current - 1)"
              :disabled="pagination.current <= 1"
              class="page-btn"
            >
              ← 上一页
            </button>
            <span class="page-info">
              第 {{ pagination.current }} / {{ Math.ceil(pagination.total / pagination.pageSize) }} 页
            </span>
            <button 
              @click="handlePageChange(pagination.current + 1)"
              :disabled="pagination.current >= Math.ceil(pagination.total / pagination.pageSize)"
              class="page-btn"
            >
              下一页 →
            </button>
          </div>
        </div>
      </div>
    </div>
  </MainLayout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import MainLayout from '@/layouts/MainLayout.vue'

// 搜索表单
const searchForm = reactive({
  keyword: '',
  status: '',
  product: ''
})

// 数据状态
const loading = ref(false)
const licenses = ref([])
const pagination = reactive({
  current: 1,
  pageSize: 12,
  total: 0
})

// 统计数据
const stats = reactive({
  total: 0,
  active: 0,
  expiring: 0,
  expired: 0
})

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    active: '有效',
    expired: '已过期',
    disabled: '已禁用',
    expiring: '即将过期'
  }
  return statusMap[status] || '未知'
}

// 格式化日期
const formatDate = (dateString: string) => {
  return new Date(dateString).toLocaleDateString('zh-CN')
}

// 加载数据
const loadData = async () => {
  loading.value = true
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 模拟数据
    const mockLicenses = [
      {
        id: 1,
        licenseKey: 'DEMO-LICENSE-001',
        customerName: 'ABC公司',
        customerEmail: '<EMAIL>',
        productName: '企业版',
        status: 'active',
        maxDevices: 10,
        createdAt: '2024-01-15T10:30:00',
        expiresAt: '2024-12-31T23:59:59'
      },
      {
        id: 2,
        licenseKey: 'DEMO-LICENSE-002',
        customerName: 'XYZ科技',
        customerEmail: '<EMAIL>',
        productName: '专业版',
        status: 'active',
        maxDevices: 5,
        createdAt: '2024-01-20T14:15:00',
        expiresAt: '2024-06-30T23:59:59'
      },
      {
        id: 3,
        licenseKey: 'DEMO-LICENSE-003',
        customerName: '测试公司',
        customerEmail: '<EMAIL>',
        productName: '基础版',
        status: 'expired',
        maxDevices: 1,
        createdAt: '2023-12-01T09:00:00',
        expiresAt: '2024-01-01T23:59:59'
      },
      {
        id: 4,
        licenseKey: 'DEMO-LICENSE-004',
        customerName: '创新科技',
        customerEmail: '<EMAIL>',
        productName: '企业版',
        status: 'expiring',
        maxDevices: 20,
        createdAt: '2024-01-10T08:00:00',
        expiresAt: '2024-02-15T23:59:59'
      },
      {
        id: 5,
        licenseKey: 'DEMO-LICENSE-005',
        customerName: '未来公司',
        customerEmail: '<EMAIL>',
        productName: '专业版',
        status: 'active',
        maxDevices: 8,
        createdAt: '2024-01-25T16:30:00',
        expiresAt: '2024-07-25T23:59:59'
      },
      {
        id: 6,
        licenseKey: 'DEMO-LICENSE-006',
        customerName: '智能系统',
        customerEmail: '<EMAIL>',
        productName: '基础版',
        status: 'disabled',
        maxDevices: 3,
        createdAt: '2024-01-05T11:20:00',
        expiresAt: '2024-05-05T23:59:59'
      }
    ]
    
    licenses.value = mockLicenses
    pagination.total = mockLicenses.length
    
    // 更新统计数据
    stats.total = mockLicenses.length
    stats.active = mockLicenses.filter(l => l.status === 'active').length
    stats.expiring = mockLicenses.filter(l => l.status === 'expiring').length
    stats.expired = mockLicenses.filter(l => l.status === 'expired').length
    
  } catch (error) {
    console.error('加载数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  pagination.current = 1
  loadData()
}

// 重置
const handleReset = () => {
  Object.assign(searchForm, {
    keyword: '',
    status: '',
    product: ''
  })
  handleSearch()
}

// 刷新
const handleRefresh = () => {
  loadData()
}

// 导出
const handleExport = () => {
  alert('导出功能开发中...')
}

// 分页
const handlePageChange = (page: number) => {
  pagination.current = page
  loadData()
}

// 删除
const handleDelete = async (id: number) => {
  if (confirm('确定要删除这个许可证吗？')) {
    try {
      // 模拟删除API调用
      await new Promise(resolve => setTimeout(resolve, 500))
      alert('删除成功')
      loadData()
    } catch (error) {
      alert('删除失败')
    }
  }
}

onMounted(() => {
  loadData()
})
</script>

<style scoped>
.license-list-container {
  max-width: 1400px;
  margin: 0 auto;
}

/* 页面头部 */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding: 24px;
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.header-content h1 {
  margin: 0 0 4px 0;
  font-size: 28px;
  color: #262626;
  font-weight: 600;
}

.header-content p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.header-actions {
  display: flex;
  gap: 12px;
}

.primary-btn {
  padding: 12px 24px;
  background: linear-gradient(135deg, #1890ff, #722ed1);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.2s;
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.3);
}

.primary-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(24, 144, 255, 0.4);
}

.secondary-btn {
  padding: 12px 24px;
  background: white;
  color: #666;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.2s;
}

.secondary-btn:hover:not(:disabled) {
  border-color: #1890ff;
  color: #1890ff;
}

.secondary-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 搜索卡片 */
.search-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.search-form {
  width: 100%;
}

.form-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr 1fr;
  gap: 20px;
  align-items: end;
}

.form-group {
  display: flex;
  flex-direction: column;
}

.form-group label {
  margin-bottom: 8px;
  font-weight: 500;
  color: #262626;
  font-size: 14px;
}

.form-group input,
.form-group select {
  padding: 12px 16px;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.2s;
}

.form-group input:focus,
.form-group select:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

.button-group {
  display: flex;
  gap: 8px;
}

.search-btn {
  padding: 12px 20px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.search-btn:hover {
  background: #40a9ff;
}

.reset-btn {
  padding: 12px 20px;
  background: #f5f5f5;
  color: #666;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.reset-btn:hover {
  background: #e6f7ff;
  color: #1890ff;
}

/* 统计卡片 */
.stats-row {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stat-icon {
  width: 56px;
  height: 56px;
  border-radius: 16px;
  background: linear-gradient(135deg, #f0f9ff, #e6f7ff);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.stat-content {
  flex: 1;
}

.stat-value {
  font-size: 32px;
  font-weight: bold;
  color: #262626;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 14px;
  color: #8c8c8c;
}

/* 表格卡片 */
.table-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
}

.table-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.table-header h3 {
  margin: 0;
  font-size: 20px;
  color: #262626;
  font-weight: 600;
}

.export-btn {
  padding: 8px 16px;
  background: #f5f5f5;
  color: #666;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.export-btn:hover {
  background: #e6f7ff;
  color: #1890ff;
}

/* 许可证网格 */
.license-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(400px, 1fr));
  gap: 20px;
}

.license-card {
  border: 1px solid #f0f0f0;
  border-radius: 12px;
  padding: 20px;
  transition: all 0.2s;
  background: #fafafa;
}

.license-card:hover {
  border-color: #1890ff;
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.1);
  transform: translateY(-2px);
}

.license-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.license-key {
  font-size: 16px;
  font-weight: 600;
  color: #262626;
  font-family: 'Courier New', monospace;
}

.license-status {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.license-status.active {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.license-status.expired {
  background: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
}

.license-status.disabled {
  background: rgba(140, 140, 140, 0.1);
  color: #8c8c8c;
}

.license-status.expiring {
  background: rgba(250, 173, 20, 0.1);
  color: #faad14;
}

.license-body {
  margin-bottom: 16px;
}

.license-info {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 12px;
}

.info-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-label {
  font-size: 12px;
  color: #8c8c8c;
}

.info-value {
  font-size: 14px;
  color: #262626;
  font-weight: 500;
}

.license-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.license-date {
  font-size: 12px;
  color: #8c8c8c;
}

.license-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 6px 12px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s;
}

.action-btn.view {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

.action-btn.edit {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.action-btn.delete {
  background: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
}

.action-btn:hover {
  transform: translateY(-1px);
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.loading-spinner {
  font-size: 48px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 16px;
  color: #8c8c8c;
  font-size: 16px;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
}

.empty-icon {
  font-size: 64px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-text {
  color: #8c8c8c;
  font-size: 16px;
  margin-bottom: 24px;
}

.empty-action {
  padding: 12px 24px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.2s;
}

.empty-action:hover {
  background: #40a9ff;
}

/* 分页 */
.pagination {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 24px;
  padding-top: 24px;
  border-top: 1px solid #f0f0f0;
}

.pagination-info {
  color: #8c8c8c;
  font-size: 14px;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 16px;
}

.page-btn {
  padding: 8px 16px;
  background: white;
  border: 1px solid #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.page-btn:hover:not(:disabled) {
  border-color: #1890ff;
  color: #1890ff;
}

.page-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.page-info {
  color: #666;
  font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .stats-row {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .license-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: flex-end;
  }
  
  .form-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .stats-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .license-info {
    grid-template-columns: 1fr;
  }
  
  .license-footer {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .pagination {
    flex-direction: column;
    gap: 16px;
  }
}
</style>