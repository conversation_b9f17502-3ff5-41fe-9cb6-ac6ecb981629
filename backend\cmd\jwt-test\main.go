package main

import (
	"fmt"
	"log"
	"time"
	"software-auth/internal/config"
	"software-auth/internal/auth"
	"software-auth/internal/model"
	"software-auth/internal/cache"
)

func main() {
	fmt.Println("JWT Authentication Test Tool")
	fmt.Println("============================")

	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化缓存（用于令牌黑名单）
	fmt.Println("Initializing cache for token blacklist...")
	if err := cache.Initialize(&cfg.Redis); err != nil {
		fmt.Printf("⚠️  Warning: Redis not available: %v\n", err)
		fmt.Println("Token revocation will not work without Redis")
	} else {
		fmt.Println("✅ Cache initialized successfully")
	}

	// 创建JWT服务
	jwtService := auth.NewJWTService(&cfg.JWT)
	passwordService := auth.NewPasswordService()

	// 创建测试用户
	testUser := &model.User{
		ID:       1,
		Username: "admin",
		Role:     "admin",
		Status:   "active",
	}

	fmt.Printf("\nTest User: %s (ID: %d, Role: %s)\n", testUser.Username, testUser.ID, testUser.Role)

	// 测试密码服务
	fmt.Println("\n1. Testing Password Service...")
	testPassword := "AdminPass123!"
	hashedPassword, err := passwordService.HashPassword(testPassword)
	if err != nil {
		log.Fatalf("Failed to hash password: %v", err)
	}
	fmt.Printf("✅ Password hashed successfully\n")

	err = passwordService.VerifyPassword(hashedPassword, testPassword)
	if err != nil {
		log.Fatalf("Failed to verify password: %v", err)
	}
	fmt.Printf("✅ Password verification successful\n")

	// 测试密码强度检查
	weakPassword := "123"
	err = passwordService.IsStrongPassword(weakPassword)
	if err != nil {
		fmt.Printf("✅ Weak password correctly rejected: %v\n", err)
	} else {
		fmt.Printf("❌ Weak password should be rejected\n")
	}

	// 生成令牌
	fmt.Println("\n2. Generating JWT Tokens...")
	tokens, err := jwtService.GenerateToken(testUser)
	if err != nil {
		log.Fatalf("Failed to generate token: %v", err)
	}

	fmt.Printf("✅ Tokens generated successfully\n")
	fmt.Printf("   Token Type: %s\n", tokens.TokenType)
	fmt.Printf("   Expires In: %d seconds\n", tokens.ExpiresIn)
	fmt.Printf("   Access Token: %s...\n", tokens.AccessToken[:50])
	fmt.Printf("   Refresh Token: %s...\n", tokens.RefreshToken[:50])

	// 验证令牌
	fmt.Println("\n3. Validating Access Token...")
	claims, err := jwtService.ValidateToken(tokens.AccessToken)
	if err != nil {
		log.Fatalf("Failed to validate token: %v", err)
	}

	fmt.Printf("✅ Token validation successful\n")
	fmt.Printf("   User ID: %d\n", claims.UserID)
	fmt.Printf("   Username: %s\n", claims.Username)
	fmt.Printf("   Role: %s\n", claims.Role)
	fmt.Printf("   Token ID: %s\n", claims.TokenID)
	fmt.Printf("   Issuer: %s\n", claims.Issuer)
	fmt.Printf("   Expires At: %v\n", claims.ExpiresAt.Time)

	// 检查令牌撤销状态（应该是false）
	fmt.Println("\n4. Checking Token Revocation Status...")
	isRevoked := jwtService.IsTokenRevoked(claims.TokenID)
	if !isRevoked {
		fmt.Printf("✅ Token is not revoked (as expected)\n")
	} else {
		fmt.Printf("❌ Token should not be revoked yet\n")
	}

	// 刷新令牌
	fmt.Println("\n5. Refreshing Token...")
	newTokens, err := jwtService.RefreshToken(tokens.RefreshToken)
	if err != nil {
		log.Fatalf("Failed to refresh token: %v", err)
	}

	fmt.Printf("✅ Token refresh successful\n")
	fmt.Printf("   New Access Token: %s...\n", newTokens.AccessToken[:50])
	fmt.Printf("   New Refresh Token: %s...\n", newTokens.RefreshToken[:50])

	// 验证新令牌
	fmt.Println("\n6. Validating New Token...")
	newClaims, err := jwtService.ValidateToken(newTokens.AccessToken)
	if err != nil {
		log.Fatalf("Failed to validate new token: %v", err)
	}

	fmt.Printf("✅ New token validation successful\n")
	fmt.Printf("   New Token ID: %s\n", newClaims.TokenID)

	// 撤销原始令牌
	fmt.Println("\n7. Revoking Original Token...")
	err = jwtService.RevokeToken(claims.TokenID)
	if err != nil {
		log.Fatalf("Failed to revoke token: %v", err)
	}

	fmt.Printf("✅ Token revocation successful\n")

	// 等待一下确保缓存更新
	time.Sleep(100 * time.Millisecond)

	// 检查撤销状态
	fmt.Println("\n8. Checking Revoked Token Status...")
	isRevoked = jwtService.IsTokenRevoked(claims.TokenID)
	if cache.IsHealthy() {
		if isRevoked {
			fmt.Printf("✅ Token is correctly marked as revoked\n")
		} else {
			fmt.Printf("❌ Token should be revoked\n")
		}
	} else {
		fmt.Printf("⚠️  Cannot check revocation status (Redis not available)\n")
	}

	// 尝试使用被撤销的令牌
	fmt.Println("\n9. Testing Revoked Token Validation...")
	if cache.IsHealthy() && isRevoked {
		fmt.Printf("✅ Revoked token would be rejected by middleware\n")
	} else {
		fmt.Printf("⚠️  Revocation check skipped (Redis not available)\n")
	}

	// 测试无效令牌
	fmt.Println("\n10. Testing Invalid Token...")
	invalidToken := "invalid.token.here"
	_, err = jwtService.ValidateToken(invalidToken)
	if err != nil {
		fmt.Printf("✅ Invalid token correctly rejected: %v\n", err)
	} else {
		fmt.Printf("❌ Invalid token should be rejected\n")
	}

	fmt.Println("\n🎉 All JWT tests completed!")
	
	if cache.IsHealthy() {
		fmt.Println("✅ Full functionality available (with Redis)")
	} else {
		fmt.Println("⚠️  Limited functionality (Redis not available)")
		fmt.Println("   - Token generation and validation work")
		fmt.Println("   - Token revocation requires Redis")
	}
}