package database

import (
	"log"
	"software-auth/internal/model"
)

// MigrationVersion 迁移版本结构
type MigrationVersion struct {
	Version   int    `gorm:"primarykey"`
	Name      string `gorm:"size:255;not null"`
	AppliedAt int64  `gorm:"not null"`
}

// TableName 指定表名
func (MigrationVersion) TableName() string {
	return "migration_versions"
}

// RunMigrations 运行数据库迁移
func RunMigrations() error {
	log.Println("Running database migrations...")

	// 确保迁移版本表存在
	if err := DB.AutoMigrate(&MigrationVersion{}); err != nil {
		return err
	}

	// 定义迁移列表
	migrations := []Migration{
		{
			Version: 1,
			Name:    "create_initial_tables",
			Up:      migration001Up,
			Down:    migration001Down,
		},
		{
			Version: 2,
			Name:    "add_indexes",
			Up:      migration002Up,
			Down:    migration002Down,
		},
		{
			Version: 3,
			Name:    "create_audit_logs_table",
			Up:      migration003Up,
			Down:    migration003Down,
		},
	}

	// 执行迁移
	for _, migration := range migrations {
		if err := runMigration(migration); err != nil {
			return err
		}
	}

	log.Println("Database migrations completed")
	return nil
}

// Migration 迁移结构
type Migration struct {
	Version int
	Name    string
	Up      func() error
	Down    func() error
}

// runMigration 执行单个迁移
func runMigration(migration Migration) error {
	// 检查迁移是否已应用
	var count int64
	DB.Model(&MigrationVersion{}).Where("version = ?", migration.Version).Count(&count)
	
	if count > 0 {
		log.Printf("Migration %d (%s) already applied, skipping", migration.Version, migration.Name)
		return nil
	}

	log.Printf("Applying migration %d: %s", migration.Version, migration.Name)

	// 执行迁移
	if err := migration.Up(); err != nil {
		return err
	}

	// 记录迁移版本
	version := &MigrationVersion{
		Version:   migration.Version,
		Name:      migration.Name,
		AppliedAt: DB.NowFunc().Unix(),
	}

	return DB.Create(version).Error
}

// migration001Up 创建初始表
func migration001Up() error {
	return DB.AutoMigrate(
		&model.User{},
		&model.License{},
		&model.DeviceBinding{},
		&model.VerificationLog{},
	)
}

// migration001Down 删除初始表
func migration001Down() error {
	return DB.Migrator().DropTable(
		&model.VerificationLog{},
		&model.DeviceBinding{},
		&model.License{},
		&model.User{},
	)
}

// migration002Up 添加索引
func migration002Up() error {
	// 为经常查询的字段添加索引
	if err := DB.Exec("CREATE INDEX IF NOT EXISTS idx_licenses_status ON licenses(status)").Error; err != nil {
		return err
	}
	
	if err := DB.Exec("CREATE INDEX IF NOT EXISTS idx_licenses_expires_at ON licenses(expires_at)").Error; err != nil {
		return err
	}
	
	if err := DB.Exec("CREATE INDEX IF NOT EXISTS idx_verification_logs_created_at ON verification_logs(created_at)").Error; err != nil {
		return err
	}
	
	if err := DB.Exec("CREATE INDEX IF NOT EXISTS idx_device_bindings_last_seen_at ON device_bindings(last_seen_at)").Error; err != nil {
		return err
	}

	return nil
}

// migration002Down 删除索引
func migration002Down() error {
	DB.Exec("DROP INDEX IF EXISTS idx_licenses_status")
	DB.Exec("DROP INDEX IF EXISTS idx_licenses_expires_at")
	DB.Exec("DROP INDEX IF EXISTS idx_verification_logs_created_at")
	DB.Exec("DROP INDEX IF EXISTS idx_device_bindings_last_seen_at")
	return nil
}

// migration003Up 创建审计日志表
func migration003Up() error {
	return DB.AutoMigrate(&model.AuditLog{})
}

// migration003Down 删除审计日志表
func migration003Down() error {
	return DB.Migrator().DropTable(&model.AuditLog{})
}