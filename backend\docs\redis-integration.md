# Redis缓存集成文档

## 概述

软件网络授权系统集成了Redis缓存，用于提高系统性能和响应速度。系统设计为即使Redis不可用也能正常运行，只是性能会有所降低。

## 功能特性

### ✅ 已实现功能

1. **Redis连接管理**
   - 自动连接池管理
   - 连接超时和重试机制
   - 优雅的连接失败处理

2. **缓存服务接口**
   - 基本的SET/GET操作
   - JSON数据序列化/反序列化
   - 批量操作支持
   - TTL（生存时间）管理

3. **高级缓存功能**
   - JWT令牌缓存
   - 许可证验证结果缓存
   - 用户会话缓存
   - API限流缓存
   - 统计数据缓存

4. **缓存键管理**
   - 结构化的缓存键命名
   - 不同类型数据的TTL策略
   - 缓存失效和清理机制

5. **健康检查和监控**
   - 缓存连接状态检查
   - 性能指标收集
   - 连接池统计
   - 响应时间监控

6. **中间件集成**
   - HTTP响应缓存中间件
   - API限流中间件
   - 缓存失效中间件

## 缓存策略

### 缓存键设计

```
auth:token:{token_id}           # JWT令牌缓存，TTL: 24小时
license:verify:{license_key}    # 许可证验证结果，TTL: 1小时
license:info:{license_key}      # 许可证信息，TTL: 30分钟
user:session:{user_id}          # 用户会话信息，TTL: 24小时
stats:daily:{date}              # 每日统计数据，TTL: 7天
rate_limit:{ip}:{endpoint}      # API限流，TTL: 1分钟
device:binding:{license}:{device} # 设备绑定信息，TTL: 2小时
```

### TTL策略

| 数据类型 | TTL | 说明 |
|---------|-----|------|
| JWT令牌 | 24小时 | 与令牌过期时间一致 |
| 许可证验证 | 1小时 | 平衡性能和数据一致性 |
| 许可证信息 | 30分钟 | 较频繁的更新需求 |
| 用户会话 | 24小时 | 用户活跃期间保持 |
| 统计数据 | 7天 | 历史数据保留 |
| API限流 | 1分钟 | 限流窗口期 |
| 设备绑定 | 2小时 | 设备状态变化检测 |

## 使用方法

### 1. 基本缓存操作

```go
// 创建缓存服务
service := cache.NewCacheService()

// 设置缓存
err := service.Set(ctx, "key", "value", 5*time.Minute)

// 获取缓存
value, err := service.Get(ctx, "key")

// JSON缓存
data := map[string]interface{}{"name": "test"}
err = service.SetJSON(ctx, "json_key", data, 10*time.Minute)

var result map[string]interface{}
err = service.GetJSON(ctx, "json_key", &result)
```

### 2. 高级缓存管理

```go
// 创建缓存管理器
manager := cache.NewManager()

// JWT令牌缓存
tokenData := &cache.AuthTokenData{
    UserID:   1,
    Username: "admin",
    Role:     "admin",
    IssuedAt: time.Now().Unix(),
}
err := manager.SetAuthToken(ctx, "token_id", tokenData)

// 许可证验证缓存
licenseData := &cache.LicenseVerifyData{
    Valid:       true,
    LicenseKey:  "LICENSE-KEY",
    ProductName: "Software",
    MaxDevices:  5,
    Features:    []string{"basic", "premium"},
}
err := manager.SetLicenseVerifyResult(ctx, "LICENSE-KEY", licenseData)

// 限流检查
allowed, err := manager.CheckRateLimit(ctx, "127.0.0.1", "/api/verify", 60)
```

### 3. 健康检查

```go
// 检查缓存健康状态
health := cache.CheckHealth()
if health.Available {
    fmt.Println("Cache is healthy")
}

// 简单健康检查
if cache.IsHealthy() {
    fmt.Println("Cache is available")
}

// 获取连接信息
info := cache.GetConnectionInfo()
```

## API接口

### 缓存管理接口（开发环境）

```
GET  /api/v1/cache/info      # 获取缓存信息
GET  /api/v1/cache/health    # 获取健康状态
GET  /api/v1/cache/metrics   # 获取性能指标
POST /api/v1/cache/test      # 测试缓存功能
POST /api/v1/cache/warmup    # 预热缓存
DELETE /api/v1/cache/clear   # 清空缓存
DELETE /api/v1/cache/cleanup/{pattern} # 清理指定模式的键
```

### 健康检查接口

```
GET /health  # 系统健康检查（包含缓存状态）
```

## 配置说明

### Redis配置项

```yaml
redis:
  addr: "localhost:6379"     # Redis服务器地址
  password: ""               # 密码（可选）
  db: 0                      # 数据库编号
  pool_size: 10              # 连接池大小
  min_idle_conns: 5          # 最小空闲连接数
  dial_timeout: 5s           # 连接超时
  read_timeout: 3s           # 读取超时
  write_timeout: 3s          # 写入超时
```

## 安装和部署

### Windows环境安装Redis

#### 方法1：使用Chocolatey（推荐）

```bash
# 安装Chocolatey（如果未安装）
# 在PowerShell管理员模式下运行
Set-ExecutionPolicy Bypass -Scope Process -Force
iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))

# 安装Redis
choco install redis-64 -y

# 启动Redis
redis-server
```

#### 方法2：手动安装

1. 下载Redis for Windows：https://github.com/microsoftarchive/redis/releases
2. 解压到目录（如：C:\Redis）
3. 运行redis-server.exe

#### 方法3：使用脚本

```bash
# 运行自动安装脚本
.\scripts\setup-redis.bat
```

### 验证安装

```bash
# 测试Redis连接
redis-cli ping
# 应该返回：PONG

# 测试应用缓存
go run ./cmd/cache-test/main.go
```

## 故障排除

### 常见问题

1. **连接被拒绝**
   ```
   Error: dial tcp [::1]:6379: connectex: No connection could be made
   ```
   - 解决方案：确保Redis服务器正在运行
   - 检查：`redis-cli ping`

2. **权限错误**
   ```
   Error: NOAUTH Authentication required
   ```
   - 解决方案：检查Redis密码配置
   - 更新config.yaml中的redis.password

3. **连接超时**
   ```
   Error: i/o timeout
   ```
   - 解决方案：增加超时时间或检查网络连接
   - 调整config.yaml中的超时设置

### 性能优化

1. **连接池优化**
   - 根据并发需求调整pool_size
   - 设置合适的min_idle_conns

2. **TTL策略优化**
   - 根据数据更新频率调整TTL
   - 避免过短的TTL导致频繁缓存失效

3. **键命名优化**
   - 使用有意义的键前缀
   - 避免键名冲突

## 监控和维护

### 性能监控

```bash
# 查看Redis信息
redis-cli info

# 查看连接数
redis-cli info clients

# 查看内存使用
redis-cli info memory

# 查看统计信息
redis-cli info stats
```

### 缓存清理

```bash
# 清空所有缓存
redis-cli flushdb

# 清理特定模式的键
redis-cli --scan --pattern "prefix:*" | xargs redis-cli del
```

### 备份和恢复

```bash
# 创建备份
redis-cli save

# 备份文件位置
# Windows: Redis安装目录下的dump.rdb
```

## 最佳实践

1. **缓存设计**
   - 合理设置TTL，避免数据过期
   - 使用结构化的键命名规范
   - 避免存储过大的数据对象

2. **错误处理**
   - 缓存失败不应影响业务逻辑
   - 实现缓存降级机制
   - 记录缓存相关错误日志

3. **性能优化**
   - 使用批量操作减少网络往返
   - 合理使用连接池
   - 监控缓存命中率

4. **安全考虑**
   - 生产环境设置Redis密码
   - 限制Redis访问IP
   - 定期更新Redis版本

## 开发工具

### 测试工具

```bash
# 缓存功能测试
go run ./cmd/cache-test/main.go

# Redis连接测试
go run ./cmd/redis-test/main.go

# 服务器启动测试
go run ./cmd/server/main.go
```

### 管理脚本

```bash
# Redis管理工具
.\scripts\redis.bat

# Redis安装脚本
.\scripts\setup-redis.bat
```

## 总结

Redis缓存集成已成功完成，系统具备以下特性：

✅ **完整的缓存功能** - 支持各种数据类型的缓存操作
✅ **优雅降级** - Redis不可用时系统仍能正常运行
✅ **性能监控** - 提供详细的健康检查和性能指标
✅ **易于维护** - 提供管理工具和清理脚本
✅ **生产就绪** - 包含错误处理和故障恢复机制

系统现在可以在有或没有Redis的情况下运行，为用户提供了灵活的部署选择。