Place your local development certificates here.

Recommended for local development:
- Use mkcert to generate and trust a local CA, then issue certs for localhost.

Quick start (Windows/macOS/Linux):
1) Install mkcert: https://github.com/FiloSottile/mkcert
2) Initialize local CA (one time):
   mkcert -install
3) Generate certs for localhost:
   mkcert localhost 127.0.0.1 ::1
4) Copy/rename the generated files to match config paths, for example:
   - ./configs/certs/localhost.pem
   - ./configs/certs/localhost-key.pem

Update backend/configs/config.yaml:
server:
  tls:
    enabled: true
    port: 8443
    cert_file: "./configs/certs/localhost.pem"
    key_file: "./configs/certs/localhost-key.pem"

Then start the server, and access:
- https://localhost:8443/api/v1/...

Note: Do not commit real private keys to version control for production.

