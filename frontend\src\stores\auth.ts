import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import type { LoginResponse, UserInfo } from '@/types/auth'
import { authApi } from '@/api/auth'

export const useAuthStore = defineStore('auth', () => {
  // 状态
  const token = ref<string | null>(localStorage.getItem('token'))
  const refreshToken = ref<string | null>(localStorage.getItem('refreshToken'))
  const user = ref<UserInfo | null>(null)
  const isLoading = ref(false)

  // 计算属性
  const isAuthenticated = computed(() => !!token.value && !!user.value)
  const isAdmin = computed(() => user.value?.role === 'admin')
  const isUser = computed(() => user.value?.role === 'user')

  // 登录
  const login = async (username: string, password: string) => {
    try {
      isLoading.value = true
      const response = await authApi.login({ username, password })
      
      if (response.data) {
        const { user: userInfo, tokens } = response.data
        
        // 保存令牌
        token.value = tokens.access_token
        refreshToken.value = tokens.refresh_token
        user.value = userInfo
        
        // 持久化存储
        localStorage.setItem('token', tokens.access_token)
        localStorage.setItem('refreshToken', tokens.refresh_token)
        localStorage.setItem('user', JSON.stringify(userInfo))
        
        return { success: true }
      }
      
      return { success: false, message: '登录失败' }
    } catch (error: any) {
      console.error('Login error:', error)
      return { 
        success: false, 
        message: error.response?.data?.message || '登录失败，请检查网络连接' 
      }
    } finally {
      isLoading.value = false
    }
  }

  // 登出
  const logout = async () => {
    try {
      if (token.value) {
        await authApi.logout()
      }
    } catch (error) {
      console.error('Logout error:', error)
    } finally {
      // 清除状态
      token.value = null
      refreshToken.value = null
      user.value = null
      
      // 清除存储
      localStorage.removeItem('token')
      localStorage.removeItem('refreshToken')
      localStorage.removeItem('user')
    }
  }

  // 刷新令牌
  const refreshAccessToken = async () => {
    try {
      if (!refreshToken.value) {
        throw new Error('No refresh token available')
      }
      
      const response = await authApi.refreshToken({ refresh_token: refreshToken.value })
      
      if (response.data) {
        const tokens = response.data
        token.value = tokens.access_token
        refreshToken.value = tokens.refresh_token
        
        localStorage.setItem('token', tokens.access_token)
        localStorage.setItem('refreshToken', tokens.refresh_token)
        
        return true
      }
      
      return false
    } catch (error) {
      console.error('Token refresh error:', error)
      await logout()
      return false
    }
  }

  // 获取用户资料
  const fetchProfile = async () => {
    try {
      const response = await authApi.getProfile()
      if (response.data) {
        user.value = response.data
        localStorage.setItem('user', JSON.stringify(response.data))
      }
    } catch (error) {
      console.error('Fetch profile error:', error)
    }
  }

  // 初始化用户信息
  const initializeAuth = () => {
    const storedUser = localStorage.getItem('user')
    if (storedUser && token.value) {
      try {
        user.value = JSON.parse(storedUser)
      } catch (error) {
        console.error('Failed to parse stored user:', error)
        logout()
      }
    }
  }

  // 修改密码
  const changePassword = async (oldPassword: string, newPassword: string) => {
    try {
      isLoading.value = true
      await authApi.changePassword({ old_password: oldPassword, new_password: newPassword })
      return { success: true, message: '密码修改成功' }
    } catch (error: any) {
      console.error('Change password error:', error)
      return { 
        success: false, 
        message: error.response?.data?.message || '密码修改失败' 
      }
    } finally {
      isLoading.value = false
    }
  }

  return {
    // 状态
    token,
    refreshToken,
    user,
    isLoading,
    
    // 计算属性
    isAuthenticated,
    isAdmin,
    isUser,
    
    // 方法
    login,
    logout,
    refreshAccessToken,
    fetchProfile,
    initializeAuth,
    changePassword
  }
})