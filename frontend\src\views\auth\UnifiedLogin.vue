<template>
  <div class="unified-login">
    <!-- 背景装饰 -->
    <div class="background-decoration">
      <div class="decoration-circle circle-1"></div>
      <div class="decoration-circle circle-2"></div>
      <div class="decoration-circle circle-3"></div>
      <div class="decoration-circle circle-4"></div>
    </div>

    <!-- 登录容器 -->
    <div class="login-container">
      <!-- 左侧信息面板 -->
      <div class="info-panel">
        <div class="info-content">
          <div class="logo-section">
            <div class="logo-icon">🔐</div>
            <h1 class="logo-title">软件网络授权系统</h1>
            <p class="logo-subtitle">Software Network Authorization System</p>
          </div>
          
          <div class="features-section">
            <h2>系统特性</h2>
            <div class="feature-list">
              <div class="feature-item">
                <div class="feature-icon">🛡️</div>
                <div class="feature-content">
                  <h3>安全可靠</h3>
                  <p>采用JWT认证和多重安全防护</p>
                </div>
              </div>
              <div class="feature-item">
                <div class="feature-icon">⚡</div>
                <div class="feature-content">
                  <h3>高效管理</h3>
                  <p>直观的界面和强大的管理功能</p>
                </div>
              </div>
              <div class="feature-item">
                <div class="feature-icon">📊</div>
                <div class="feature-content">
                  <h3>实时监控</h3>
                  <p>全面的统计分析和监控报告</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 右侧登录表单 -->
      <div class="login-panel">
        <div class="login-form-container">
          <div class="form-header">
            <h2>欢迎回来</h2>
            <p>请登录您的账户以继续使用系统</p>
          </div>

          <form @submit.prevent="handleLogin" class="login-form">
            <div class="form-group">
              <label for="username">用户名</label>
              <div class="input-wrapper">
                <span class="input-icon">👤</span>
                <input
                  id="username"
                  v-model="loginForm.username"
                  type="text"
                  placeholder="请输入用户名"
                  required
                  :disabled="loading"
                />
              </div>
            </div>

            <div class="form-group">
              <label for="password">密码</label>
              <div class="input-wrapper">
                <span class="input-icon">🔒</span>
                <input
                  id="password"
                  v-model="loginForm.password"
                  :type="showPassword ? 'text' : 'password'"
                  placeholder="请输入密码"
                  required
                  :disabled="loading"
                />
                <button
                  type="button"
                  @click="togglePassword"
                  class="password-toggle"
                  :disabled="loading"
                >
                  {{ showPassword ? '🙈' : '👁️' }}
                </button>
              </div>
            </div>

            <div class="form-options">
              <label class="checkbox-wrapper">
                <input type="checkbox" v-model="rememberMe" :disabled="loading">
                <span class="checkbox-custom"></span>
                <span class="checkbox-label">记住我</span>
              </label>
              <a href="#" class="forgot-password">忘记密码？</a>
            </div>

            <button type="submit" class="login-button" :disabled="loading">
              <span v-if="loading" class="loading-spinner">🔄</span>
              <span v-else>登录</span>
            </button>
          </form>

          <div class="demo-info">
            <div class="demo-header">
              <span class="demo-icon">💡</span>
              <span class="demo-title">演示账户</span>
            </div>
            <div class="demo-account">
              <div class="demo-item">
                <span class="demo-label">用户名:</span>
                <span class="demo-value">admin</span>
                <button @click="fillDemo" class="demo-fill-btn">快速填入</button>
              </div>
              <div class="demo-item">
                <span class="demo-label">密码:</span>
                <span class="demo-value">AdminPass123!</span>
              </div>
            </div>
          </div>

          <div class="login-footer">
            <p>&copy; 2024 软件网络授权系统. 保留所有权利.</p>
          </div>
        </div>
      </div>
    </div>

    <!-- 消息提示 -->
    <div v-if="message" class="message-toast" :class="messageType">
      <span class="message-icon">
        {{ messageType === 'success' ? '✅' : messageType === 'error' ? '❌' : 'ℹ️' }}
      </span>
      <span class="message-text">{{ message }}</span>
      <button @click="clearMessage" class="message-close">×</button>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()

// 表单数据
const loginForm = reactive({
  username: '',
  password: ''
})

// 状态
const loading = ref(false)
const showPassword = ref(false)
const rememberMe = ref(false)
const message = ref('')
const messageType = ref('info')

// 切换密码显示
const togglePassword = () => {
  showPassword.value = !showPassword.value
}

// 填入演示账户
const fillDemo = () => {
  loginForm.username = 'admin'
  loginForm.password = 'AdminPass123!'
}

// 显示消息
const showMessage = (text: string, type: string = 'info') => {
  message.value = text
  messageType.value = type
  
  // 3秒后自动清除
  setTimeout(() => {
    clearMessage()
  }, 3000)
}

// 清除消息
const clearMessage = () => {
  message.value = ''
}

// 处理登录
const handleLogin = async () => {
  if (!loginForm.username || !loginForm.password) {
    showMessage('请填写完整的登录信息', 'error')
    return
  }

  loading.value = true
  
  try {
    // 模拟登录验证
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    if (loginForm.username === 'admin' && loginForm.password === 'AdminPass123!') {
      // 保存登录状态
      localStorage.setItem('isLoggedIn', 'true')
      localStorage.setItem('username', loginForm.username)
      
      if (rememberMe.value) {
        localStorage.setItem('rememberMe', 'true')
      }
      
      showMessage('登录成功，正在跳转...', 'success')
      
      // 延迟跳转以显示成功消息
      setTimeout(() => {
        router.push('/dashboard')
      }, 1000)
    } else {
      showMessage('用户名或密码错误，请重试', 'error')
    }
  } catch (error) {
    showMessage('登录失败，请检查网络连接', 'error')
  } finally {
    loading.value = false
  }
}
</script>

<style scoped>
.unified-login {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  position: relative;
  overflow: hidden;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 背景装饰 */
.background-decoration {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  animation: float 6s ease-in-out infinite;
}

.circle-1 {
  width: 200px;
  height: 200px;
  top: 10%;
  left: 10%;
  animation-delay: 0s;
}

.circle-2 {
  width: 150px;
  height: 150px;
  top: 20%;
  right: 15%;
  animation-delay: 2s;
}

.circle-3 {
  width: 100px;
  height: 100px;
  bottom: 20%;
  left: 20%;
  animation-delay: 4s;
}

.circle-4 {
  width: 120px;
  height: 120px;
  bottom: 15%;
  right: 10%;
  animation-delay: 1s;
}

@keyframes float {
  0%, 100% { transform: translateY(0px) rotate(0deg); }
  50% { transform: translateY(-20px) rotate(180deg); }
}

/* 登录容器 */
.login-container {
  display: flex;
  max-width: 1200px;
  width: 100%;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-radius: 24px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
  overflow: hidden;
  position: relative;
  z-index: 1;
}

/* 左侧信息面板 */
.info-panel {
  flex: 1;
  background: linear-gradient(135deg, #1890ff 0%, #722ed1 100%);
  color: white;
  padding: 60px 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.logo-section {
  text-align: center;
  margin-bottom: 60px;
}

.logo-icon {
  font-size: 80px;
  margin-bottom: 20px;
  filter: drop-shadow(0 4px 8px rgba(0, 0, 0, 0.3));
}

.logo-title {
  font-size: 32px;
  font-weight: 700;
  margin: 0 0 8px 0;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.logo-subtitle {
  font-size: 16px;
  opacity: 0.9;
  margin: 0;
  font-weight: 300;
}

.features-section h2 {
  font-size: 24px;
  font-weight: 600;
  margin-bottom: 30px;
  text-align: center;
}

.feature-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.feature-item:hover {
  background: rgba(255, 255, 255, 0.2);
  transform: translateX(8px);
}

.feature-icon {
  font-size: 32px;
  width: 56px;
  height: 56px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 12px;
  flex-shrink: 0;
}

.feature-content h3 {
  font-size: 18px;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.feature-content p {
  font-size: 14px;
  margin: 0;
  opacity: 0.9;
}

/* 右侧登录表单 */
.login-panel {
  flex: 1;
  padding: 60px 40px;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.form-header {
  text-align: center;
  margin-bottom: 40px;
}

.form-header h2 {
  font-size: 28px;
  font-weight: 700;
  color: #262626;
  margin: 0 0 8px 0;
}

.form-header p {
  font-size: 16px;
  color: #8c8c8c;
  margin: 0;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.form-group label {
  font-size: 14px;
  font-weight: 600;
  color: #262626;
}

.input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.input-icon {
  position: absolute;
  left: 16px;
  font-size: 18px;
  color: #8c8c8c;
  z-index: 1;
}

.input-wrapper input {
  width: 100%;
  padding: 16px 16px 16px 48px;
  border: 2px solid #f0f0f0;
  border-radius: 12px;
  font-size: 16px;
  transition: all 0.3s ease;
  background: white;
}

.input-wrapper input:focus {
  outline: none;
  border-color: #1890ff;
  box-shadow: 0 0 0 4px rgba(24, 144, 255, 0.1);
}

.input-wrapper input:disabled {
  background: #f5f5f5;
  cursor: not-allowed;
}

.password-toggle {
  position: absolute;
  right: 16px;
  background: none;
  border: none;
  font-size: 18px;
  cursor: pointer;
  color: #8c8c8c;
  transition: all 0.2s;
}

.password-toggle:hover:not(:disabled) {
  color: #1890ff;
}

.password-toggle:disabled {
  cursor: not-allowed;
  opacity: 0.5;
}

.form-options {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin: 8px 0;
}

.checkbox-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  font-size: 14px;
  color: #666;
}

.checkbox-wrapper input[type="checkbox"] {
  display: none;
}

.checkbox-custom {
  width: 18px;
  height: 18px;
  border: 2px solid #d9d9d9;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.checkbox-wrapper input[type="checkbox"]:checked + .checkbox-custom {
  background: #1890ff;
  border-color: #1890ff;
}

.checkbox-wrapper input[type="checkbox"]:checked + .checkbox-custom::after {
  content: '✓';
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.forgot-password {
  color: #1890ff;
  text-decoration: none;
  font-size: 14px;
  transition: all 0.2s;
}

.forgot-password:hover {
  color: #40a9ff;
  text-decoration: underline;
}

.login-button {
  width: 100%;
  padding: 16px;
  background: linear-gradient(135deg, #1890ff, #722ed1);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 16px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.3);
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

.login-button:hover:not(:disabled) {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(24, 144, 255, 0.4);
}

.login-button:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
}

.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* 演示信息 */
.demo-info {
  margin-top: 32px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 12px;
  border: 1px solid #e9ecef;
}

.demo-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.demo-icon {
  font-size: 18px;
}

.demo-title {
  font-size: 14px;
  font-weight: 600;
  color: #495057;
}

.demo-account {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.demo-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.demo-label {
  font-size: 13px;
  color: #6c757d;
  min-width: 50px;
}

.demo-value {
  font-size: 13px;
  font-family: 'Courier New', monospace;
  background: white;
  padding: 4px 8px;
  border-radius: 4px;
  border: 1px solid #dee2e6;
  flex: 1;
}

.demo-fill-btn {
  padding: 4px 12px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s;
}

.demo-fill-btn:hover {
  background: #40a9ff;
}

.login-footer {
  margin-top: 32px;
  text-align: center;
}

.login-footer p {
  font-size: 12px;
  color: #8c8c8c;
  margin: 0;
}

/* 消息提示 */
.message-toast {
  position: fixed;
  top: 24px;
  right: 24px;
  padding: 16px 20px;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2);
  display: flex;
  align-items: center;
  gap: 12px;
  z-index: 1000;
  backdrop-filter: blur(10px);
  animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.message-toast.success {
  background: rgba(82, 196, 26, 0.9);
  color: white;
}

.message-toast.error {
  background: rgba(255, 77, 79, 0.9);
  color: white;
}

.message-toast.info {
  background: rgba(24, 144, 255, 0.9);
  color: white;
}

.message-icon {
  font-size: 18px;
}

.message-text {
  font-size: 14px;
  font-weight: 500;
}

.message-close {
  background: none;
  border: none;
  color: currentColor;
  font-size: 18px;
  cursor: pointer;
  padding: 0;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  transition: all 0.2s;
}

.message-close:hover {
  background: rgba(255, 255, 255, 0.2);
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .login-container {
    flex-direction: column;
    max-width: 500px;
  }
  
  .info-panel {
    padding: 40px 30px;
  }
  
  .login-panel {
    padding: 40px 30px;
  }
  
  .features-section {
    display: none;
  }
}

@media (max-width: 768px) {
  .unified-login {
    padding: 16px;
  }
  
  .login-container {
    border-radius: 16px;
  }
  
  .info-panel {
    padding: 30px 20px;
  }
  
  .login-panel {
    padding: 30px 20px;
  }
  
  .logo-icon {
    font-size: 60px;
  }
  
  .logo-title {
    font-size: 24px;
  }
  
  .form-header h2 {
    font-size: 24px;
  }
  
  .message-toast {
    top: 16px;
    right: 16px;
    left: 16px;
  }
}

@media (max-width: 480px) {
  .demo-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .demo-value {
    width: 100%;
  }
}
</style>