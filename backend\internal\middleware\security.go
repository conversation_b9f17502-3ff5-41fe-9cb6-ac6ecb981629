package middleware

import (
	"net"
	"net/http"
	"strings"
	"sync"
	"time"

	"github.com/gin-gonic/gin"
	"golang.org/x/time/rate"
)

// RateLimiter 速率限制器
type RateLimiter struct {
	limiters map[string]*rate.Limiter
	mu       sync.RWMutex
	rate     rate.Limit
	burst    int
	cleanup  time.Duration
}

// NewRateLimiter 创建速率限制器
func NewRateLimiter(r rate.Limit, b int) *RateLimiter {
	rl := &RateLimiter{
		limiters: make(map[string]*rate.Limiter),
		rate:     r,
		burst:    b,
		cleanup:  time.Minute * 5, // 5分钟清理一次
	}

	// 启动清理协程
	go rl.cleanupRoutine()
	return rl
}

// GetLimiter 获取指定IP的限制器
func (rl *RateLimiter) GetLimiter(ip string) *rate.Limiter {
	rl.mu.Lock()
	defer rl.mu.Unlock()

	limiter, exists := rl.limiters[ip]
	if !exists {
		limiter = rate.NewLimiter(rl.rate, rl.burst)
		rl.limiters[ip] = limiter
	}

	return limiter
}

// cleanupRoutine 清理不活跃的限制器
func (rl *RateLimiter) cleanupRoutine() {
	ticker := time.NewTicker(rl.cleanup)
	defer ticker.Stop()

	for range ticker.C {
		rl.mu.Lock()
		for ip, limiter := range rl.limiters {
			// 如果限制器的令牌桶已满且最近没有请求，则删除
			if limiter.Tokens() == float64(rl.burst) {
				delete(rl.limiters, ip)
			}
		}
		rl.mu.Unlock()
	}
}

// IPWhitelist IP白名单
type IPWhitelist struct {
	whitelist map[string]bool
	mu        sync.RWMutex
}

// NewIPWhitelist 创建IP白名单
func NewIPWhitelist(ips []string) *IPWhitelist {
	whitelist := make(map[string]bool)
	for _, ip := range ips {
		whitelist[ip] = true
	}
	return &IPWhitelist{
		whitelist: whitelist,
	}
}

// IsWhitelisted 检查IP是否在白名单中
func (w *IPWhitelist) IsWhitelisted(ip string) bool {
	w.mu.RLock()
	defer w.mu.RUnlock()
	return w.whitelist[ip]
}

// AddIP 添加IP到白名单
func (w *IPWhitelist) AddIP(ip string) {
	w.mu.Lock()
	defer w.mu.Unlock()
	w.whitelist[ip] = true
}

// RemoveIP 从白名单移除IP
func (w *IPWhitelist) RemoveIP(ip string) {
	w.mu.Lock()
	defer w.mu.Unlock()
	delete(w.whitelist, ip)
}

// IPBlacklist IP黑名单
type IPBlacklist struct {
	blacklist map[string]time.Time
	mu        sync.RWMutex
}

// NewIPBlacklist 创建IP黑名单
func NewIPBlacklist() *IPBlacklist {
	bl := &IPBlacklist{
		blacklist: make(map[string]time.Time),
	}
	
	// 启动清理协程
	go bl.cleanupRoutine()
	return bl
}

// IsBlacklisted 检查IP是否在黑名单中
func (b *IPBlacklist) IsBlacklisted(ip string) bool {
	b.mu.RLock()
	defer b.mu.RUnlock()
	
	expiry, exists := b.blacklist[ip]
	if !exists {
		return false
	}
	
	// 检查是否过期
	if time.Now().After(expiry) {
		delete(b.blacklist, ip)
		return false
	}
	
	return true
}

// AddIP 添加IP到黑名单
func (b *IPBlacklist) AddIP(ip string, duration time.Duration) {
	b.mu.Lock()
	defer b.mu.Unlock()
	b.blacklist[ip] = time.Now().Add(duration)
}

// RemoveIP 从黑名单移除IP
func (b *IPBlacklist) RemoveIP(ip string) {
	b.mu.Lock()
	defer b.mu.Unlock()
	delete(b.blacklist, ip)
}

// cleanupRoutine 清理过期的黑名单条目
func (b *IPBlacklist) cleanupRoutine() {
	ticker := time.NewTicker(time.Minute * 10) // 10分钟清理一次
	defer ticker.Stop()

	for range ticker.C {
		b.mu.Lock()
		now := time.Now()
		for ip, expiry := range b.blacklist {
			if now.After(expiry) {
				delete(b.blacklist, ip)
			}
		}
		b.mu.Unlock()
	}
}

// APIKeyAuth API密钥认证
type APIKeyAuth struct {
	keys map[string]APIKeyInfo
	mu   sync.RWMutex
}

// APIKeyInfo API密钥信息
type APIKeyInfo struct {
	Name        string
	Permissions []string
	CreatedAt   time.Time
	LastUsedAt  time.Time
	IsActive    bool
}

// NewAPIKeyAuth 创建API密钥认证
func NewAPIKeyAuth() *APIKeyAuth {
	return &APIKeyAuth{
		keys: make(map[string]APIKeyInfo),
	}
}

// AddAPIKey 添加API密钥
func (a *APIKeyAuth) AddAPIKey(key, name string, permissions []string) {
	a.mu.Lock()
	defer a.mu.Unlock()
	
	a.keys[key] = APIKeyInfo{
		Name:        name,
		Permissions: permissions,
		CreatedAt:   time.Now(),
		IsActive:    true,
	}
}

// ValidateAPIKey 验证API密钥
func (a *APIKeyAuth) ValidateAPIKey(key string) (APIKeyInfo, bool) {
	a.mu.Lock()
	defer a.mu.Unlock()
	
	info, exists := a.keys[key]
	if !exists || !info.IsActive {
		return APIKeyInfo{}, false
	}
	
	// 更新最后使用时间
	info.LastUsedAt = time.Now()
	a.keys[key] = info
	
	return info, true
}

// 全局实例
var (
	globalRateLimiter *RateLimiter
	globalWhitelist   *IPWhitelist
	globalBlacklist   *IPBlacklist
	globalAPIKeyAuth  *APIKeyAuth
)

// 初始化安全组件
func init() {
	// 默认配置：每秒10个请求，突发20个
	globalRateLimiter = NewRateLimiter(10, 20)
	
	// 默认白名单（本地IP）
	globalWhitelist = NewIPWhitelist([]string{
		"127.0.0.1",
		"::1",
		"localhost",
	})
	
	globalBlacklist = NewIPBlacklist()
	globalAPIKeyAuth = NewAPIKeyAuth()
	
	// 添加一些默认的API密钥（实际应用中应该从配置文件或数据库读取）
	globalAPIKeyAuth.AddAPIKey("test-api-key-123", "Test Client", []string{"verify", "offline"})
	globalAPIKeyAuth.AddAPIKey("admin-api-key-456", "Admin Client", []string{"*"})
}

// RateLimitMiddleware 速率限制中间件
func RateLimitMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		ip := getClientIP(c)
		
		// 检查白名单
		if globalWhitelist.IsWhitelisted(ip) {
			c.Next()
			return
		}
		
		// 检查黑名单
		if globalBlacklist.IsBlacklisted(ip) {
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error":   "IP blocked",
				"message": "Your IP address has been temporarily blocked due to excessive requests",
				"code":    "IP_BLOCKED",
			})
			c.Abort()
			return
		}
		
		// 速率限制检查
		limiter := globalRateLimiter.GetLimiter(ip)
		if !limiter.Allow() {
			// 如果超过限制，将IP加入黑名单（临时）
			globalBlacklist.AddIP(ip, time.Minute*15) // 15分钟黑名单
			
			c.JSON(http.StatusTooManyRequests, gin.H{
				"error":   "Rate limit exceeded",
				"message": "Too many requests. Please try again later.",
				"code":    "RATE_LIMIT_EXCEEDED",
			})
			c.Abort()
			return
		}
		
		c.Next()
	}
}

// IPFilterMiddleware IP过滤中间件
func IPFilterMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		ip := getClientIP(c)
		
		// 检查黑名单
		if globalBlacklist.IsBlacklisted(ip) {
			c.JSON(http.StatusForbidden, gin.H{
				"error":   "Access denied",
				"message": "Your IP address is blocked",
				"code":    "IP_BLOCKED",
			})
			c.Abort()
			return
		}
		
		c.Next()
	}
}

// APIKeyMiddleware API密钥认证中间件
func APIKeyMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 从Header获取API密钥
		apiKey := c.GetHeader("X-API-Key")
		if apiKey == "" {
			// 也可以从查询参数获取
			apiKey = c.Query("api_key")
		}
		
		if apiKey == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "API key required",
				"message": "Please provide a valid API key",
				"code":    "API_KEY_REQUIRED",
			})
			c.Abort()
			return
		}
		
		// 验证API密钥
		keyInfo, valid := globalAPIKeyAuth.ValidateAPIKey(apiKey)
		if !valid {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error":   "Invalid API key",
				"message": "The provided API key is invalid or inactive",
				"code":    "INVALID_API_KEY",
			})
			c.Abort()
			return
		}
		
		// 将API密钥信息存储到上下文
		c.Set("api_key_info", keyInfo)
		c.Next()
	}
}

// SecurityHeadersMiddleware 安全头中间件
func SecurityHeadersMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 设置安全头
		c.Header("X-Content-Type-Options", "nosniff")
		c.Header("X-Frame-Options", "DENY")
		c.Header("X-XSS-Protection", "1; mode=block")
		c.Header("Referrer-Policy", "strict-origin-when-cross-origin")
		c.Header("Content-Security-Policy", "default-src 'self'")
		
		// 移除服务器信息
		c.Header("Server", "")
		
		c.Next()
	}
}

// CORSMiddleware CORS中间件
func CORSMiddleware() gin.HandlerFunc {
	return func(c *gin.Context) {
		origin := c.Request.Header.Get("Origin")
		
		// 允许的域名列表（实际应用中应该从配置读取）
		allowedOrigins := []string{
			"http://localhost:3000",
			"http://localhost:8080",
			"http://127.0.0.1:3000",
			"http://127.0.0.1:8080",
		}
		
		// 检查是否为允许的域名
		allowed := false
		for _, allowedOrigin := range allowedOrigins {
			if origin == allowedOrigin {
				allowed = true
				break
			}
		}
		
		if allowed {
			c.Header("Access-Control-Allow-Origin", origin)
		}
		
		c.Header("Access-Control-Allow-Methods", "GET, POST, PUT, DELETE, OPTIONS")
		c.Header("Access-Control-Allow-Headers", "Origin, Content-Type, Content-Length, Accept-Encoding, X-CSRF-Token, Authorization, X-API-Key")
		c.Header("Access-Control-Expose-Headers", "Content-Length")
		c.Header("Access-Control-Allow-Credentials", "true")
		c.Header("Access-Control-Max-Age", "86400")
		
		// 处理预检请求
		if c.Request.Method == "OPTIONS" {
			c.AbortWithStatus(http.StatusNoContent)
			return
		}
		
		c.Next()
	}
}

// getClientIP 获取客户端真实IP
func getClientIP(c *gin.Context) string {
	// 检查X-Forwarded-For头
	xForwardedFor := c.GetHeader("X-Forwarded-For")
	if xForwardedFor != "" {
		// X-Forwarded-For可能包含多个IP，取第一个
		ips := strings.Split(xForwardedFor, ",")
		if len(ips) > 0 {
			ip := strings.TrimSpace(ips[0])
			if net.ParseIP(ip) != nil {
				return ip
			}
		}
	}
	
	// 检查X-Real-IP头
	xRealIP := c.GetHeader("X-Real-IP")
	if xRealIP != "" {
		if net.ParseIP(xRealIP) != nil {
			return xRealIP
		}
	}
	
	// 检查X-Forwarded头
	xForwarded := c.GetHeader("X-Forwarded")
	if xForwarded != "" {
		if net.ParseIP(xForwarded) != nil {
			return xForwarded
		}
	}
	
	// 使用RemoteAddr
	ip, _, err := net.SplitHostPort(c.Request.RemoteAddr)
	if err != nil {
		return c.Request.RemoteAddr
	}
	
	return ip
}

// GetActiveLimiters 获取活跃限制器数量
func (rl *RateLimiter) GetActiveLimiters() map[string]*rate.Limiter {
	rl.mu.RLock()
	defer rl.mu.RUnlock()
	
	result := make(map[string]*rate.Limiter)
	for k, v := range rl.limiters {
		result[k] = v
	}
	return result
}

// GetRate 获取速率
func (rl *RateLimiter) GetRate() rate.Limit {
	return rl.rate
}

// GetBurst 获取突发大小
func (rl *RateLimiter) GetBurst() int {
	return rl.burst
}

// GetCount 获取白名单IP数量
func (w *IPWhitelist) GetCount() int {
	w.mu.RLock()
	defer w.mu.RUnlock()
	return len(w.whitelist)
}

// GetAllIPs 获取所有白名单IP
func (w *IPWhitelist) GetAllIPs() []string {
	w.mu.RLock()
	defer w.mu.RUnlock()
	
	ips := make([]string, 0, len(w.whitelist))
	for ip := range w.whitelist {
		ips = append(ips, ip)
	}
	return ips
}

// GetCount 获取黑名单IP数量
func (b *IPBlacklist) GetCount() int {
	b.mu.RLock()
	defer b.mu.RUnlock()
	return len(b.blacklist)
}

// GetAllIPs 获取所有黑名单IP及过期时间
func (b *IPBlacklist) GetAllIPs() map[string]time.Time {
	b.mu.RLock()
	defer b.mu.RUnlock()
	
	result := make(map[string]time.Time)
	for ip, expiry := range b.blacklist {
		result[ip] = expiry
	}
	return result
}

// GetCount 获取API密钥数量
func (a *APIKeyAuth) GetCount() int {
	a.mu.RLock()
	defer a.mu.RUnlock()
	return len(a.keys)
}

// GetAllKeys 获取所有API密钥信息（不包含密钥本身）
func (a *APIKeyAuth) GetAllKeys() []map[string]interface{} {
	a.mu.RLock()
	defer a.mu.RUnlock()
	
	result := make([]map[string]interface{}, 0, len(a.keys))
	for key, info := range a.keys {
		result = append(result, map[string]interface{}{
			"key_prefix":   key[:8] + "...", // 只显示前8位
			"name":         info.Name,
			"permissions":  info.Permissions,
			"created_at":   info.CreatedAt,
			"last_used_at": info.LastUsedAt,
			"is_active":    info.IsActive,
		})
	}
	return result
}

// RevokeAPIKey 撤销API密钥
func (a *APIKeyAuth) RevokeAPIKey(key string) bool {
	a.mu.Lock()
	defer a.mu.Unlock()
	
	info, exists := a.keys[key]
	if !exists {
		return false
	}
	
	info.IsActive = false
	a.keys[key] = info
	return true
}

// GetRateLimiter 获取全局速率限制器
func GetRateLimiter() *RateLimiter {
	return globalRateLimiter
}

// GetIPWhitelist 获取全局IP白名单
func GetIPWhitelist() *IPWhitelist {
	return globalWhitelist
}

// GetIPBlacklist 获取全局IP黑名单
func GetIPBlacklist() *IPBlacklist {
	return globalBlacklist
}

// GetAPIKeyAuth 获取全局API密钥认证
func GetAPIKeyAuth() *APIKeyAuth {
	return globalAPIKeyAuth
}