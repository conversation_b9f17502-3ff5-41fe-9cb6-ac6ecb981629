@echo off
echo ========================================
echo 前端构建工具
echo ========================================

echo.
echo 1. 安装依赖
echo 2. 开发模式启动
echo 3. 构建生产版本
echo 4. 预览构建结果
echo 5. 类型检查
echo 6. 退出
echo.

set /p choice=请选择操作 (1-6): 

if "%choice%"=="1" (
    echo 安装依赖...
    npm install
) else if "%choice%"=="2" (
    echo 启动开发服务器...
    npm run dev
) else if "%choice%"=="3" (
    echo 构建生产版本...
    npm run build
) else if "%choice%"=="4" (
    echo 预览构建结果...
    npm run preview
) else if "%choice%"=="5" (
    echo 运行类型检查...
    npm run type-check
) else if "%choice%"=="6" (
    exit
) else (
    echo 无效选择，请重新运行脚本
)

pause