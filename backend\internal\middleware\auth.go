package middleware

import (
	"fmt"
	"net/http"
	"strings"

	"github.com/gin-gonic/gin"
	"software-auth/internal/auth"
	"software-auth/internal/cache"
	"software-auth/internal/config"
)

// AuthMiddleware 认证中间件
func AuthMiddleware(jwtConfig *config.JWTConfig) gin.HandlerFunc {
	jwtService := auth.NewJWTService(jwtConfig)
	cacheManager := cache.NewManager()

	return func(c *gin.Context) {
		// 获取Authorization头
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Authorization header required",
				"code":  "MISSING_AUTH_HEADER",
			})
			c.Abort()
			return
		}

		// 检查Bearer前缀
		const bearerPrefix = "Bearer "
		if !strings.HasPrefix(authHeader, bearerPrefix) {
			c.<PERSON>(http.StatusUnauthorized, gin.H{
				"error": "Invalid authorization header format",
				"code":  "INVALID_AUTH_FORMAT",
			})
			c.Abort()
			return
		}

		// 提取令牌
		tokenString := authHeader[len(bearerPrefix):]
		if tokenString == "" {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Token required",
				"code":  "MISSING_TOKEN",
			})
			c.Abort()
			return
		}

		// 验证令牌
		claims, err := jwtService.ValidateToken(tokenString)
		if err != nil {
			var errorCode string
			switch err {
			case auth.ErrTokenExpired:
				errorCode = "TOKEN_EXPIRED"
			case auth.ErrInvalidToken:
				errorCode = "INVALID_TOKEN"
			default:
				errorCode = "TOKEN_VALIDATION_FAILED"
			}

			c.JSON(http.StatusUnauthorized, gin.H{
				"error": err.Error(),
				"code":  errorCode,
			})
			c.Abort()
			return
		}

		// 检查令牌是否已被撤销
		if jwtService.IsTokenRevoked(claims.TokenID) {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "Token has been revoked",
				"code":  "TOKEN_REVOKED",
			})
			c.Abort()
			return
		}

		// 检查令牌是否在缓存中（可选的额外验证）
		if tokenData, err := cacheManager.GetAuthToken(c.Request.Context(), claims.TokenID); err == nil {
			// 验证缓存中的令牌数据
			if tokenData.UserID != claims.UserID {
				c.JSON(http.StatusUnauthorized, gin.H{
					"error": "Token user mismatch",
					"code":  "TOKEN_USER_MISMATCH",
				})
				c.Abort()
				return
			}
		} else if err != cache.ErrCacheNotFound {
			// 缓存错误（非未找到错误）记录日志但不阻止请求
			fmt.Printf("Cache error during token validation: %v\n", err)
		}

		// 将用户信息存储到上下文中
		c.Set("user_id", claims.UserID)
		c.Set("username", claims.Username)
		c.Set("user_role", claims.Role)
		c.Set("token_id", claims.TokenID)
		c.Set("claims", claims)

		c.Next()
	}
}

// RequireRole 角色权限中间件
func RequireRole(allowedRoles ...string) gin.HandlerFunc {
	return func(c *gin.Context) {
		userRole, exists := c.Get("user_role")
		if !exists {
			c.JSON(http.StatusUnauthorized, gin.H{
				"error": "User role not found in context",
				"code":  "MISSING_USER_ROLE",
			})
			c.Abort()
			return
		}

		role, ok := userRole.(string)
		if !ok {
			c.JSON(http.StatusInternalServerError, gin.H{
				"error": "Invalid user role type",
				"code":  "INVALID_ROLE_TYPE",
			})
			c.Abort()
			return
		}

		// 检查角色权限
		for _, allowedRole := range allowedRoles {
			if role == allowedRole {
				c.Next()
				return
			}
		}

		c.JSON(http.StatusForbidden, gin.H{
			"error": "Insufficient permissions",
			"code":  "INSUFFICIENT_PERMISSIONS",
			"required_roles": allowedRoles,
			"user_role": role,
		})
		c.Abort()
	}
}

// RequireAdmin 管理员权限中间件
func RequireAdmin() gin.HandlerFunc {
	return RequireRole("admin")
}

// RequireActiveUser 活跃用户中间件
func RequireActiveUser() gin.HandlerFunc {
	return func(c *gin.Context) {
		// 这里可以添加检查用户是否活跃的逻辑
		// 例如检查用户状态、最后活动时间等
		c.Next()
	}
}

// OptionalAuth 可选认证中间件
func OptionalAuth(jwtConfig *config.JWTConfig) gin.HandlerFunc {
	jwtService := auth.NewJWTService(jwtConfig)

	return func(c *gin.Context) {
		authHeader := c.GetHeader("Authorization")
		if authHeader == "" {
			// 没有认证头，继续处理但不设置用户信息
			c.Next()
			return
		}

		const bearerPrefix = "Bearer "
		if !strings.HasPrefix(authHeader, bearerPrefix) {
			c.Next()
			return
		}

		tokenString := authHeader[len(bearerPrefix):]
		if tokenString == "" {
			c.Next()
			return
		}

		// 尝试验证令牌
		claims, err := jwtService.ValidateToken(tokenString)
		if err == nil {
			// 令牌有效，设置用户信息
			c.Set("user_id", claims.UserID)
			c.Set("username", claims.Username)
			c.Set("user_role", claims.Role)
			c.Set("token_id", claims.TokenID)
			c.Set("claims", claims)
		}

		c.Next()
	}
}

// GetCurrentUser 获取当前用户信息
func GetCurrentUser(c *gin.Context) *CurrentUser {
	userID, exists := c.Get("user_id")
	if !exists {
		return nil
	}

	username, _ := c.Get("username")
	userRole, _ := c.Get("user_role")
	tokenID, _ := c.Get("token_id")

	return &CurrentUser{
		ID:       userID.(uint),
		Username: username.(string),
		Role:     userRole.(string),
		TokenID:  tokenID.(string),
	}
}

// CurrentUser 当前用户信息
type CurrentUser struct {
	ID       uint   `json:"id"`
	Username string `json:"username"`
	Role     string `json:"role"`
	TokenID  string `json:"token_id"`
}

// IsAdmin 检查是否为管理员
func (u *CurrentUser) IsAdmin() bool {
	return u.Role == "admin"
}

// IsAPI 检查是否为API用户
func (u *CurrentUser) IsAPI() bool {
	return u.Role == "api"
}

// CanAccess 检查是否可以访问指定资源
func (u *CurrentUser) CanAccess(resource string) bool {
	// 管理员可以访问所有资源
	if u.IsAdmin() {
		return true
	}

	// 这里可以实现更复杂的权限检查逻辑
	// 例如基于资源的访问控制
	switch resource {
	case "licenses":
		return u.Role == "user" || u.Role == "admin"
	case "users":
		return u.Role == "admin"
	case "reports":
		return u.Role == "admin"
	default:
		return false
	}
}