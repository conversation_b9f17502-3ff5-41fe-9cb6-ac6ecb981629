package api

import (
	"net/http"
	"strconv"
	"time"

	"software-auth/internal/auth"
	"software-auth/internal/database"
	"software-auth/internal/model"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// UserHandler 用户处理器
type UserHandler struct {
	passwordService auth.PasswordService
}

// NewUserHandler 创建用户处理器
func NewUserHandler() *UserHandler {
	return &UserHandler{
		passwordService: auth.NewPasswordService(),
	}
}

// CreateUserRequest 创建用户请求
type CreateUserRequest struct {
	Username string `json:"username" binding:"required,min=3,max=50"`
	Email    string `json:"email" binding:"required,email"`
	Password string `json:"password" binding:"required,min=8"`
	Role     string `json:"role" binding:"required,oneof=admin user"`
	Status   string `json:"status" binding:"omitempty,oneof=active inactive blocked"`
}

// UpdateUserRequest 更新用户请求
type UpdateUserRequest struct {
	Username string `json:"username" binding:"omitempty,min=3,max=50"`
	Email    string `json:"email" binding:"omitempty,email"`
	Role     string `json:"role" binding:"omitempty,oneof=admin user"`
	Status   string `json:"status" binding:"omitempty,oneof=active inactive blocked suspended"`
}

// ChangeUserPasswordRequest 修改用户密码请求
type ChangeUserPasswordRequest struct {
	NewPassword string `json:"new_password" binding:"required,min=8"`
}

// UserListRequest 用户列表请求
type UserListRequest struct {
	Page      int    `form:"page,default=1" binding:"min=1"`
	PageSize  int    `form:"page_size,default=10" binding:"min=1,max=100"`
	Keyword   string `form:"keyword"`
	Role      string `form:"role" binding:"omitempty,oneof=admin user"`
	Status    string `form:"status" binding:"omitempty,oneof=active inactive blocked suspended"`
	SortBy    string `form:"sort_by,default=created_at" binding:"omitempty,oneof=created_at username email role status last_login_at"`
	SortOrder string `form:"sort_order,default=desc" binding:"omitempty,oneof=asc desc"`
}

// UserResponse 用户响应
type UserResponse struct {
	ID          uint      `json:"id"`
	Username    string    `json:"username"`
	Email       string    `json:"email"`
	Role        string    `json:"role"`
	Status      string    `json:"status"`
	CreatedAt   time.Time `json:"created_at"`
	UpdatedAt   time.Time `json:"updated_at"`
	LastLoginAt time.Time `json:"last_login_at"`
}

// UserListResponse 用户列表响应
type UserListResponse struct {
	Users      []UserResponse `json:"users"`
	Total      int64          `json:"total"`
	Page       int            `json:"page"`
	PageSize   int            `json:"page_size"`
	TotalPages int            `json:"total_pages"`
}

// UserStatsResponse 用户统计响应
type UserStatsResponse struct {
	Total     int64 `json:"total"`
	Active    int64 `json:"active"`
	Inactive  int64 `json:"inactive"`
	Blocked   int64 `json:"blocked"`
	Suspended int64 `json:"suspended"`
	Admins    int64 `json:"admins"`
	Users     int64 `json:"users"`
}

// CreateUser 创建用户
func (h *UserHandler) CreateUser(c *gin.Context) {
	// 检查权限
	userRole, exists := c.Get("user_role")
	if !exists || userRole != "admin" {
		ErrorResponse(c, http.StatusForbidden, "Insufficient permissions", "ADMIN_REQUIRED")
		return
	}

	var req CreateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ErrorResponse(c, http.StatusBadRequest, "Invalid request format", err.Error())
		return
	}

	// 设置默认状态
	if req.Status == "" {
		req.Status = "active"
	}

	// 检查用户名是否已存在
	db := database.GetDB()
	var existingUser model.User
	if err := db.Where("username = ?", req.Username).First(&existingUser).Error; err == nil {
		ErrorResponse(c, http.StatusConflict, "Username already exists", "USERNAME_EXISTS")
		return
	}

	// 检查邮箱是否已存在
	if err := db.Where("email = ?", req.Email).First(&existingUser).Error; err == nil {
		ErrorResponse(c, http.StatusConflict, "Email already exists", "EMAIL_EXISTS")
		return
	}

	// 验证密码强度
	if err := h.passwordService.IsStrongPassword(req.Password); err != nil {
		ErrorResponse(c, http.StatusBadRequest, "Password does not meet requirements", err.Error())
		return
	}

	// 加密密码
	passwordHash, err := h.passwordService.HashPassword(req.Password)
	if err != nil {
		ErrorResponse(c, http.StatusInternalServerError, "Failed to hash password", err.Error())
		return
	}

	// 创建用户
	user := model.User{
		Username:  req.Username,
		Email:     req.Email,
		Password:  passwordHash,
		Role:      req.Role,
		Status:    req.Status,
		CreatedAt: time.Now(),
		UpdatedAt: time.Now(),
	}

	if err := db.Create(&user).Error; err != nil {
		ErrorResponse(c, http.StatusInternalServerError, "Failed to create user", err.Error())
		return
	}

	// 构建响应
	response := buildUserResponse(&user)
	SuccessResponse(c, "User created successfully", response)
}

// GetUserList 获取用户列表
func (h *UserHandler) GetUserList(c *gin.Context) {
	// 检查权限
	userRole, exists := c.Get("user_role")
	if !exists || userRole != "admin" {
		ErrorResponse(c, http.StatusForbidden, "Insufficient permissions", "ADMIN_REQUIRED")
		return
	}

	var req UserListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		ErrorResponse(c, http.StatusBadRequest, "Invalid query parameters", err.Error())
		return
	}

	db := database.GetDB()
	query := db.Model(&model.User{})

	// 应用筛选条件
	if req.Keyword != "" {
		keyword := "%" + req.Keyword + "%"
		query = query.Where("username LIKE ? OR email LIKE ?", keyword, keyword)
	}

	if req.Role != "" {
		query = query.Where("role = ?", req.Role)
	}

	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	// 计算总数
	var total int64
	query.Count(&total)

	// 应用排序
	orderClause := req.SortBy + " " + req.SortOrder
	query = query.Order(orderClause)

	// 应用分页
	offset := (req.Page - 1) * req.PageSize
	query = query.Offset(offset).Limit(req.PageSize)

	// 查询数据
	var users []model.User
	if err := query.Find(&users).Error; err != nil {
		ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch users", err.Error())
		return
	}

	// 构建响应
	userResponses := make([]UserResponse, len(users))
	for i, user := range users {
		userResponses[i] = buildUserResponse(&user)
	}

	totalPages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))

	response := UserListResponse{
		Users:      userResponses,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}

	SuccessResponse(c, "Users retrieved successfully", response)
}

// GetUser 获取单个用户
func (h *UserHandler) GetUser(c *gin.Context) {
	// 检查权限
	userRole, exists := c.Get("user_role")
	if !exists || userRole != "admin" {
		ErrorResponse(c, http.StatusForbidden, "Insufficient permissions", "ADMIN_REQUIRED")
		return
	}

	idParam := c.Param("id")
	id, err := strconv.ParseUint(idParam, 10, 32)
	if err != nil {
		ErrorResponse(c, http.StatusBadRequest, "Invalid user ID", "INVALID_ID")
		return
	}

	db := database.GetDB()
	var user model.User
	if err := db.First(&user, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			ErrorResponse(c, http.StatusNotFound, "User not found", "USER_NOT_FOUND")
			return
		}
		ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch user", err.Error())
		return
	}

	response := buildUserResponse(&user)
	SuccessResponse(c, "User retrieved successfully", response)
}

// UpdateUser 更新用户
func (h *UserHandler) UpdateUser(c *gin.Context) {
	// 检查权限
	userRole, exists := c.Get("user_role")
	if !exists || userRole != "admin" {
		ErrorResponse(c, http.StatusForbidden, "Insufficient permissions", "ADMIN_REQUIRED")
		return
	}

	idParam := c.Param("id")
	id, err := strconv.ParseUint(idParam, 10, 32)
	if err != nil {
		ErrorResponse(c, http.StatusBadRequest, "Invalid user ID", "INVALID_ID")
		return
	}

	var req UpdateUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ErrorResponse(c, http.StatusBadRequest, "Invalid request format", err.Error())
		return
	}

	db := database.GetDB()
	var user model.User
	if err := db.First(&user, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			ErrorResponse(c, http.StatusNotFound, "User not found", "USER_NOT_FOUND")
			return
		}
		ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch user", err.Error())
		return
	}

	// 防止修改自己的角色和状态
	currentUserID, _ := c.Get("user_id")
	if currentUserID == user.ID {
		if req.Role != "" && req.Role != user.Role {
			ErrorResponse(c, http.StatusForbidden, "Cannot change your own role", "CANNOT_CHANGE_OWN_ROLE")
			return
		}
		if req.Status != "" && req.Status != user.Status {
			ErrorResponse(c, http.StatusForbidden, "Cannot change your own status", "CANNOT_CHANGE_OWN_STATUS")
			return
		}
	}

	// 更新字段
	if req.Username != "" {
		// 检查用户名是否已被其他用户使用
		var existingUser model.User
		if err := db.Where("username = ? AND id != ?", req.Username, user.ID).First(&existingUser).Error; err == nil {
			ErrorResponse(c, http.StatusConflict, "Username already exists", "USERNAME_EXISTS")
			return
		}
		user.Username = req.Username
	}

	if req.Email != "" {
		// 检查邮箱是否已被其他用户使用
		var existingUser model.User
		if err := db.Where("email = ? AND id != ?", req.Email, user.ID).First(&existingUser).Error; err == nil {
			ErrorResponse(c, http.StatusConflict, "Email already exists", "EMAIL_EXISTS")
			return
		}
		user.Email = req.Email
	}

	if req.Role != "" {
		user.Role = req.Role
	}

	if req.Status != "" {
		user.Status = req.Status
	}

	user.UpdatedAt = time.Now()

	if err := db.Save(&user).Error; err != nil {
		ErrorResponse(c, http.StatusInternalServerError, "Failed to update user", err.Error())
		return
	}

	response := buildUserResponse(&user)
	SuccessResponse(c, "User updated successfully", response)
}

// DeleteUser 删除用户
func (h *UserHandler) DeleteUser(c *gin.Context) {
	// 检查权限
	userRole, exists := c.Get("user_role")
	if !exists || userRole != "admin" {
		ErrorResponse(c, http.StatusForbidden, "Insufficient permissions", "ADMIN_REQUIRED")
		return
	}

	idParam := c.Param("id")
	id, err := strconv.ParseUint(idParam, 10, 32)
	if err != nil {
		ErrorResponse(c, http.StatusBadRequest, "Invalid user ID", "INVALID_ID")
		return
	}

	// 防止删除自己
	currentUserID, _ := c.Get("user_id")
	if currentUserID == uint(id) {
		ErrorResponse(c, http.StatusForbidden, "Cannot delete yourself", "CANNOT_DELETE_SELF")
		return
	}

	db := database.GetDB()
	var user model.User
	if err := db.First(&user, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			ErrorResponse(c, http.StatusNotFound, "User not found", "USER_NOT_FOUND")
			return
		}
		ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch user", err.Error())
		return
	}

	// 软删除用户
	if err := db.Delete(&user).Error; err != nil {
		ErrorResponse(c, http.StatusInternalServerError, "Failed to delete user", err.Error())
		return
	}

	SuccessResponse(c, "User deleted successfully", nil)
}

// ChangeUserPassword 修改用户密码
func (h *UserHandler) ChangeUserPassword(c *gin.Context) {
	// 检查权限
	userRole, exists := c.Get("user_role")
	if !exists || userRole != "admin" {
		ErrorResponse(c, http.StatusForbidden, "Insufficient permissions", "ADMIN_REQUIRED")
		return
	}

	idParam := c.Param("id")
	id, err := strconv.ParseUint(idParam, 10, 32)
	if err != nil {
		ErrorResponse(c, http.StatusBadRequest, "Invalid user ID", "INVALID_ID")
		return
	}

	var req ChangeUserPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ErrorResponse(c, http.StatusBadRequest, "Invalid request format", err.Error())
		return
	}

	db := database.GetDB()
	var user model.User
	if err := db.First(&user, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			ErrorResponse(c, http.StatusNotFound, "User not found", "USER_NOT_FOUND")
			return
		}
		ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch user", err.Error())
		return
	}

	// 检查新密码强度
	if err := h.passwordService.IsStrongPassword(req.NewPassword); err != nil {
		ErrorResponse(c, http.StatusBadRequest, "New password does not meet requirements", err.Error())
		return
	}

	// 加密新密码
	newPasswordHash, err := h.passwordService.HashPassword(req.NewPassword)
	if err != nil {
		ErrorResponse(c, http.StatusInternalServerError, "Failed to hash password", err.Error())
		return
	}

	// 更新密码
	user.Password = newPasswordHash
	user.UpdatedAt = time.Now()

	if err := db.Save(&user).Error; err != nil {
		ErrorResponse(c, http.StatusInternalServerError, "Failed to update password", err.Error())
		return
	}

	SuccessResponse(c, "Password changed successfully", nil)
}

// GetUserStats 获取用户统计
func (h *UserHandler) GetUserStats(c *gin.Context) {
	// 检查权限
	userRole, exists := c.Get("user_role")
	if !exists || userRole != "admin" {
		ErrorResponse(c, http.StatusForbidden, "Insufficient permissions", "ADMIN_REQUIRED")
		return
	}

	db := database.GetDB()

	var stats UserStatsResponse

	// 总数
	db.Model(&model.User{}).Count(&stats.Total)

	// 按状态统计
	db.Model(&model.User{}).Where("status = ?", "active").Count(&stats.Active)
	db.Model(&model.User{}).Where("status = ?", "inactive").Count(&stats.Inactive)
	db.Model(&model.User{}).Where("status = ?", "blocked").Count(&stats.Blocked)
	db.Model(&model.User{}).Where("status = ?", "suspended").Count(&stats.Suspended)

	// 按角色统计
	db.Model(&model.User{}).Where("role = ?", "admin").Count(&stats.Admins)
	db.Model(&model.User{}).Where("role = ?", "user").Count(&stats.Users)

	SuccessResponse(c, "User stats retrieved successfully", stats)
}

// buildUserResponse 构建用户响应
func buildUserResponse(user *model.User) UserResponse {
	return UserResponse{
		ID:          user.ID,
		Username:    user.Username,
		Email:       user.Email,
		Role:        user.Role,
		Status:      user.Status,
		CreatedAt:   user.CreatedAt,
		UpdatedAt:   user.UpdatedAt,
		LastLoginAt: user.LastLoginAt,
	}
}
