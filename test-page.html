<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>软件网络授权系统 - 测试页面</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            padding: 40px;
            max-width: 800px;
            width: 90%;
            text-align: center;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 32px;
            font-weight: bold;
        }
        
        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 2.5em;
        }
        
        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 1.2em;
        }
        
        .features {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        
        .feature h3 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .feature p {
            color: #666;
            line-height: 1.6;
        }
        
        .status {
            background: #e8f5e8;
            border: 1px solid #4caf50;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .status h3 {
            color: #2e7d32;
            margin-bottom: 10px;
        }
        
        .status-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-top: 15px;
        }
        
        .status-item {
            background: white;
            padding: 15px;
            border-radius: 8px;
            border-left: 3px solid #4caf50;
        }
        
        .status-item strong {
            color: #2e7d32;
        }
        
        .buttons {
            margin-top: 30px;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 30px;
            margin: 0 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 25px;
            font-weight: 500;
            transition: transform 0.2s, box-shadow 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .btn-secondary {
            background: #6c757d;
        }
        
        .btn-secondary:hover {
            box-shadow: 0 10px 20px rgba(108, 117, 125, 0.3);
        }
        
        .footer {
            margin-top: 30px;
            padding-top: 20px;
            border-top: 1px solid #eee;
            color: #999;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🔐</div>
        <h1>软件网络授权系统</h1>
        <p class="subtitle">企业级软件许可证管理和验证平台</p>
        
        <div class="status">
            <h3>✅ 项目状态：已完成</h3>
            <div class="status-grid">
                <div class="status-item">
                    <strong>后端服务</strong><br>
                    Go + Gin + SQLite
                </div>
                <div class="status-item">
                    <strong>前端界面</strong><br>
                    Vue3 + TypeScript + Arco Design
                </div>
                <div class="status-item">
                    <strong>核心功能</strong><br>
                    100% 完成
                </div>
                <div class="status-item">
                    <strong>API接口</strong><br>
                    45个接口完成
                </div>
            </div>
        </div>
        
        <div class="features">
            <div class="feature">
                <h3>🔐 用户认证</h3>
                <p>JWT令牌认证、角色权限控制、会话管理、密码安全</p>
            </div>
            <div class="feature">
                <h3>📄 许可证管理</h3>
                <p>许可证生成、验证、设备绑定、使用限制、统计监控</p>
            </div>
            <div class="feature">
                <h3>🛡️ 安全防护</h3>
                <p>速率限制、IP白/黑名单、API密钥认证、审计日志</p>
            </div>
            <div class="feature">
                <h3>📊 数据分析</h3>
                <p>使用统计、报告生成、实时监控、数据导出</p>
            </div>
            <div class="feature">
                <h3>🌐 离线验证</h3>
                <p>离线令牌生成、断网验证、数据同步、安全签名</p>
            </div>
            <div class="feature">
                <h3>⚡ 高性能</h3>
                <p>Redis缓存、数据库优化、连接池、查询优化</p>
            </div>
        </div>
        
        <div class="buttons">
            <a href="http://localhost:5173" class="btn" target="_blank">🖥️ 前端界面</a>
            <a href="http://localhost:8080/health" class="btn btn-secondary" target="_blank">🔧 后端API</a>
        </div>
        
        <div class="footer">
            <p>开发完成时间：2025年1月 | 项目状态：✅ 核心功能完成，可投入使用</p>
            <p>默认管理员账户：admin / AdminPass123!</p>
        </div>
    </div>
    
    <script>
        // 检查服务状态
        function checkServices() {
            // 检查前端服务
            fetch('http://localhost:5173')
                .then(() => {
                    console.log('✅ 前端服务运行中');
                })
                .catch(() => {
                    console.log('❌ 前端服务未启动');
                });
            
            // 检查后端服务
            fetch('http://localhost:8080/health')
                .then(() => {
                    console.log('✅ 后端服务运行中');
                })
                .catch(() => {
                    console.log('❌ 后端服务未启动');
                });
        }
        
        // 页面加载后检查服务
        window.addEventListener('load', checkServices);
    </script>
</body>
</html>
