@echo off
echo ========================================
echo 软件网络授权系统 - 开发环境启动
echo ========================================

echo.
echo 1. 启动后端服务器 (端口: 8080)
echo 2. 启动前端开发服务器 (端口: 5173)
echo 3. 同时启动前后端
echo 4. 数据库管理
echo 5. Redis管理
echo 6. 认证系统测试
echo 7. 前端构建工具
echo 8. 退出
echo.

set /p choice=请选择操作 (1-8): 

if "%choice%"=="1" (
    echo 启动后端服务器...
    cd backend
    call scripts\dev.bat
) else if "%choice%"=="2" (
    echo 启动前端开发服务器...
    cd frontend
    call scripts\dev.bat
) else if "%choice%"=="3" (
    echo 同时启动前后端服务器...
    start "Backend Server" cmd /c "cd backend && call scripts\dev.bat"
    timeout /t 3 /nobreak >nul
    start "Frontend Server" cmd /c "cd frontend && call scripts\dev.bat"
    echo.
    echo 前后端服务器已在新窗口中启动
    echo 后端地址: http://localhost:8080
    echo 前端地址: http://localhost:5173
    echo.
    pause
) else if "%choice%"=="4" (
    echo 数据库管理...
    cd backend
    call scripts\db.bat
) else if "%choice%"=="5" (
    echo Redis管理...
    cd backend
    call scripts\redis.bat
) else if "%choice%"=="6" (
    echo 认证系统测试...
    cd backend
    call scripts\auth.bat
) else if "%choice%"=="7" (
    echo 前端构建工具...
    cd frontend
    call scripts\build.bat
) else if "%choice%"=="8" (
    exit
) else (
    echo 无效选择，请重新运行脚本
    pause
)