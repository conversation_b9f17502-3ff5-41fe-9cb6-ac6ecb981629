package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"time"
)

const baseURL = "http://localhost:8080/api/v1"
const apiKey = "test-api-key-123" // 测试API密钥

type LoginRequest struct {
	Username   string `json:"username"`
	Password   string `json:"password"`
	RememberMe bool   `json:"remember_me"`
}

type LoginResponse struct {
	Message string `json:"message"`
	Data    struct {
		User struct {
			ID       uint   `json:"id"`
			Username string `json:"username"`
			Email    string `json:"email"`
			Role     string `json:"role"`
			Status   string `json:"status"`
		} `json:"user"`
		Tokens struct {
			AccessToken  string `json:"access_token"`
			RefreshToken string `json:"refresh_token"`
			TokenType    string `json:"token_type"`
			ExpiresIn    int    `json:"expires_in"`
		} `json:"tokens"`
	} `json:"data"`
}

func main() {
	fmt.Println("Comprehensive API Test Suite")
	fmt.Println("============================")
	fmt.Printf("Base URL: %s\n", baseURL)
	fmt.Printf("API Key: %s\n", apiKey)

	// 1. 测试健康检查
	fmt.Println("\n1. Testing Health Check...")
	if err := testHealthCheck(); err != nil {
		log.Printf("❌ Health check failed: %v", err)
	} else {
		fmt.Println("✅ Health check passed")
	}

	// 2. 测试认证系统
	fmt.Println("\n2. Testing Authentication System...")
	accessToken, err := testLogin()
	if err != nil {
		log.Printf("❌ Authentication test failed: %v", err)
		return
	}
	fmt.Println("✅ Authentication system working")

	// 3. 测试用户管理
	fmt.Println("\n3. Testing User Management...")
	if err := testUserManagement(accessToken); err != nil {
		log.Printf("❌ User management test failed: %v", err)
	} else {
		fmt.Println("✅ User management working")
	}

	// 4. 测试许可证管理
	fmt.Println("\n4. Testing License Management...")
	licenseKey, err := testLicenseManagement(accessToken)
	if err != nil {
		log.Printf("❌ License management test failed: %v", err)
	} else {
		fmt.Println("✅ License management working")
	}

	// 5. 测试许可证验证
	if licenseKey != "" {
		fmt.Println("\n5. Testing License Verification...")
		if err := testLicenseVerification(licenseKey); err != nil {
			log.Printf("❌ License verification test failed: %v", err)
		} else {
			fmt.Println("✅ License verification working")
		}

		// 6. 测试离线验证
		fmt.Println("\n6. Testing Offline Verification...")
		if err := testOfflineVerification(licenseKey); err != nil {
			log.Printf("❌ Offline verification test failed: %v", err)
		} else {
			fmt.Println("✅ Offline verification working")
		}
	}

	// 7. 测试安全功能
	fmt.Println("\n7. Testing Security Features...")
	if err := testSecurityFeatures(accessToken); err != nil {
		log.Printf("❌ Security features test failed: %v", err)
	} else {
		fmt.Println("✅ Security features working")
	}

	fmt.Println("\n🎉 Comprehensive API test completed!")
	fmt.Println("\n📊 Test Summary:")
	fmt.Println("   ✅ Health Check")
	fmt.Println("   ✅ Authentication System")
	fmt.Println("   ✅ User Management")
	fmt.Println("   ✅ License Management")
	if licenseKey != "" {
		fmt.Println("   ✅ License Verification")
		fmt.Println("   ✅ Offline Verification")
	}
	fmt.Println("   ✅ Security Features")
}

func testHealthCheck() error {
	resp, err := http.Get(baseURL[:len(baseURL)-7] + "/health") // 移除 /api/v1
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("health check failed with status %d", resp.StatusCode)
	}

	body, _ := io.ReadAll(resp.Body)
	fmt.Printf("   Health Status: %s\n", string(body))
	return nil
}

func testLogin() (string, error) {
	req := LoginRequest{
		Username:   "admin",
		Password:   "AdminPass123!",
		RememberMe: true,
	}

	jsonData, _ := json.Marshal(req)
	resp, err := http.Post(baseURL+"/auth/login", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("login failed with status %d: %s", resp.StatusCode, string(body))
	}

	var loginResp LoginResponse
	if err := json.Unmarshal(body, &loginResp); err != nil {
		return "", err
	}

	fmt.Printf("   Logged in as: %s (%s)\n", loginResp.Data.User.Username, loginResp.Data.User.Role)
	return loginResp.Data.Tokens.AccessToken, nil
}

func testUserManagement(accessToken string) error {
	// 测试获取用户列表
	client := &http.Client{}
	req, _ := http.NewRequest("GET", baseURL+"/users?page=1&page_size=5", nil)
	req.Header.Set("Authorization", "Bearer "+accessToken)

	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		body, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("get users failed with status %d: %s", resp.StatusCode, string(body))
	}

	var response struct {
		Data struct {
			Total int64 `json:"total"`
		} `json:"data"`
	}

	body, _ := io.ReadAll(resp.Body)
	json.Unmarshal(body, &response)

	fmt.Printf("   Total users: %d\n", response.Data.Total)
	return nil
}

func testLicenseManagement(accessToken string) (string, error) {
	// 创建测试许可证
	createReq := map[string]interface{}{
		"customer_name":   "Test Customer",
		"customer_email":  "<EMAIL>",
		"product_name":    "Test Software",
		"product_version": "1.0.0",
		"license_type":    "professional",
		"max_devices":     5,
		"features":        []string{"feature1", "feature2"},
		"expires_at":      time.Now().AddDate(1, 0, 0).Format(time.RFC3339),
		"notes":           "Test license for comprehensive testing",
	}

	jsonData, _ := json.Marshal(createReq)
	client := &http.Client{}
	httpReq, _ := http.NewRequest("POST", baseURL+"/licenses", bytes.NewBuffer(jsonData))
	httpReq.Header.Set("Authorization", "Bearer "+accessToken)
	httpReq.Header.Set("Content-Type", "application/json")

	resp, err := client.Do(httpReq)
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("create license failed with status %d: %s", resp.StatusCode, string(body))
	}

	var response struct {
		Data struct {
			LicenseKey string `json:"license_key"`
		} `json:"data"`
	}

	if err := json.Unmarshal(body, &response); err != nil {
		return "", err
	}

	fmt.Printf("   Created license: %s\n", response.Data.LicenseKey)
	return response.Data.LicenseKey, nil
}

func testLicenseVerification(licenseKey string) error {
	verifyReq := map[string]interface{}{
		"license_key":     licenseKey,
		"device_id":       "TEST-DEVICE-001",
		"device_name":     "Test Computer",
		"machine_info":    "Windows 10 Pro",
		"product_name":    "Test Software",
		"product_version": "1.0.0",
	}

	jsonData, _ := json.Marshal(verifyReq)
	client := &http.Client{}
	httpReq, _ := http.NewRequest("POST", baseURL+"/verify/license", bytes.NewBuffer(jsonData))
	httpReq.Header.Set("X-API-Key", apiKey)
	httpReq.Header.Set("Content-Type", "application/json")

	resp, err := client.Do(httpReq)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("verify license failed with status %d: %s", resp.StatusCode, string(body))
	}

	var response struct {
		Valid   bool   `json:"valid"`
		Message string `json:"message"`
	}

	if err := json.Unmarshal(body, &response); err != nil {
		return err
	}

	if !response.Valid {
		return fmt.Errorf("license verification failed: %s", response.Message)
	}

	fmt.Printf("   License verification: %s\n", response.Message)
	return nil
}

func testOfflineVerification(licenseKey string) error {
	// 生成离线令牌
	generateReq := map[string]interface{}{
		"license_key": licenseKey,
		"device_id":   "TEST-DEVICE-001",
		"valid_days":  30,
	}

	jsonData, _ := json.Marshal(generateReq)
	client := &http.Client{}
	httpReq, _ := http.NewRequest("POST", baseURL+"/offline/generate-token", bytes.NewBuffer(jsonData))
	httpReq.Header.Set("X-API-Key", apiKey)
	httpReq.Header.Set("Content-Type", "application/json")

	resp, err := client.Do(httpReq)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("generate offline token failed with status %d: %s", resp.StatusCode, string(body))
	}

	var generateResp struct {
		Data struct {
			Token string `json:"token"`
		} `json:"data"`
	}

	if err := json.Unmarshal(body, &generateResp); err != nil {
		return err
	}

	// 验证离线令牌
	verifyReq := map[string]interface{}{
		"token":        generateResp.Data.Token,
		"device_id":    "TEST-DEVICE-001",
		"product_name": "Test Software",
	}

	jsonData, _ = json.Marshal(verifyReq)
	resp, err = http.Post(baseURL+"/offline/verify", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err = io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("verify offline token failed with status %d: %s", resp.StatusCode, string(body))
	}

	var verifyResp struct {
		Valid   bool   `json:"valid"`
		Message string `json:"message"`
	}

	if err := json.Unmarshal(body, &verifyResp); err != nil {
		return err
	}

	if !verifyResp.Valid {
		return fmt.Errorf("offline token verification failed: %s", verifyResp.Message)
	}

	fmt.Printf("   Offline token generated and verified successfully\n")
	return nil
}

func testSecurityFeatures(accessToken string) error {
	// 测试获取安全统计
	client := &http.Client{}
	req, _ := http.NewRequest("GET", baseURL+"/security/stats", nil)
	req.Header.Set("Authorization", "Bearer "+accessToken)

	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		b2, _ := io.ReadAll(resp.Body)
		return fmt.Errorf("get security stats failed with status %d: %s", resp.StatusCode, string(b2))
	}

	fmt.Printf("   Security stats retrieved successfully\n")
	return nil
}
