@echo off
chcp 65001 >nul
echo ========================================
echo Redis Setup for Windows
echo ========================================

echo.
echo This script will help you install and start Redis on Windows
echo.

echo Checking if Redis is already installed...
redis-server --version >nul 2>&1
if not errorlevel 1 (
    echo Redis is already installed!
    goto :start_redis
)

echo Redis not found. Installing Redis...
echo.

echo Checking if Chocolatey is installed...
choco --version >nul 2>&1
if errorlevel 1 (
    echo Chocolatey not found. Please install Chocolatey first:
    echo https://chocolatey.org/install
    echo.
    echo Or download Redis manually from:
    echo https://github.com/microsoftarchive/redis/releases
    pause
    exit /b 1
)

echo Installing Redis using Chocolatey...
choco install redis-64 -y

if errorlevel 1 (
    echo Failed to install Redis via Chocolatey
    echo Please install manually from: https://github.com/microsoftarchive/redis/releases
    pause
    exit /b 1
)

:start_redis
echo.
echo Starting Redis server...
echo Press Ctrl+C to stop Redis when needed
echo.

start "Redis Server" redis-server
timeout /t 3 >nul

echo Testing Redis connection...
redis-cli ping >nul 2>&1
if not errorlevel 1 (
    echo ✅ Redis is running successfully!
    echo You can now run: go run cmd/redis-test/main.go
) else (
    echo ❌ Redis failed to start properly
    echo Please check the Redis server window for errors
)

echo.
echo Redis server is running in a separate window
echo Keep that window open while using the application
pause