import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios'
import { useAuthStore } from '@/stores/auth'
import { useAppStore } from '@/stores/app'
import type { ApiResponse, ApiErrorResponse } from '@/types/auth'

// 创建axios实例
const http: AxiosInstance = axios.create({
  baseURL: '/api/v1',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
http.interceptors.request.use(
  (config) => {
    const authStore = useAuthStore()
    
    // 添加认证头
    if (authStore.token) {
      config.headers.Authorization = `Bearer ${authStore.token}`
    }
    
    return config
  },
  (error) => {
    return Promise.reject(error)
  }
)

// 响应拦截器
http.interceptors.response.use(
  (response: AxiosResponse<ApiResponse>) => {
    return response
  },
  async (error) => {
    const authStore = useAuthStore()
    const appStore = useAppStore()
    
    if (error.response) {
      const { status, data } = error.response
      
      switch (status) {
        case 401:
          // 未授权，尝试刷新令牌
          if (data.code === 'TOKEN_EXPIRED' && authStore.refreshToken) {
            try {
              const success = await authStore.refreshAccessToken()
              if (success) {
                // 重新发送原请求
                return http.request(error.config)
              }
            } catch (refreshError) {
              console.error('Token refresh failed:', refreshError)
            }
          }
          
          // 刷新失败或其他认证错误，跳转到登录页
          await authStore.logout()
          if (window.location.pathname !== '/login') {
            window.location.href = '/login'
          }
          break
          
        case 403:
          appStore.showError('权限不足，无法访问该资源')
          break
          
        case 404:
          appStore.showError('请求的资源不存在')
          break
          
        case 429:
          appStore.showError('请求过于频繁，请稍后再试')
          break
          
        case 500:
          appStore.showError('服务器内部错误，请稍后再试')
          break
          
        default:
          const errorMessage = data?.message || data?.error || '请求失败'
          appStore.showError(errorMessage)
      }
    } else if (error.request) {
      // 网络错误
      appStore.showError('网络连接失败，请检查网络设置')
    } else {
      // 其他错误
      appStore.showError('请求配置错误')
    }
    
    return Promise.reject(error)
  }
)

// 通用请求方法
export const request = {
  get: <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> => {
    return http.get(url, config)
  },
  
  post: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> => {
    return http.post(url, data, config)
  },
  
  put: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> => {
    return http.put(url, data, config)
  },
  
  delete: <T = any>(url: string, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> => {
    return http.delete(url, config)
  },
  
  patch: <T = any>(url: string, data?: any, config?: AxiosRequestConfig): Promise<AxiosResponse<ApiResponse<T>>> => {
    return http.patch(url, data, config)
  }
}

export default http