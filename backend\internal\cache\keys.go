package cache

import (
	"fmt"
	"time"
)

// 缓存键前缀常量
const (
	PrefixAuth         = "auth"
	PrefixLicense      = "license"
	PrefixUser         = "user"
	PrefixStats        = "stats"
	PrefixRateLimit    = "rate_limit"
	PrefixVerification = "verification"
	PrefixDevice       = "device"
)

// 缓存TTL常量
const (
	TTLAuthToken      = 24 * time.Hour  // JWT令牌缓存
	TTLLicenseVerify  = 1 * time.Hour   // 许可证验证结果
	TTLLicenseInfo    = 30 * time.Minute // 许可证信息
	TTLUserSession    = 24 * time.Hour  // 用户会话信息
	TTLStatsDaily     = 7 * 24 * time.Hour // 每日统计数据
	TTLRateLimit      = 1 * time.Minute // API限流
	TTLDeviceBinding  = 2 * time.Hour   // 设备绑定信息
	TTLOfflineToken   = 72 * time.Hour  // 离线验证令牌
)

// CacheKeys 缓存键生成器
type CacheKeys struct{}

// NewCacheKeys 创建缓存键生成器
func NewCacheKeys() *CacheKeys {
	return &CacheKeys{}
}

// AuthToken JWT令牌缓存键
func (k *CacheKeys) AuthToken(tokenID string) string {
	return fmt.Sprintf("%s:token:%s", PrefixAuth, tokenID)
}

// UserSession 用户会话缓存键
func (k *CacheKeys) UserSession(userID uint) string {
	return fmt.Sprintf("%s:session:%d", PrefixUser, userID)
}

// LicenseVerify 许可证验证结果缓存键
func (k *CacheKeys) LicenseVerify(licenseKey string) string {
	return fmt.Sprintf("%s:verify:%s", PrefixLicense, licenseKey)
}

// LicenseInfo 许可证信息缓存键
func (k *CacheKeys) LicenseInfo(licenseKey string) string {
	return fmt.Sprintf("%s:info:%s", PrefixLicense, licenseKey)
}

// DeviceBinding 设备绑定缓存键
func (k *CacheKeys) DeviceBinding(licenseKey, deviceID string) string {
	return fmt.Sprintf("%s:binding:%s:%s", PrefixDevice, licenseKey, deviceID)
}

// RateLimit API限流缓存键
func (k *CacheKeys) RateLimit(ip, endpoint string) string {
	return fmt.Sprintf("%s:%s:%s", PrefixRateLimit, ip, endpoint)
}

// StatsDaily 每日统计缓存键
func (k *CacheKeys) StatsDaily(date string) string {
	return fmt.Sprintf("%s:daily:%s", PrefixStats, date)
}

// StatsHourly 每小时统计缓存键
func (k *CacheKeys) StatsHourly(datetime string) string {
	return fmt.Sprintf("%s:hourly:%s", PrefixStats, datetime)
}

// VerificationResult 验证结果缓存键
func (k *CacheKeys) VerificationResult(licenseKey, deviceID string) string {
	return fmt.Sprintf("%s:result:%s:%s", PrefixVerification, licenseKey, deviceID)
}

// OfflineToken 离线验证令牌缓存键
func (k *CacheKeys) OfflineToken(licenseKey string) string {
	return fmt.Sprintf("%s:offline:%s", PrefixLicense, licenseKey)
}

// UserInfo 用户信息缓存键
func (k *CacheKeys) UserInfo(userID uint) string {
	return fmt.Sprintf("%s:info:%d", PrefixUser, userID)
}

// LicenseList 许可证列表缓存键
func (k *CacheKeys) LicenseList(userID uint, page, limit int) string {
	return fmt.Sprintf("%s:list:%d:%d:%d", PrefixLicense, userID, page, limit)
}

// DeviceList 设备列表缓存键
func (k *CacheKeys) DeviceList(licenseKey string) string {
	return fmt.Sprintf("%s:list:%s", PrefixDevice, licenseKey)
}