package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"time"
)

const baseURL = "http://localhost:8080/api/v1"

type VerifyLicenseRequest struct {
	LicenseKey     string `json:"license_key"`
	DeviceID       string `json:"device_id"`
	DeviceName     string `json:"device_name"`
	MachineInfo    string `json:"machine_info"`
	ProductName    string `json:"product_name"`
	ProductVersion string `json:"product_version"`
	ClientIP       string `json:"client_ip"`
}

type VerifyLicenseResponse struct {
	Valid          bool      `json:"valid"`
	LicenseKey     string    `json:"license_key"`
	CustomerName   string    `json:"customer_name"`
	ProductName    string    `json:"product_name"`
	ProductVersion string    `json:"product_version"`
	LicenseType    string    `json:"license_type"`
	Features       []string  `json:"features"`
	MaxDevices     int       `json:"max_devices"`
	DeviceCount    int       `json:"device_count"`
	ExpiresAt      time.Time `json:"expires_at"`
	DaysRemaining  int       `json:"days_remaining"`
	Status         string    `json:"status"`
	Message        string    `json:"message"`
	OfflineToken   string    `json:"offline_token,omitempty"`
}

type DeviceBindingRequest struct {
	LicenseKey  string `json:"license_key"`
	DeviceID    string `json:"device_id"`
	DeviceName  string `json:"device_name"`
	MachineInfo string `json:"machine_info"`
}

type UnbindDeviceRequest struct {
	LicenseKey string `json:"license_key"`
	DeviceID   string `json:"device_id"`
}

func main() {
	fmt.Println("License Verification API Test Tool")
	fmt.Println("==================================")

	// 测试用的许可证密钥（需要先创建一个许可证）
	licenseKey := "PRO-1234-5678-9ABC-DEF0" // 这个需要替换为实际的许可证密钥
	deviceID := "TEST-DEVICE-001"
	deviceName := "Test Computer"
	machineInfo := "Windows 10 Pro, Intel i7-8700K, 16GB RAM"
	productName := "Test Software"
	productVersion := "1.0.0"

	fmt.Printf("Testing with License Key: %s\n", licenseKey)
	fmt.Printf("Device ID: %s\n", deviceID)

	// 1. 测试许可证验证
	fmt.Println("\n1. Testing License Verification...")
	if err := testVerifyLicense(licenseKey, deviceID, deviceName, machineInfo, productName, productVersion); err != nil {
		log.Printf("❌ License verification failed: %v", err)
	} else {
		fmt.Println("✅ License verification successful")
	}

	// 2. 测试设备绑定
	fmt.Println("\n2. Testing Device Binding...")
	if err := testBindDevice(licenseKey, deviceID+"_BIND", deviceName+" (Bind Test)", machineInfo); err != nil {
		log.Printf("❌ Device binding failed: %v", err)
	} else {
		fmt.Println("✅ Device binding successful")
	}

	// 3. 测试获取设备列表
	fmt.Println("\n3. Testing Get Device List...")
	if err := testGetDeviceList(licenseKey); err != nil {
		log.Printf("❌ Get device list failed: %v", err)
	} else {
		fmt.Println("✅ Device list retrieved successfully")
	}

	// 4. 测试设备解绑
	fmt.Println("\n4. Testing Device Unbinding...")
	if err := testUnbindDevice(licenseKey, deviceID+"_BIND"); err != nil {
		log.Printf("❌ Device unbinding failed: %v", err)
	} else {
		fmt.Println("✅ Device unbinding successful")
	}

	// 5. 测试无效许可证
	fmt.Println("\n5. Testing Invalid License...")
	if err := testVerifyLicense("INVALID-LICENSE-KEY", deviceID, deviceName, machineInfo, productName, productVersion); err != nil {
		fmt.Println("✅ Invalid license correctly rejected")
	} else {
		fmt.Println("❌ Invalid license should be rejected")
	}

	// 6. 测试产品不匹配
	fmt.Println("\n6. Testing Product Mismatch...")
	if err := testVerifyLicense(licenseKey, deviceID, deviceName, machineInfo, "Wrong Product", productVersion); err != nil {
		fmt.Println("✅ Product mismatch correctly rejected")
	} else {
		fmt.Println("❌ Product mismatch should be rejected")
	}

	fmt.Println("\n🎉 All verification API tests completed!")
	fmt.Println("\n📝 Note: Some tests may fail if the license key doesn't exist.")
	fmt.Println("   Please create a license first using the license management API.")
}

func testVerifyLicense(licenseKey, deviceID, deviceName, machineInfo, productName, productVersion string) error {
	req := VerifyLicenseRequest{
		LicenseKey:     licenseKey,
		DeviceID:       deviceID,
		DeviceName:     deviceName,
		MachineInfo:    machineInfo,
		ProductName:    productName,
		ProductVersion: productVersion,
	}

	jsonData, _ := json.Marshal(req)
	resp, err := http.Post(baseURL+"/verify/license", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("verify license failed with status %d: %s", resp.StatusCode, string(body))
	}

	var response VerifyLicenseResponse
	if err := json.Unmarshal(body, &response); err != nil {
		return err
	}

	fmt.Printf("   Valid: %v\n", response.Valid)
	fmt.Printf("   Status: %s\n", response.Status)
	fmt.Printf("   Message: %s\n", response.Message)
	
	if response.Valid {
		fmt.Printf("   Customer: %s\n", response.CustomerName)
		fmt.Printf("   License Type: %s\n", response.LicenseType)
		fmt.Printf("   Features: %v\n", response.Features)
		fmt.Printf("   Devices: %d/%d\n", response.DeviceCount, response.MaxDevices)
		fmt.Printf("   Days Remaining: %d\n", response.DaysRemaining)
		if response.OfflineToken != "" {
			fmt.Printf("   Offline Token: %s...\n", response.OfflineToken[:20])
		}
	}

	if !response.Valid {
		return fmt.Errorf("license verification failed: %s", response.Message)
	}

	return nil
}

func testBindDevice(licenseKey, deviceID, deviceName, machineInfo string) error {
	req := DeviceBindingRequest{
		LicenseKey:  licenseKey,
		DeviceID:    deviceID,
		DeviceName:  deviceName,
		MachineInfo: machineInfo,
	}

	jsonData, _ := json.Marshal(req)
	resp, err := http.Post(baseURL+"/verify/bind", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("bind device failed with status %d: %s", resp.StatusCode, string(body))
	}

	var response struct {
		Success    bool      `json:"success"`
		DeviceID   string    `json:"device_id"`
		DeviceName string    `json:"device_name"`
		BoundAt    time.Time `json:"bound_at"`
		Message    string    `json:"message"`
	}

	if err := json.Unmarshal(body, &response); err != nil {
		return err
	}

	fmt.Printf("   Success: %v\n", response.Success)
	fmt.Printf("   Device: %s (%s)\n", response.DeviceName, response.DeviceID)
	fmt.Printf("   Bound At: %s\n", response.BoundAt.Format("2006-01-02 15:04:05"))
	fmt.Printf("   Message: %s\n", response.Message)

	return nil
}

func testGetDeviceList(licenseKey string) error {
	url := fmt.Sprintf("%s/verify/devices?license_key=%s", baseURL, licenseKey)
	resp, err := http.Get(url)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("get device list failed with status %d: %s", resp.StatusCode, string(body))
	}

	var response struct {
		Message string `json:"message"`
		Data    struct {
			LicenseKey  string `json:"license_key"`
			MaxDevices  int    `json:"max_devices"`
			DeviceCount int    `json:"device_count"`
			Devices     []struct {
				ID          uint      `json:"id"`
				DeviceID    string    `json:"device_id"`
				DeviceName  string    `json:"device_name"`
				MachineInfo string    `json:"machine_info"`
				BoundAt     time.Time `json:"bound_at"`
				LastSeenAt  time.Time `json:"last_seen_at"`
				Status      string    `json:"status"`
			} `json:"devices"`
		} `json:"data"`
	}

	if err := json.Unmarshal(body, &response); err != nil {
		return err
	}

	fmt.Printf("   License: %s\n", response.Data.LicenseKey)
	fmt.Printf("   Devices: %d/%d\n", response.Data.DeviceCount, response.Data.MaxDevices)
	
	for i, device := range response.Data.Devices {
		fmt.Printf("   Device %d:\n", i+1)
		fmt.Printf("     ID: %s\n", device.DeviceID)
		fmt.Printf("     Name: %s\n", device.DeviceName)
		fmt.Printf("     Status: %s\n", device.Status)
		fmt.Printf("     Bound: %s\n", device.BoundAt.Format("2006-01-02 15:04:05"))
		fmt.Printf("     Last Seen: %s\n", device.LastSeenAt.Format("2006-01-02 15:04:05"))
	}

	return nil
}

func testUnbindDevice(licenseKey, deviceID string) error {
	req := UnbindDeviceRequest{
		LicenseKey: licenseKey,
		DeviceID:   deviceID,
	}

	jsonData, _ := json.Marshal(req)
	resp, err := http.Post(baseURL+"/verify/unbind", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("unbind device failed with status %d: %s", resp.StatusCode, string(body))
	}

	var response struct {
		Message string `json:"message"`
		Data    struct {
			DeviceID   string `json:"device_id"`
			LicenseKey string `json:"license_key"`
		} `json:"data"`
	}

	if err := json.Unmarshal(body, &response); err != nil {
		return err
	}

	fmt.Printf("   Message: %s\n", response.Message)
	fmt.Printf("   Device: %s\n", response.Data.DeviceID)
	fmt.Printf("   License: %s\n", response.Data.LicenseKey)

	return nil
}