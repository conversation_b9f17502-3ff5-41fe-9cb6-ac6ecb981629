<template>
  <div style="padding: 20px; background: white; min-height: 100vh;">
    <h1 style="color: #1890ff;">🎉 登录成功！</h1>
    <p>欢迎来到软件网络授权系统</p>
    <p>当前用户: {{ username }}</p>
    <p>当前时间: {{ currentTime }}</p>
    
    <div style="margin-top: 20px;">
      <button @click="goToLicenses" style="margin-right: 10px; padding: 10px 20px; background: #1890ff; color: white; border: none; border-radius: 4px; cursor: pointer;">
        许可证管理
      </button>
      <button @click="logout" style="padding: 10px 20px; background: #ff4d4f; color: white; border: none; border-radius: 4px; cursor: pointer;">
        退出登录
      </button>
    </div>

    <div style="margin-top: 30px; padding: 20px; background: #f5f5f5; border-radius: 8px;">
      <h3>系统信息</h3>
      <ul>
        <li>路由状态: 正常</li>
        <li>页面加载: 成功</li>
        <li>用户认证: 已登录</li>
        <li>本地存储: {{ storageInfo }}</li>
      </ul>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const username = ref('')
const currentTime = ref('')
const storageInfo = ref('')

const updateTime = () => {
  currentTime.value = new Date().toLocaleString()
}

const checkStorage = () => {
  const isLoggedIn = localStorage.getItem('isLoggedIn')
  const storedUsername = localStorage.getItem('username')
  username.value = storedUsername || '未知用户'
  storageInfo.value = `登录状态: ${isLoggedIn}, 用户名: ${storedUsername}`
}

const goToLicenses = () => {
  router.push('/licenses')
}

const logout = () => {
  localStorage.removeItem('isLoggedIn')
  localStorage.removeItem('username')
  alert('已退出登录')
  router.push('/login')
}

onMounted(() => {
  console.log('TestDashboard 组件已挂载')
  updateTime()
  checkStorage()
  
  // 每秒更新时间
  setInterval(updateTime, 1000)
})
</script>