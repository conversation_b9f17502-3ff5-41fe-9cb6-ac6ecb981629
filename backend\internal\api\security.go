package api

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"software-auth/internal/middleware"
)

// SecurityHandler 安全管理处理器
type SecurityHandler struct{}

// NewSecurityHandler 创建安全管理处理器
func NewSecurityHandler() *SecurityHandler {
	return &SecurityHandler{}
}

// AddIPToWhitelistRequest 添加IP到白名单请求
type AddIPToWhitelistRequest struct {
	IP string `json:"ip" binding:"required"`
}

// AddIPToBlacklistRequest 添加IP到黑名单请求
type AddIPToBlacklistRequest struct {
	IP       string `json:"ip" binding:"required"`
	Duration int    `json:"duration" binding:"required,min=1"` // 分钟
}

// CreateAPIKeyRequest 创建API密钥请求
type CreateAPIKeyRequest struct {
	Name        string   `json:"name" binding:"required"`
	Permissions []string `json:"permissions" binding:"required"`
}

// GetSecurityStats 获取安全统计
func (h *SecurityHandler) GetSecurityStats(c *gin.Context) {
	// 检查权限
	userRole, exists := c.Get("user_role")
	if !exists || userRole != "admin" {
		ErrorResponse(c, http.StatusForbidden, "Insufficient permissions", "ADMIN_REQUIRED")
		return
	}

	rateLimiter := middleware.GetRateLimiter()
	
	stats := gin.H{
		"rate_limiter": gin.H{
			"active_limiters": len(rateLimiter.GetActiveLimiters()),
			"rate_per_second": float64(rateLimiter.GetRate()),
			"burst_size":      rateLimiter.GetBurst(),
		},
		"ip_whitelist": gin.H{
			"count": middleware.GetIPWhitelist().GetCount(),
		},
		"ip_blacklist": gin.H{
			"count": middleware.GetIPBlacklist().GetCount(),
		},
		"api_keys": gin.H{
			"count": middleware.GetAPIKeyAuth().GetCount(),
		},
	}

	SuccessResponse(c, "Security stats retrieved successfully", stats)
}

// GetIPWhitelist 获取IP白名单
func (h *SecurityHandler) GetIPWhitelist(c *gin.Context) {
	// 检查权限
	userRole, exists := c.Get("user_role")
	if !exists || userRole != "admin" {
		ErrorResponse(c, http.StatusForbidden, "Insufficient permissions", "ADMIN_REQUIRED")
		return
	}

	whitelist := middleware.GetIPWhitelist()
	ips := whitelist.GetAllIPs()

	SuccessResponse(c, "IP whitelist retrieved successfully", gin.H{
		"ips":   ips,
		"count": len(ips),
	})
}

// AddIPToWhitelist 添加IP到白名单
func (h *SecurityHandler) AddIPToWhitelist(c *gin.Context) {
	// 检查权限
	userRole, exists := c.Get("user_role")
	if !exists || userRole != "admin" {
		ErrorResponse(c, http.StatusForbidden, "Insufficient permissions", "ADMIN_REQUIRED")
		return
	}

	var req AddIPToWhitelistRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ErrorResponse(c, http.StatusBadRequest, "Invalid request format", err.Error())
		return
	}

	whitelist := middleware.GetIPWhitelist()
	whitelist.AddIP(req.IP)

	SuccessResponse(c, "IP added to whitelist successfully", gin.H{
		"ip": req.IP,
	})
}

// RemoveIPFromWhitelist 从白名单移除IP
func (h *SecurityHandler) RemoveIPFromWhitelist(c *gin.Context) {
	// 检查权限
	userRole, exists := c.Get("user_role")
	if !exists || userRole != "admin" {
		ErrorResponse(c, http.StatusForbidden, "Insufficient permissions", "ADMIN_REQUIRED")
		return
	}

	ip := c.Param("ip")
	if ip == "" {
		ErrorResponse(c, http.StatusBadRequest, "IP parameter is required", "MISSING_IP")
		return
	}

	whitelist := middleware.GetIPWhitelist()
	whitelist.RemoveIP(ip)

	SuccessResponse(c, "IP removed from whitelist successfully", gin.H{
		"ip": ip,
	})
}

// GetIPBlacklist 获取IP黑名单
func (h *SecurityHandler) GetIPBlacklist(c *gin.Context) {
	// 检查权限
	userRole, exists := c.Get("user_role")
	if !exists || userRole != "admin" {
		ErrorResponse(c, http.StatusForbidden, "Insufficient permissions", "ADMIN_REQUIRED")
		return
	}

	blacklist := middleware.GetIPBlacklist()
	ips := blacklist.GetAllIPs()

	SuccessResponse(c, "IP blacklist retrieved successfully", gin.H{
		"ips":   ips,
		"count": len(ips),
	})
}

// AddIPToBlacklist 添加IP到黑名单
func (h *SecurityHandler) AddIPToBlacklist(c *gin.Context) {
	// 检查权限
	userRole, exists := c.Get("user_role")
	if !exists || userRole != "admin" {
		ErrorResponse(c, http.StatusForbidden, "Insufficient permissions", "ADMIN_REQUIRED")
		return
	}

	var req AddIPToBlacklistRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ErrorResponse(c, http.StatusBadRequest, "Invalid request format", err.Error())
		return
	}

	blacklist := middleware.GetIPBlacklist()
	duration := time.Duration(req.Duration) * time.Minute
	blacklist.AddIP(req.IP, duration)

	SuccessResponse(c, "IP added to blacklist successfully", gin.H{
		"ip":       req.IP,
		"duration": req.Duration,
		"expires_at": time.Now().Add(duration),
	})
}

// RemoveIPFromBlacklist 从黑名单移除IP
func (h *SecurityHandler) RemoveIPFromBlacklist(c *gin.Context) {
	// 检查权限
	userRole, exists := c.Get("user_role")
	if !exists || userRole != "admin" {
		ErrorResponse(c, http.StatusForbidden, "Insufficient permissions", "ADMIN_REQUIRED")
		return
	}

	ip := c.Param("ip")
	if ip == "" {
		ErrorResponse(c, http.StatusBadRequest, "IP parameter is required", "MISSING_IP")
		return
	}

	blacklist := middleware.GetIPBlacklist()
	blacklist.RemoveIP(ip)

	SuccessResponse(c, "IP removed from blacklist successfully", gin.H{
		"ip": ip,
	})
}

// GetAPIKeys 获取API密钥列表
func (h *SecurityHandler) GetAPIKeys(c *gin.Context) {
	// 检查权限
	userRole, exists := c.Get("user_role")
	if !exists || userRole != "admin" {
		ErrorResponse(c, http.StatusForbidden, "Insufficient permissions", "ADMIN_REQUIRED")
		return
	}

	apiKeyAuth := middleware.GetAPIKeyAuth()
	keys := apiKeyAuth.GetAllKeys()

	SuccessResponse(c, "API keys retrieved successfully", gin.H{
		"keys":  keys,
		"count": len(keys),
	})
}

// CreateAPIKey 创建API密钥
func (h *SecurityHandler) CreateAPIKey(c *gin.Context) {
	// 检查权限
	userRole, exists := c.Get("user_role")
	if !exists || userRole != "admin" {
		ErrorResponse(c, http.StatusForbidden, "Insufficient permissions", "ADMIN_REQUIRED")
		return
	}

	var req CreateAPIKeyRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ErrorResponse(c, http.StatusBadRequest, "Invalid request format", err.Error())
		return
	}

	// 生成API密钥
	apiKey := generateAPIKey()
	
	apiKeyAuth := middleware.GetAPIKeyAuth()
	apiKeyAuth.AddAPIKey(apiKey, req.Name, req.Permissions)

	SuccessResponse(c, "API key created successfully", gin.H{
		"api_key":     apiKey,
		"name":        req.Name,
		"permissions": req.Permissions,
		"created_at":  time.Now(),
	})
}

// RevokeAPIKey 撤销API密钥
func (h *SecurityHandler) RevokeAPIKey(c *gin.Context) {
	// 检查权限
	userRole, exists := c.Get("user_role")
	if !exists || userRole != "admin" {
		ErrorResponse(c, http.StatusForbidden, "Insufficient permissions", "ADMIN_REQUIRED")
		return
	}

	apiKey := c.Param("key")
	if apiKey == "" {
		ErrorResponse(c, http.StatusBadRequest, "API key parameter is required", "MISSING_API_KEY")
		return
	}

	apiKeyAuth := middleware.GetAPIKeyAuth()
	if !apiKeyAuth.RevokeAPIKey(apiKey) {
		ErrorResponse(c, http.StatusNotFound, "API key not found", "API_KEY_NOT_FOUND")
		return
	}

	SuccessResponse(c, "API key revoked successfully", gin.H{
		"api_key": apiKey,
	})
}

// UpdateRateLimit 更新速率限制
func (h *SecurityHandler) UpdateRateLimit(c *gin.Context) {
	// 检查权限
	userRole, exists := c.Get("user_role")
	if !exists || userRole != "admin" {
		ErrorResponse(c, http.StatusForbidden, "Insufficient permissions", "ADMIN_REQUIRED")
		return
	}

	rateStr := c.Query("rate")
	burstStr := c.Query("burst")

	if rateStr == "" || burstStr == "" {
		ErrorResponse(c, http.StatusBadRequest, "Rate and burst parameters are required", "MISSING_PARAMETERS")
		return
	}

	rate, err := strconv.ParseFloat(rateStr, 64)
	if err != nil {
		ErrorResponse(c, http.StatusBadRequest, "Invalid rate parameter", err.Error())
		return
	}

	burst, err := strconv.Atoi(burstStr)
	if err != nil {
		ErrorResponse(c, http.StatusBadRequest, "Invalid burst parameter", err.Error())
		return
	}

	// 这里应该更新全局速率限制器的配置
	// 由于当前实现的限制，我们只返回成功消息
	SuccessResponse(c, "Rate limit updated successfully", gin.H{
		"rate":  rate,
		"burst": burst,
		"note":  "Rate limit will be applied to new connections",
	})
}

// generateAPIKey 生成API密钥
func generateAPIKey() string {
	// 简单的API密钥生成（实际应用中应该使用更安全的方法）
	return fmt.Sprintf("ak_%d_%s", time.Now().Unix(), randomString(32))
}

// randomString 生成随机字符串
func randomString(length int) string {
	const charset = "abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789"
	b := make([]byte, length)
	for i := range b {
		b[i] = charset[time.Now().UnixNano()%int64(len(charset))]
	}
	return string(b)
}