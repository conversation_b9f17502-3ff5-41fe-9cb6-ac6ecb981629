package cache

import (
	"context"
	"fmt"
	"time"
)

// Manager 缓存管理器
type Manager struct {
	service CacheService
	keys    *CacheKeys
}

// NewManager 创建缓存管理器
func NewManager() *Manager {
	return &Manager{
		service: NewCacheService(),
		keys:    NewCacheKeys(),
	}
}

// AuthTokenData JWT令牌缓存数据
type AuthTokenData struct {
	UserID   uint   `json:"user_id"`
	Username string `json:"username"`
	Role     string `json:"role"`
	IssuedAt int64  `json:"issued_at"`
}

// LicenseVerifyData 许可证验证缓存数据
type LicenseVerifyData struct {
	Valid        bool      `json:"valid"`
	LicenseKey   string    `json:"license_key"`
	ProductName  string    `json:"product_name"`
	MaxDevices   int       `json:"max_devices"`
	Features     []string  `json:"features"`
	ExpiresAt    *int64    `json:"expires_at,omitempty"`
	DeviceCount  int       `json:"device_count"`
	VerifiedAt   time.Time `json:"verified_at"`
	ErrorMessage string    `json:"error_message,omitempty"`
}

// UserSessionData 用户会话缓存数据
type UserSessionData struct {
	UserID    uint      `json:"user_id"`
	Username  string    `json:"username"`
	Role      string    `json:"role"`
	LastLogin time.Time `json:"last_login"`
	IPAddress string    `json:"ip_address"`
}

// StatsData 统计数据
type StatsData struct {
	TotalLicenses    int64 `json:"total_licenses"`
	ActiveLicenses   int64 `json:"active_licenses"`
	TotalVerifications int64 `json:"total_verifications"`
	SuccessfulVerifications int64 `json:"successful_verifications"`
	UniqueDevices    int64 `json:"unique_devices"`
	Date             string `json:"date"`
	UpdatedAt        time.Time `json:"updated_at"`
}

// SetAuthToken 缓存JWT令牌
func (m *Manager) SetAuthToken(ctx context.Context, tokenID string, data *AuthTokenData) error {
	key := m.keys.AuthToken(tokenID)
	return m.service.SetJSON(ctx, key, data, TTLAuthToken)
}

// GetAuthToken 获取JWT令牌缓存
func (m *Manager) GetAuthToken(ctx context.Context, tokenID string) (*AuthTokenData, error) {
	key := m.keys.AuthToken(tokenID)
	var data AuthTokenData
	err := m.service.GetJSON(ctx, key, &data)
	if err != nil {
		return nil, err
	}
	return &data, nil
}

// DeleteAuthToken 删除JWT令牌缓存
func (m *Manager) DeleteAuthToken(ctx context.Context, tokenID string) error {
	key := m.keys.AuthToken(tokenID)
	return m.service.Delete(ctx, key)
}

// SetLicenseVerifyResult 缓存许可证验证结果
func (m *Manager) SetLicenseVerifyResult(ctx context.Context, licenseKey string, data *LicenseVerifyData) error {
	key := m.keys.LicenseVerify(licenseKey)
	return m.service.SetJSON(ctx, key, data, TTLLicenseVerify)
}

// GetLicenseVerifyResult 获取许可证验证结果缓存
func (m *Manager) GetLicenseVerifyResult(ctx context.Context, licenseKey string) (*LicenseVerifyData, error) {
	key := m.keys.LicenseVerify(licenseKey)
	var data LicenseVerifyData
	err := m.service.GetJSON(ctx, key, &data)
	if err != nil {
		return nil, err
	}
	return &data, nil
}

// InvalidateLicenseCache 使许可证相关缓存失效
func (m *Manager) InvalidateLicenseCache(ctx context.Context, licenseKey string) error {
	keys := []string{
		m.keys.LicenseVerify(licenseKey),
		m.keys.LicenseInfo(licenseKey),
		m.keys.DeviceList(licenseKey),
		m.keys.OfflineToken(licenseKey),
	}
	return m.service.Delete(ctx, keys...)
}

// SetUserSession 缓存用户会话
func (m *Manager) SetUserSession(ctx context.Context, userID uint, data *UserSessionData) error {
	key := m.keys.UserSession(userID)
	return m.service.SetJSON(ctx, key, data, TTLUserSession)
}

// GetUserSession 获取用户会话缓存
func (m *Manager) GetUserSession(ctx context.Context, userID uint) (*UserSessionData, error) {
	key := m.keys.UserSession(userID)
	var data UserSessionData
	err := m.service.GetJSON(ctx, key, &data)
	if err != nil {
		return nil, err
	}
	return &data, nil
}

// DeleteUserSession 删除用户会话缓存
func (m *Manager) DeleteUserSession(ctx context.Context, userID uint) error {
	key := m.keys.UserSession(userID)
	return m.service.Delete(ctx, key)
}

// CheckRateLimit 检查API限流
func (m *Manager) CheckRateLimit(ctx context.Context, ip, endpoint string, limit int64) (bool, error) {
	key := m.keys.RateLimit(ip, endpoint)
	
	// 获取当前计数
	current, err := m.service.Increment(ctx, key)
	if err != nil {
		return false, err
	}
	
	// 如果是第一次访问，设置过期时间
	if current == 1 {
		if err := m.service.Expire(ctx, key, TTLRateLimit); err != nil {
			return false, err
		}
	}
	
	return current <= limit, nil
}

// SetDailyStats 缓存每日统计数据
func (m *Manager) SetDailyStats(ctx context.Context, date string, data *StatsData) error {
	key := m.keys.StatsDaily(date)
	return m.service.SetJSON(ctx, key, data, TTLStatsDaily)
}

// GetDailyStats 获取每日统计数据缓存
func (m *Manager) GetDailyStats(ctx context.Context, date string) (*StatsData, error) {
	key := m.keys.StatsDaily(date)
	var data StatsData
	err := m.service.GetJSON(ctx, key, &data)
	if err != nil {
		return nil, err
	}
	return &data, nil
}

// SetDeviceBinding 缓存设备绑定信息
func (m *Manager) SetDeviceBinding(ctx context.Context, licenseKey, deviceID string, data interface{}) error {
	key := m.keys.DeviceBinding(licenseKey, deviceID)
	return m.service.SetJSON(ctx, key, data, TTLDeviceBinding)
}

// GetDeviceBinding 获取设备绑定缓存
func (m *Manager) GetDeviceBinding(ctx context.Context, licenseKey, deviceID string, dest interface{}) error {
	key := m.keys.DeviceBinding(licenseKey, deviceID)
	return m.service.GetJSON(ctx, key, dest)
}

// SetOfflineToken 缓存离线验证令牌
func (m *Manager) SetOfflineToken(ctx context.Context, licenseKey, token string) error {
	key := m.keys.OfflineToken(licenseKey)
	return m.service.Set(ctx, key, token, TTLOfflineToken)
}

// GetOfflineToken 获取离线验证令牌
func (m *Manager) GetOfflineToken(ctx context.Context, licenseKey string) (string, error) {
	key := m.keys.OfflineToken(licenseKey)
	return m.service.Get(ctx, key)
}

// CacheWithFallback 带回退的缓存获取
func (m *Manager) CacheWithFallback(ctx context.Context, key string, ttl time.Duration, fallback func() (interface{}, error)) (interface{}, error) {
	// 尝试从缓存获取
	var result interface{}
	err := m.service.GetJSON(ctx, key, &result)
	if err == nil {
		return result, nil
	}
	
	// 缓存未命中，调用回退函数
	if err == ErrCacheNotFound {
		data, err := fallback()
		if err != nil {
			return nil, err
		}
		
		// 将结果缓存
		if err := m.service.SetJSON(ctx, key, data, ttl); err != nil {
			// 缓存失败不影响返回结果
			fmt.Printf("Failed to cache data for key %s: %v\n", key, err)
		}
		
		return data, nil
	}
	
	return nil, err
}

// BatchDelete 批量删除缓存
func (m *Manager) BatchDelete(ctx context.Context, pattern string) error {
	// 注意：这个方法在生产环境中应该谨慎使用
	// Redis的KEYS命令在大数据集上性能较差
	client := GetClient()
	if client == nil {
		return ErrCacheConnection
	}
	
	keys, err := client.Keys(ctx, pattern).Result()
	if err != nil {
		return err
	}
	
	if len(keys) > 0 {
		return m.service.Delete(ctx, keys...)
	}
	
	return nil
}

// GetCacheInfo 获取缓存信息
func (m *Manager) GetCacheInfo(ctx context.Context) (map[string]interface{}, error) {
	client := GetClient()
	if client == nil {
		return nil, ErrCacheConnection
	}
	
	info, err := client.Info(ctx, "memory").Result()
	if err != nil {
		return nil, err
	}
	
	dbSize, err := client.DBSize(ctx).Result()
	if err != nil {
		return nil, err
	}
	
	return map[string]interface{}{
		"db_size": dbSize,
		"memory_info": info,
		"connected": true,
	}, nil
}