server:
  port: 8080
  mode: debug # debug, release, test
  read_timeout: 30s
  write_timeout: 30s
  tls:
    enabled: true
    port: 8443
    cert_file: "./configs/certs/localhost.pem"
    key_file: "./configs/certs/localhost-key.pem"

database:
  driver: sqlite
  dsn: "./data/database.db"
  max_open_conns: 25
  max_idle_conns: 5
  conn_max_lifetime: 300s
  
redis:
  addr: "localhost:6379"
  password: ""
  db: 0
  pool_size: 10
  min_idle_conns: 5
  dial_timeout: 5s
  read_timeout: 3s
  write_timeout: 3s
  
jwt:
  secret: "your-super-secret-jwt-key-change-in-production"
  expire_hours: 24
  refresh_expire_hours: 168 # 7 days
  issuer: "software-auth"
  
logging:
  level: info # debug, info, warn, error
  file: "./data/logs/app.log"
  max_size: 100 # MB
  max_backups: 5
  max_age: 30 # days
  compress: true
  
security:
  rate_limit:
    enabled: true
    requests_per_minute: 60
  cors:
    enabled: true
    allowed_origins: ["http://localhost:3000", "http://localhost:5173", "https://localhost:3000", "https://localhost:5173", "https://localhost:8443"]
    allowed_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    allowed_headers: ["Origin", "Content-Type", "Authorization"]
  
license:
  default_expire_days: 365
  max_devices_per_license: 10
  offline_verify_window_hours: 72