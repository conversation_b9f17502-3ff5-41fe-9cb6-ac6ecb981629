/* 设计系统变量 */
:root {
  /* 主色调 */
  --primary-color: #1890ff;
  --primary-hover: #40a9ff;
  --primary-active: #096dd9;
  --primary-light: #e6f7ff;
  --primary-lighter: #f0f9ff;
  
  /* 辅助色 */
  --secondary-color: #722ed1;
  --secondary-hover: #9254de;
  --secondary-light: #f9f0ff;
  
  /* 状态色 */
  --success-color: #52c41a;
  --success-hover: #73d13d;
  --success-light: #f6ffed;
  
  --warning-color: #faad14;
  --warning-hover: #ffc53d;
  --warning-light: #fff7e6;
  
  --error-color: #ff4d4f;
  --error-hover: #ff7875;
  --error-light: #fff1f0;
  
  --info-color: #13c2c2;
  --info-hover: #36cfc9;
  --info-light: #e6fffb;
  
  /* 中性色 */
  --text-primary: #262626;
  --text-secondary: #595959;
  --text-tertiary: #8c8c8c;
  --text-quaternary: #bfbfbf;
  --text-disabled: #d9d9d9;
  
  /* 背景色 */
  --bg-primary: #ffffff;
  --bg-secondary: #fafafa;
  --bg-tertiary: #f5f5f5;
  --bg-quaternary: #f0f0f0;
  --bg-disabled: #f5f5f5;
  
  /* 边框色 */
  --border-primary: #d9d9d9;
  --border-secondary: #f0f0f0;
  --border-light: #f5f5f5;
  
  /* 阴影 */
  --shadow-sm: 0 2px 8px rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 20px rgba(0, 0, 0, 0.08);
  --shadow-lg: 0 8px 32px rgba(0, 0, 0, 0.12);
  --shadow-xl: 0 12px 40px rgba(0, 0, 0, 0.15);
  
  /* 渐变 */
  --gradient-primary: linear-gradient(135deg, #1890ff, #722ed1);
  --gradient-success: linear-gradient(135deg, #52c41a, #73d13d);
  --gradient-warning: linear-gradient(135deg, #faad14, #ffc53d);
  --gradient-error: linear-gradient(135deg, #ff4d4f, #ff7875);
  --gradient-background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  
  /* 圆角 */
  --radius-sm: 4px;
  --radius-md: 8px;
  --radius-lg: 12px;
  --radius-xl: 16px;
  --radius-2xl: 20px;
  --radius-3xl: 24px;
  --radius-full: 50%;
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 12px;
  --spacing-lg: 16px;
  --spacing-xl: 20px;
  --spacing-2xl: 24px;
  --spacing-3xl: 32px;
  --spacing-4xl: 40px;
  --spacing-5xl: 48px;
  --spacing-6xl: 64px;
  
  /* 字体大小 */
  --font-xs: 12px;
  --font-sm: 14px;
  --font-md: 16px;
  --font-lg: 18px;
  --font-xl: 20px;
  --font-2xl: 24px;
  --font-3xl: 28px;
  --font-4xl: 32px;
  --font-5xl: 36px;
  
  /* 字体权重 */
  --font-light: 300;
  --font-normal: 400;
  --font-medium: 500;
  --font-semibold: 600;
  --font-bold: 700;
  
  /* 行高 */
  --line-height-tight: 1.2;
  --line-height-normal: 1.4;
  --line-height-relaxed: 1.6;
  --line-height-loose: 1.8;
  
  /* 过渡动画 */
  --transition-fast: 0.15s ease-out;
  --transition-normal: 0.2s ease-out;
  --transition-slow: 0.3s ease-out;
  
  /* Z-index层级 */
  --z-dropdown: 1000;
  --z-sticky: 1020;
  --z-fixed: 1030;
  --z-modal-backdrop: 1040;
  --z-modal: 1050;
  --z-popover: 1060;
  --z-tooltip: 1070;
  --z-toast: 1080;
  
  /* 布局 */
  --container-sm: 640px;
  --container-md: 768px;
  --container-lg: 1024px;
  --container-xl: 1280px;
  --container-2xl: 1536px;
  
  /* 侧边栏 */
  --sidebar-width: 260px;
  --sidebar-collapsed-width: 80px;
  
  /* 头部高度 */
  --header-height: 64px;
  
  /* 表单元素 */
  --input-height: 48px;
  --input-height-sm: 36px;
  --input-height-lg: 56px;
  
  /* 按钮高度 */
  --button-height: 48px;
  --button-height-sm: 36px;
  --button-height-lg: 56px;
}

/* 深色主题变量 */
[data-theme="dark"] {
  --text-primary: #ffffff;
  --text-secondary: #d9d9d9;
  --text-tertiary: #8c8c8c;
  --text-quaternary: #595959;
  --text-disabled: #434343;
  
  --bg-primary: #141414;
  --bg-secondary: #1f1f1f;
  --bg-tertiary: #262626;
  --bg-quaternary: #2f2f2f;
  --bg-disabled: #262626;
  
  --border-primary: #434343;
  --border-secondary: #303030;
  --border-light: #262626;
}

/* 响应式断点 */
@media (max-width: 640px) {
  :root {
    --spacing-2xl: 16px;
    --spacing-3xl: 24px;
    --spacing-4xl: 32px;
    --font-2xl: 20px;
    --font-3xl: 24px;
    --font-4xl: 28px;
  }
}