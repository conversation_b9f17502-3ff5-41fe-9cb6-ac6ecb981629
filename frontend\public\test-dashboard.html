<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试仪表板</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1890ff;
        }
        .info {
            background: #e6f7ff;
            padding: 15px;
            border-radius: 4px;
            margin: 20px 0;
        }
        button {
            padding: 10px 20px;
            background: #1890ff;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        button:hover {
            background: #40a9ff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🎉 仪表板测试页面</h1>
        <p>这是一个静态测试页面，用于验证路由是否正常工作。</p>
        
        <div class="info">
            <h3>登录信息</h3>
            <p>用户名: <span id="username">加载中...</span></p>
            <p>登录状态: <span id="loginStatus">检查中...</span></p>
            <p>当前时间: <span id="currentTime">加载中...</span></p>
        </div>
        
        <button onclick="goToVueApp()">返回Vue应用</button>
        <button onclick="logout()">退出登录</button>
    </div>

    <script>
        // 更新时间
        function updateTime() {
            document.getElementById('currentTime').textContent = new Date().toLocaleString();
        }
        
        // 检查登录状态
        function checkLoginStatus() {
            const username = localStorage.getItem('username') || '未知用户';
            const isLoggedIn = localStorage.getItem('isLoggedIn') || 'false';
            
            document.getElementById('username').textContent = username;
            document.getElementById('loginStatus').textContent = isLoggedIn === 'true' ? '已登录' : '未登录';
        }
        
        // 返回Vue应用
        function goToVueApp() {
            window.location.href = '/dashboard';
        }
        
        // 退出登录
        function logout() {
            localStorage.removeItem('isLoggedIn');
            localStorage.removeItem('username');
            alert('已退出登录');
            window.location.href = '/login';
        }
        
        // 初始化
        checkLoginStatus();
        updateTime();
        setInterval(updateTime, 1000);
        
        console.log('测试页面已加载');
        console.log('localStorage内容:', {
            isLoggedIn: localStorage.getItem('isLoggedIn'),
            username: localStorage.getItem('username')
        });
    </script>
</body>
</html>