<template>
  <div class="license-detail-page">
    <PageHeader 
      :title="`许可证详情 - ${licenseData?.license_key || ''}`"
      description="查看许可证的详细信息和使用情况"
    >
      <template #extra>
        <a-space>
          <a-button @click="$router.back()">
            <template #icon>
              <icon-left />
            </template>
            返回
          </a-button>
          <a-button @click="handleRefresh" :loading="loading">
            <template #icon>
              <icon-refresh />
            </template>
            刷新
          </a-button>
          <a-dropdown>
            <a-button type="primary">
              操作
              <icon-down />
            </a-button>
            <template #content>
              <a-doption @click="handleEdit">
                <icon-edit />
                编辑许可证
              </a-doption>
              <a-doption @click="handleCopyKey">
                <icon-copy />
                复制密钥
              </a-doption>
              <a-doption @click="handleExport">
                <icon-download />
                导出数据
              </a-doption>
              <a-doption @click="handleToggleStatus">
                <icon-settings />
                {{ licenseData?.status === 'active' ? '禁用' : '启用' }}许可证
              </a-doption>
              <a-doption class="danger-option" @click="handleDelete">
                <icon-delete />
                删除许可证
              </a-doption>
            </template>
          </a-dropdown>
        </a-space>
      </template>
    </PageHeader>

    <div v-if="pageLoading" class="loading-container">
      <LoadingSpinner text="加载许可证信息..." />
    </div>

    <div v-else-if="licenseData" class="detail-container">
      <a-row :gutter="24">
        <!-- 左侧：基本信息 -->
        <a-col :span="16">
          <!-- 基本信息卡片 -->
          <a-card title="基本信息" class="info-card">
            <a-descriptions :column="2" bordered>
              <a-descriptions-item label="许可证密钥">
                <div class="license-key-display">
                  <a-typography-text copyable code>
                    {{ licenseData.license_key }}
                  </a-typography-text>
                </div>
              </a-descriptions-item>
              
              <a-descriptions-item label="状态">
                <StatusTag :status="licenseData.status" type="license" />
              </a-descriptions-item>
              
              <a-descriptions-item label="产品名称">
                {{ licenseData.product_name }}
              </a-descriptions-item>
              
              <a-descriptions-item label="客户名称">
                {{ licenseData.customer_name }}
              </a-descriptions-item>
              
              <a-descriptions-item label="客户邮箱">
                <a-link :href="`mailto:${licenseData.customer_email}`">
                  {{ licenseData.customer_email }}
                </a-link>
              </a-descriptions-item>
              
              <a-descriptions-item label="设备限制">
                <div class="device-limit-display">
                  <span>{{ currentDeviceCount }} / {{ licenseData.max_devices }} 台</span>
                  <a-progress
                    :percent="getDeviceUsagePercent()"
                    :color="getDeviceUsageColor()"
                    size="small"
                    :show-text="false"
                    class="device-progress"
                  />
                </div>
              </a-descriptions-item>
              
              <a-descriptions-item label="过期时间">
                <div class="expire-display">
                  <span v-if="licenseData.expires_at">
                    {{ formatDate(licenseData.expires_at) }}
                  </span>
                  <a-tag v-else color="blue">永不过期</a-tag>
                  
                  <a-tag 
                    v-if="licenseData.expires_at && isExpiringSoon(licenseData.expires_at)"
                    color="orange"
                    size="small"
                    class="expiring-tag"
                  >
                    {{ getExpireStatus(licenseData.expires_at) }}
                  </a-tag>
                </div>
              </a-descriptions-item>
              
              <a-descriptions-item label="创建时间">
                {{ formatDate(licenseData.created_at) }}
              </a-descriptions-item>
              
              <a-descriptions-item label="创建者">
                {{ licenseData.creator?.username || '-' }}
              </a-descriptions-item>
              
              <a-descriptions-item label="最后更新">
                {{ formatDate(licenseData.updated_at) }}
              </a-descriptions-item>
            </a-descriptions>
          </a-card>

          <!-- 功能特性 -->
          <a-card title="功能特性" class="features-card">
            <div v-if="licenseData.features.length > 0" class="features-list">
              <a-tag 
                v-for="feature in licenseData.features" 
                :key="feature"
                color="blue"
                class="feature-tag"
              >
                {{ getFeatureLabel(feature) }}
              </a-tag>
            </div>
            <EmptyState 
              v-else
              type="no-data"
              title="无功能特性"
              description="此许可证未配置任何功能特性"
            />
          </a-card>

          <!-- 设备绑定 -->
          <a-card title="设备绑定" class="devices-card">
            <template #extra>
              <a-space>
                <a-button 
                  type="text" 
                  size="small"
                  @click="handleRefreshDevices"
                  :loading="devicesLoading"
                >
                  <icon-refresh />
                  刷新
                </a-button>
              </a-space>
            </template>
            
            <div v-if="licenseData.device_bindings?.length" class="devices-table">
              <a-table
                :data="licenseData.device_bindings"
                :pagination="false"
                size="small"
              >
                <template #columns>
                  <a-table-column title="设备ID" data-index="device_id">
                    <template #cell="{ record }">
                      <a-typography-text code>{{ record.device_id }}</a-typography-text>
                    </template>
                  </a-table-column>
                  
                  <a-table-column title="设备名称" data-index="device_name">
                    <template #cell="{ record }">
                      {{ record.device_name || '-' }}
                    </template>
                  </a-table-column>
                  
                  <a-table-column title="状态" data-index="status">
                    <template #cell="{ record }">
                      <StatusTag :status="record.status" type="device" size="small" />
                    </template>
                  </a-table-column>
                  
                  <a-table-column title="首次绑定" data-index="first_seen_at">
                    <template #cell="{ record }">
                      {{ formatDate(record.first_seen_at) }}
                    </template>
                  </a-table-column>
                  
                  <a-table-column title="最后活跃" data-index="last_seen_at">
                    <template #cell="{ record }">
                      <div class="last-seen-cell">
                        {{ formatDate(record.last_seen_at) }}
                        <span class="last-seen-relative">
                          ({{ getRelativeTime(record.last_seen_at) }})
                        </span>
                      </div>
                    </template>
                  </a-table-column>
                  
                  <a-table-column title="操作" width="120">
                    <template #cell="{ record }">
                      <a-space>
                        <a-button 
                          type="text" 
                          size="small"
                          @click="handleUnbindDevice(record)"
                          status="danger"
                        >
                          解绑
                        </a-button>
                      </a-space>
                    </template>
                  </a-table-column>
                </template>
              </a-table>
            </div>
            
            <EmptyState 
              v-else
              type="no-data"
              title="暂无绑定设备"
              description="还没有设备使用此许可证"
            />
          </a-card>
        </a-col>

        <!-- 右侧：统计和操作 -->
        <a-col :span="8">
          <!-- 使用统计 -->
          <a-card title="使用统计" class="stats-card">
            <div class="stats-grid">
              <div class="stat-item">
                <div class="stat-value">{{ verificationStats.total }}</div>
                <div class="stat-label">总验证次数</div>
              </div>
              
              <div class="stat-item">
                <div class="stat-value">{{ verificationStats.today }}</div>
                <div class="stat-label">今日验证</div>
              </div>
              
              <div class="stat-item">
                <div class="stat-value">{{ verificationStats.success_rate }}%</div>
                <div class="stat-label">成功率</div>
              </div>
              
              <div class="stat-item">
                <div class="stat-value">{{ currentDeviceCount }}</div>
                <div class="stat-label">绑定设备</div>
              </div>
            </div>
          </a-card>

          <!-- 快速操作 -->
          <a-card title="快速操作" class="actions-card">
            <div class="quick-actions">
              <a-button 
                type="outline" 
                long 
                @click="handleEdit"
                class="action-btn"
              >
                <template #icon>
                  <icon-edit />
                </template>
                编辑许可证
              </a-button>
              
              <a-button 
                type="outline" 
                long 
                @click="handleCopyKey"
                class="action-btn"
              >
                <template #icon>
                  <icon-copy />
                </template>
                复制密钥
              </a-button>
              
              <a-button 
                type="outline" 
                long 
                @click="handleViewLogs"
                class="action-btn"
              >
                <template #icon>
                  <icon-history />
                </template>
                查看日志
              </a-button>
              
              <a-button 
                type="outline" 
                long 
                @click="handleExport"
                class="action-btn"
              >
                <template #icon>
                  <icon-download />
                </template>
                导出数据
              </a-button>
            </div>
          </a-card>

          <!-- 许可证状态 -->
          <a-card title="许可证状态" class="status-card">
            <div class="status-timeline">
              <a-timeline>
                <a-timeline-item>
                  <template #dot>
                    <icon-check-circle style="color: #52c41a" />
                  </template>
                  <div class="timeline-content">
                    <div class="timeline-title">许可证创建</div>
                    <div class="timeline-time">{{ formatDate(licenseData.created_at) }}</div>
                  </div>
                </a-timeline-item>
                
                <a-timeline-item v-if="licenseData.updated_at !== licenseData.created_at">
                  <template #dot>
                    <icon-edit style="color: #1890ff" />
                  </template>
                  <div class="timeline-content">
                    <div class="timeline-title">信息更新</div>
                    <div class="timeline-time">{{ formatDate(licenseData.updated_at) }}</div>
                  </div>
                </a-timeline-item>
                
                <a-timeline-item v-if="licenseData.device_bindings?.length">
                  <template #dot>
                    <icon-desktop style="color: #722ed1" />
                  </template>
                  <div class="timeline-content">
                    <div class="timeline-title">设备绑定</div>
                    <div class="timeline-time">{{ getLatestDeviceBindTime() }}</div>
                  </div>
                </a-timeline-item>
                
                <a-timeline-item v-if="licenseData.expires_at">
                  <template #dot>
                    <icon-clock-circle :style="{ color: getExpireColor() }" />
                  </template>
                  <div class="timeline-content">
                    <div class="timeline-title">
                      {{ isExpired(licenseData.expires_at) ? '已过期' : '将过期' }}
                    </div>
                    <div class="timeline-time">{{ formatDate(licenseData.expires_at) }}</div>
                  </div>
                </a-timeline-item>
              </a-timeline>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <EmptyState 
      v-else
      type="error"
      title="许可证不存在"
      description="找不到指定的许可证信息"
    >
      <template #action>
        <a-button type="primary" @click="$router.push('/licenses')">
          返回列表
        </a-button>
      </template>
    </EmptyState>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Message, Modal } from '@arco-design/web-vue'
import PageHeader from '@/components/common/PageHeader.vue'
import StatusTag from '@/components/common/StatusTag.vue'
import LoadingSpinner from '@/components/common/LoadingSpinner.vue'
import EmptyState from '@/components/common/EmptyState.vue'
import type { License, DeviceBinding } from '@/types/license'
import { licenseApi } from '@/api/license'
import dayjs from 'dayjs'
import relativeTime from 'dayjs/plugin/relativeTime'

dayjs.extend(relativeTime)

const router = useRouter()
const route = useRoute()

// 数据状态
const pageLoading = ref(true)
const loading = ref(false)
const devicesLoading = ref(false)
const licenseData = ref<License | null>(null)

// 验证统计数据
const verificationStats = reactive({
  total: 0,
  today: 0,
  success_rate: 0
})

// 可用功能特性映射
const availableFeatures = [
  { label: '基础功能', value: 'basic' },
  { label: '高级功能', value: 'advanced' },
  { label: '专业功能', value: 'professional' },
  { label: '企业功能', value: 'enterprise' },
  { label: 'API访问', value: 'api_access' },
  { label: '数据导出', value: 'data_export' },
  { label: '多用户支持', value: 'multi_user' },
  { label: '云同步', value: 'cloud_sync' }
]

// 当前设备数量
const currentDeviceCount = computed(() => {
  return licenseData.value?.device_bindings?.filter(d => d.status === 'active').length || 0
})

// 格式化日期
const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

// 获取相对时间
const getRelativeTime = (date: string) => {
  return dayjs(date).fromNow()
}

// 获取功能标签
const getFeatureLabel = (value: string) => {
  const feature = availableFeatures.find(f => f.value === value)
  return feature ? feature.label : value
}

// 检查是否过期
const isExpired = (expiresAt: string) => {
  return dayjs().isAfter(dayjs(expiresAt))
}

// 检查是否即将过期
const isExpiringSoon = (expiresAt: string) => {
  const expireDate = dayjs(expiresAt)
  const now = dayjs()
  const daysUntilExpire = expireDate.diff(now, 'day')
  return daysUntilExpire <= 30 && daysUntilExpire > 0
}

// 获取过期状态
const getExpireStatus = (expiresAt: string) => {
  if (isExpired(expiresAt)) {
    return '已过期'
  }
  
  const daysUntilExpire = dayjs(expiresAt).diff(dayjs(), 'day')
  if (daysUntilExpire <= 7) {
    return `${daysUntilExpire}天后过期`
  } else if (daysUntilExpire <= 30) {
    return '即将过期'
  }
  
  return ''
}

// 获取过期颜色
const getExpireColor = () => {
  if (!licenseData.value?.expires_at) return '#52c41a'
  
  if (isExpired(licenseData.value.expires_at)) {
    return '#ff4d4f'
  } else if (isExpiringSoon(licenseData.value.expires_at)) {
    return '#faad14'
  }
  
  return '#52c41a'
}

// 获取设备使用百分比
const getDeviceUsagePercent = () => {
  if (!licenseData.value) return 0
  return Math.round((currentDeviceCount.value / licenseData.value.max_devices) * 100)
}

// 获取设备使用颜色
const getDeviceUsageColor = () => {
  const percent = getDeviceUsagePercent()
  if (percent >= 90) return '#ff4d4f'
  if (percent >= 70) return '#faad14'
  return '#52c41a'
}

// 获取最新设备绑定时间
const getLatestDeviceBindTime = () => {
  if (!licenseData.value?.device_bindings?.length) return ''
  
  const latest = licenseData.value.device_bindings.reduce((latest, current) => {
    return dayjs(current.first_seen_at).isAfter(dayjs(latest.first_seen_at)) ? current : latest
  })
  
  return formatDate(latest.first_seen_at)
}

// 加载许可证数据
const loadLicenseData = async () => {
  try {
    pageLoading.value = true
    
    const licenseId = Number(route.params.id)
    if (!licenseId) {
      throw new Error('Invalid license ID')
    }

    // TODO: 调用实际API
    // const response = await licenseApi.getById(licenseId)
    
    // 模拟数据
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const mockData: License = {
      id: licenseId,
      license_key: 'TEST-DEMO-LICENSE-001',
      product_name: 'Demo Software',
      customer_name: 'Test Customer 1',
      customer_email: '<EMAIL>',
      max_devices: 3,
      features: ['basic', 'advanced', 'api_access'],
      expires_at: dayjs().add(30, 'day').toISOString(),
      status: 'active',
      created_by: 1,
      created_at: dayjs().subtract(10, 'day').toISOString(),
      updated_at: dayjs().subtract(1, 'day').toISOString(),
      creator: {
        id: 1,
        username: 'admin'
      },
      device_bindings: [
        {
          id: 1,
          license_key: 'TEST-DEMO-LICENSE-001',
          device_id: 'device-001',
          device_name: 'PC-001',
          first_seen_at: dayjs().subtract(5, 'day').toISOString(),
          last_seen_at: dayjs().subtract(1, 'hour').toISOString(),
          status: 'active',
          created_at: dayjs().subtract(5, 'day').toISOString(),
          updated_at: dayjs().subtract(1, 'hour').toISOString()
        },
        {
          id: 2,
          license_key: 'TEST-DEMO-LICENSE-001',
          device_id: 'device-002',
          device_name: 'Laptop-001',
          first_seen_at: dayjs().subtract(3, 'day').toISOString(),
          last_seen_at: dayjs().subtract(2, 'hour').toISOString(),
          status: 'active',
          created_at: dayjs().subtract(3, 'day').toISOString(),
          updated_at: dayjs().subtract(2, 'hour').toISOString()
        }
      ]
    }
    
    licenseData.value = mockData
    
    // 加载统计数据
    verificationStats.total = 1247
    verificationStats.today = 23
    verificationStats.success_rate = 98
    
  } catch (error) {
    console.error('Failed to load license data:', error)
    Message.error('加载许可证信息失败')
  } finally {
    pageLoading.value = false
  }
}

// 刷新数据
const handleRefresh = () => {
  loadLicenseData()
}

// 刷新设备列表
const handleRefreshDevices = async () => {
  try {
    devicesLoading.value = true
    // TODO: 调用实际API刷新设备列表
    await new Promise(resolve => setTimeout(resolve, 1000))
    Message.success('设备列表已刷新')
  } catch (error) {
    console.error('Refresh devices error:', error)
    Message.error('刷新设备列表失败')
  } finally {
    devicesLoading.value = false
  }
}

// 编辑许可证
const handleEdit = () => {
  router.push(`/licenses/${licenseData.value!.id}/edit`)
}

// 复制许可证密钥
const handleCopyKey = async () => {
  try {
    await navigator.clipboard.writeText(licenseData.value!.license_key)
    Message.success('许可证密钥已复制到剪贴板')
  } catch (error) {
    console.error('Copy error:', error)
    Message.error('复制失败')
  }
}

// 切换状态
const handleToggleStatus = () => {
  const action = licenseData.value!.status === 'active' ? '禁用' : '启用'
  
  Modal.confirm({
    title: `确认${action}许可证？`,
    content: `您确定要${action}许可证 ${licenseData.value!.license_key} 吗？`,
    onOk: async () => {
      try {
        // TODO: 调用实际API
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        licenseData.value!.status = licenseData.value!.status === 'active' ? 'disabled' : 'active'
        Message.success(`许可证${action}成功`)
      } catch (error) {
        console.error('Toggle status error:', error)
        Message.error('操作失败')
      }
    }
  })
}

// 删除许可证
const handleDelete = () => {
  Modal.confirm({
    title: '确认删除许可证？',
    content: `您确定要删除许可证 ${licenseData.value!.license_key} 吗？此操作不可撤销。`,
    okButtonProps: { status: 'danger' },
    onOk: async () => {
      try {
        // TODO: 调用实际API
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        Message.success('许可证删除成功')
        router.push('/licenses')
      } catch (error) {
        console.error('Delete error:', error)
        Message.error('删除失败')
      }
    }
  })
}

// 导出数据
const handleExport = async () => {
  try {
    // TODO: 调用实际API
    await new Promise(resolve => setTimeout(resolve, 1000))
    Message.success('导出成功')
  } catch (error) {
    console.error('Export error:', error)
    Message.error('导出失败')
  }
}

// 查看日志
const handleViewLogs = () => {
  // TODO: 跳转到日志页面
  Message.info('日志查看功能开发中')
}

// 解绑设备
const handleUnbindDevice = (device: DeviceBinding) => {
  Modal.confirm({
    title: '确认解绑设备？',
    content: `您确定要解绑设备 ${device.device_name || device.device_id} 吗？`,
    onOk: async () => {
      try {
        // TODO: 调用实际API
        await new Promise(resolve => setTimeout(resolve, 1000))
        
        // 从列表中移除设备
        const index = licenseData.value!.device_bindings!.findIndex(d => d.id === device.id)
        if (index > -1) {
          licenseData.value!.device_bindings!.splice(index, 1)
        }
        
        Message.success('设备解绑成功')
      } catch (error) {
        console.error('Unbind device error:', error)
        Message.error('解绑设备失败')
      }
    }
  })
}

onMounted(() => {
  loadLicenseData()
})
</script>

<style scoped>
.license-detail-page {
  padding: 24px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.detail-container {
  margin-top: 24px;
}

.info-card,
.features-card,
.devices-card,
.stats-card,
.actions-card,
.status-card {
  margin-bottom: 24px;
}

.license-key-display {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

.device-limit-display {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.device-progress {
  width: 100%;
}

.expire-display {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.expiring-tag {
  align-self: flex-start;
}

.features-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.feature-tag {
  margin-bottom: 8px;
}

.devices-table {
  margin-top: 16px;
}

.last-seen-cell {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.last-seen-relative {
  font-size: 12px;
  color: #8c8c8c;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;
}

.stat-item {
  text-align: center;
  padding: 16px;
  background: #fafafa;
  border-radius: 6px;
}

.stat-value {
  font-size: 24px;
  font-weight: 600;
  color: #1890ff;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 12px;
  color: #8c8c8c;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-btn {
  justify-content: flex-start;
}

.status-timeline {
  padding: 16px 0;
}

.timeline-content {
  padding-left: 8px;
}

.timeline-title {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.timeline-time {
  font-size: 12px;
  color: #8c8c8c;
}

.danger-option {
  color: #ff4d4f;
}

.danger-option:hover {
  background-color: #fff2f0;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .detail-container .arco-row {
    flex-direction: column;
  }
  
  .detail-container .arco-col {
    width: 100%;
  }
  
  .stats-card,
  .actions-card,
  .status-card {
    margin-top: 24px;
  }
}

@media (max-width: 768px) {
  .license-detail-page {
    padding: 16px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .arco-descriptions {
    column-count: 1;
  }
}
</style>