package api

import (
	"crypto/md5"
	"fmt"
	"net/http"
	"strings"
	"time"

	"software-auth/internal/cache"
	"software-auth/internal/database"
	"software-auth/internal/model"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// VerifyHandler 验证处理器
type VerifyHandler struct {
	cacheManager *cache.Manager
}

// NewVerifyHandler 创建验证处理器
func NewVerifyHandler() *VerifyHandler {
	return &VerifyHandler{
		cacheManager: cache.NewManager(),
	}
}

// VerifyLicenseRequest 验证许可证请求
type VerifyLicenseRequest struct {
	LicenseKey     string `json:"license_key" binding:"required"`
	DeviceID       string `json:"device_id" binding:"required"`
	DeviceName     string `json:"device_name"`
	MachineInfo    string `json:"machine_info"`
	ProductName    string `json:"product_name" binding:"required"`
	ProductVersion string `json:"product_version"`
	ClientIP       string `json:"client_ip"`
}

// VerifyLicenseResponse 验证许可证响应
type VerifyLicenseResponse struct {
	Valid          bool      `json:"valid"`
	LicenseKey     string    `json:"license_key"`
	CustomerName   string    `json:"customer_name"`
	ProductName    string    `json:"product_name"`
	ProductVersion string    `json:"product_version"`
	LicenseType    string    `json:"license_type"`
	Features       []string  `json:"features"`
	MaxDevices     int       `json:"max_devices"`
	DeviceCount    int       `json:"device_count"`
	ExpiresAt      time.Time `json:"expires_at"`
	DaysRemaining  int       `json:"days_remaining"`
	Status         string    `json:"status"`
	Message        string    `json:"message"`
	OfflineToken   string    `json:"offline_token,omitempty"`
}

// DeviceBindingRequest 设备绑定请求
type DeviceBindingRequest struct {
	LicenseKey  string `json:"license_key" binding:"required"`
	DeviceID    string `json:"device_id" binding:"required"`
	DeviceName  string `json:"device_name" binding:"required"`
	MachineInfo string `json:"machine_info"`
}

// DeviceBindingResponse 设备绑定响应
type DeviceBindingResponse struct {
	Success    bool      `json:"success"`
	DeviceID   string    `json:"device_id"`
	DeviceName string    `json:"device_name"`
	BoundAt    time.Time `json:"bound_at"`
	Message    string    `json:"message"`
}

// UnbindDeviceRequest 解绑设备请求
type UnbindDeviceRequest struct {
	LicenseKey string `json:"license_key" binding:"required"`
	DeviceID   string `json:"device_id" binding:"required"`
}

// GetDeviceListRequest 获取设备列表请求
type GetDeviceListRequest struct {
	LicenseKey string `form:"license_key" binding:"required"`
}

// DeviceInfo 设备信息
type DeviceInfo struct {
	ID          uint      `json:"id"`
	DeviceID    string    `json:"device_id"`
	DeviceName  string    `json:"device_name"`
	MachineInfo string    `json:"machine_info"`
	BoundAt     time.Time `json:"bound_at"`
	LastSeenAt  time.Time `json:"last_seen_at"`
	Status      string    `json:"status"`
}

// VerifyLicense 验证许可证
func (h *VerifyHandler) VerifyLicense(c *gin.Context) {
	var req VerifyLicenseRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ErrorResponse(c, http.StatusBadRequest, "Invalid request format", err.Error())
		return
	}

	// 设置客户端IP
	if req.ClientIP == "" {
		req.ClientIP = c.ClientIP()
	}

	// 查找许可证
	db := database.GetDB()
	var license model.License
	if err := db.Where("license_key = ?", req.LicenseKey).First(&license).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			h.logVerification(req.LicenseKey, req.DeviceID, req.ClientIP, false, "License not found")
			c.JSON(http.StatusOK, VerifyLicenseResponse{
				Valid:   false,
				Message: "Invalid license key",
				Status:  "invalid",
			})
			return
		}
		ErrorResponse(c, http.StatusInternalServerError, "Database error", err.Error())
		return
	}

	// 检查许可证状态
	if license.Status != "active" {
		message := fmt.Sprintf("License is %s", license.Status)
		h.logVerification(req.LicenseKey, req.DeviceID, req.ClientIP, false, message)
		c.JSON(http.StatusOK, VerifyLicenseResponse{
			Valid:   false,
			Message: message,
			Status:  license.Status,
		})
		return
	}

	// 检查产品匹配
	if req.ProductName != license.ProductName {
		message := "Product name mismatch"
		h.logVerification(req.LicenseKey, req.DeviceID, req.ClientIP, false, message)
		c.JSON(http.StatusOK, VerifyLicenseResponse{
			Valid:   false,
			Message: message,
			Status:  "product_mismatch",
		})
		return
	}

	// 检查过期时间
	now := time.Now()
	if license.ExpiresAt != nil && license.ExpiresAt.Before(now) {
		// 自动更新许可证状态为过期
		license.Status = "expired"
		db.Save(&license)

		message := "License has expired"
		h.logVerification(req.LicenseKey, req.DeviceID, req.ClientIP, false, message)
		c.JSON(http.StatusOK, VerifyLicenseResponse{
			Valid:         false,
			Message:       message,
			Status:        "expired",
			ExpiresAt:     *license.ExpiresAt,
			DaysRemaining: 0,
		})
		return
	}

	// 检查设备限制
	deviceCount, deviceExists, err := h.checkDeviceBinding(&license, req.DeviceID, req.DeviceName, req.MachineInfo)
	if err != nil {
		ErrorResponse(c, http.StatusInternalServerError, "Device check failed", err.Error())
		return
	}

	if !deviceExists && deviceCount >= license.MaxDevices {
		message := fmt.Sprintf("Device limit exceeded (%d/%d)", deviceCount, license.MaxDevices)
		h.logVerification(req.LicenseKey, req.DeviceID, req.ClientIP, false, message)
		c.JSON(http.StatusOK, VerifyLicenseResponse{
			Valid:       false,
			Message:     message,
			Status:      "device_limit_exceeded",
			MaxDevices:  license.MaxDevices,
			DeviceCount: deviceCount,
		})
		return
	}

	// 如果设备不存在，自动绑定
	if !deviceExists {
		if err := h.bindDevice(&license, req.DeviceID, req.DeviceName, req.MachineInfo); err != nil {
			ErrorResponse(c, http.StatusInternalServerError, "Device binding failed", err.Error())
			return
		}
		deviceCount++
	} else {
		// 更新设备最后见到时间
		h.updateDeviceLastSeen(license.ID, req.DeviceID)
	}

	// 解析功能列表
	var features []string
	if license.Features != "" {
		features = strings.Split(license.Features, ",")
	}

	// 计算剩余天数
	daysRemaining := int(license.ExpiresAt.Sub(now).Hours() / 24)

	// 生成离线验证令牌（可选）
	offlineToken := ""
	if req.ProductVersion != "" {
		offlineToken = h.generateOfflineToken(&license, req.DeviceID)
	}

	// 记录验证日志
	h.logVerification(req.LicenseKey, req.DeviceID, req.ClientIP, true, "Verification successful")

	// 返回验证结果
	response := VerifyLicenseResponse{
		Valid:        true,
		LicenseKey:   license.LicenseKey,
		CustomerName: license.CustomerName,
		ProductName:  license.ProductName,
		Features:     features,
		MaxDevices:   license.MaxDevices,
		DeviceCount:  deviceCount,
		ExpiresAt: func() time.Time {
			if license.ExpiresAt != nil {
				return *license.ExpiresAt
			}
			return time.Time{}
		}(),
		DaysRemaining: daysRemaining,
		Status:        "valid",
		Message:       "License is valid",
		OfflineToken:  offlineToken,
	}

	c.JSON(http.StatusOK, response)
}

// BindDevice 绑定设备
func (h *VerifyHandler) BindDevice(c *gin.Context) {
	var req DeviceBindingRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ErrorResponse(c, http.StatusBadRequest, "Invalid request format", err.Error())
		return
	}

	// 查找许可证
	db := database.GetDB()
	var license model.License
	if err := db.Where("license_key = ?", req.LicenseKey).First(&license).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			ErrorResponse(c, http.StatusNotFound, "License not found", "INVALID_LICENSE")
			return
		}
		ErrorResponse(c, http.StatusInternalServerError, "Database error", err.Error())
		return
	}

	// 检查许可证状态
	if license.Status != "active" {
		ErrorResponse(c, http.StatusForbidden, fmt.Sprintf("License is %s", license.Status), "LICENSE_INACTIVE")
		return
	}

	// 检查设备是否已绑定
	var existingBinding model.DeviceBinding
	if err := db.Where("license_key = ? AND device_id = ?", license.LicenseKey, req.DeviceID).First(&existingBinding).Error; err == nil {
		// 设备已绑定，更新信息
		existingBinding.DeviceName = req.DeviceName
		existingBinding.LastSeenAt = time.Now()
		db.Save(&existingBinding)

		c.JSON(http.StatusOK, DeviceBindingResponse{
			Success:    true,
			DeviceID:   req.DeviceID,
			DeviceName: req.DeviceName,
			BoundAt:    existingBinding.FirstSeenAt,
			Message:    "Device already bound, information updated",
		})
		return
	}

	// 检查设备数量限制
	var deviceCount int64
	db.Model(&model.DeviceBinding{}).Where("license_id = ?", license.ID).Count(&deviceCount)

	if int(deviceCount) >= license.MaxDevices {
		ErrorResponse(c, http.StatusForbidden,
			fmt.Sprintf("Device limit exceeded (%d/%d)", deviceCount, license.MaxDevices),
			"DEVICE_LIMIT_EXCEEDED")
		return
	}

	// 绑定新设备
	binding := model.DeviceBinding{
		LicenseKey:  license.LicenseKey,
		DeviceID:    req.DeviceID,
		DeviceName:  req.DeviceName,
		FirstSeenAt: time.Now(),
		LastSeenAt:  time.Now(),
		Status:      "active",
	}

	if err := db.Create(&binding).Error; err != nil {
		ErrorResponse(c, http.StatusInternalServerError, "Failed to bind device", err.Error())
		return
	}

	c.JSON(http.StatusOK, DeviceBindingResponse{
		Success:    true,
		DeviceID:   req.DeviceID,
		DeviceName: req.DeviceName,
		BoundAt:    binding.FirstSeenAt,
		Message:    "Device bound successfully",
	})
}

// UnbindDevice 解绑设备
func (h *VerifyHandler) UnbindDevice(c *gin.Context) {
	var req UnbindDeviceRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ErrorResponse(c, http.StatusBadRequest, "Invalid request format", err.Error())
		return
	}

	// 查找许可证
	db := database.GetDB()
	var license model.License
	if err := db.Where("license_key = ?", req.LicenseKey).First(&license).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			ErrorResponse(c, http.StatusNotFound, "License not found", "INVALID_LICENSE")
			return
		}
		ErrorResponse(c, http.StatusInternalServerError, "Database error", err.Error())
		return
	}

	// 查找设备绑定
	var binding model.DeviceBinding
	if err := db.Where("license_key = ? AND device_id = ?", license.LicenseKey, req.DeviceID).First(&binding).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			ErrorResponse(c, http.StatusNotFound, "Device binding not found", "DEVICE_NOT_BOUND")
			return
		}
		ErrorResponse(c, http.StatusInternalServerError, "Database error", err.Error())
		return
	}

	// 删除设备绑定
	if err := db.Delete(&binding).Error; err != nil {
		ErrorResponse(c, http.StatusInternalServerError, "Failed to unbind device", err.Error())
		return
	}

	SuccessResponse(c, "Device unbound successfully", gin.H{
		"device_id":   req.DeviceID,
		"license_key": req.LicenseKey,
	})
}

// GetDeviceList 获取设备列表
func (h *VerifyHandler) GetDeviceList(c *gin.Context) {
	var req GetDeviceListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		ErrorResponse(c, http.StatusBadRequest, "Invalid query parameters", err.Error())
		return
	}

	// 查找许可证
	db := database.GetDB()
	var license model.License
	if err := db.Where("license_key = ?", req.LicenseKey).First(&license).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			ErrorResponse(c, http.StatusNotFound, "License not found", "INVALID_LICENSE")
			return
		}
		ErrorResponse(c, http.StatusInternalServerError, "Database error", err.Error())
		return
	}

	// 查询设备绑定
	var bindings []model.DeviceBinding
	if err := db.Where("license_key = ?", license.LicenseKey).Order("first_seen_at DESC").Find(&bindings).Error; err != nil {
		ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch device list", err.Error())
		return
	}

	// 构建响应
	devices := make([]DeviceInfo, len(bindings))
	for i, binding := range bindings {
		devices[i] = DeviceInfo{
			ID:         binding.ID,
			DeviceID:   binding.DeviceID,
			DeviceName: binding.DeviceName,
			BoundAt:    binding.FirstSeenAt,
			LastSeenAt: binding.LastSeenAt,
			Status:     binding.Status,
		}
	}

	SuccessResponse(c, "Device list retrieved successfully", gin.H{
		"license_key":  req.LicenseKey,
		"max_devices":  license.MaxDevices,
		"device_count": len(devices),
		"devices":      devices,
	})
}

// checkDeviceBinding 检查设备绑定
func (h *VerifyHandler) checkDeviceBinding(license *model.License, deviceID, deviceName, machineInfo string) (int, bool, error) {
	db := database.GetDB()

	// 统计设备数量
	var deviceCount int64
	if err := db.Model(&model.DeviceBinding{}).Where("license_key = ?", license.LicenseKey).Count(&deviceCount).Error; err != nil {
		return 0, false, err
	}

	// 检查设备是否已绑定
	var binding model.DeviceBinding
	err := db.Where("license_key = ? AND device_id = ?", license.LicenseKey, deviceID).First(&binding).Error
	if err != nil {
		if err == gorm.ErrRecordNotFound {
			return int(deviceCount), false, nil
		}
		return 0, false, err
	}

	return int(deviceCount), true, nil
}

// bindDevice 绑定设备
func (h *VerifyHandler) bindDevice(license *model.License, deviceID, deviceName, machineInfo string) error {
	db := database.GetDB()

	binding := model.DeviceBinding{
		LicenseKey:  license.LicenseKey,
		DeviceID:    deviceID,
		DeviceName:  deviceName,
		FirstSeenAt: time.Now(),
		LastSeenAt:  time.Now(),
		Status:      "active",
	}

	return db.Create(&binding).Error
}

// updateDeviceLastSeen 更新设备最后见到时间
func (h *VerifyHandler) updateDeviceLastSeen(licenseID uint, deviceID string) {
	db := database.GetDB()
	db.Model(&model.DeviceBinding{}).
		Where("license_key = ? AND device_id = ?", licenseID, deviceID).
		Update("last_seen_at", time.Now())
}

// logVerification 记录验证日志
func (h *VerifyHandler) logVerification(licenseKey, deviceID, clientIP string, success bool, message string) {
	db := database.GetDB()

	// 查找许可证ID
	var license model.License
	if err := db.Where("license_key = ?", licenseKey).First(&license).Error; err != nil {
		return // 忽略日志记录错误
	}

	log := model.VerificationLog{
		LicenseKey: license.LicenseKey,
		DeviceID:   deviceID,
		IPAddress:  clientIP,
		VerificationResult: func() string {
			if success {
				return model.VerificationResultSuccess
			}
			return model.VerificationResultFailed
		}(),
		ErrorMessage: func() string {
			if success {
				return ""
			}
			return message
		}(),
		CreatedAt: time.Now(),
	}

	db.Create(&log) // 忽略错误
}

// generateOfflineToken 生成离线验证令牌
func (h *VerifyHandler) generateOfflineToken(license *model.License, deviceID string) string {
	// 简单的离线令牌生成（实际应用中应该使用更安全的方法）
	data := fmt.Sprintf("%s:%s:%s:%d",
		license.LicenseKey,
		deviceID,
		license.ExpiresAt.Format("2006-01-02"),
		time.Now().Unix())

	hash := md5.Sum([]byte(data))
	return fmt.Sprintf("%x", hash)
}
