<template>
  <div class="enhanced-dashboard">
    <!-- 顶部导航栏 -->
    <div class="top-navbar">
      <div class="navbar-left">
        <div class="logo">
          <span class="logo-icon">🔐</span>
          <span class="logo-text">软件网络授权系统</span>
        </div>
        <div class="breadcrumb">
          <span class="breadcrumb-item">首页</span>
          <span class="breadcrumb-separator">/</span>
          <span class="breadcrumb-item active">仪表板</span>
        </div>
      </div>
      <div class="navbar-right">
        <div class="time-display">
          <span class="time-icon">🕐</span>
          <span class="time-text">{{ currentTime }}</span>
        </div>
        <div class="user-menu">
          <div class="user-avatar">{{ username.charAt(0).toUpperCase() }}</div>
          <div class="user-info">
            <div class="user-name">{{ username }}</div>
            <div class="user-role">系统管理员</div>
          </div>
          <div class="user-actions">
            <button @click="$router.push('/profile')" class="action-btn profile-btn">
              👤 个人资料
            </button>
            <button @click="handleLogout" class="action-btn logout-btn">
              🚪 退出登录
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 欢迎横幅 -->
    <div class="welcome-banner">
      <div class="banner-content">
        <div class="welcome-text">
          <h1>欢迎回来，{{ username }}！</h1>
          <p>今天是个美好的一天，让我们开始管理您的软件授权系统吧</p>
        </div>
        <div class="banner-actions">
          <button @click="$router.push('/licenses/create')" class="primary-btn">
            ➕ 创建新许可证
          </button>
          <button @click="refreshData" class="secondary-btn" :disabled="loading">
            {{ loading ? '🔄' : '↻' }} 刷新数据
          </button>
        </div>
      </div>
      <div class="banner-decoration">
        <div class="decoration-circle circle-1"></div>
        <div class="decoration-circle circle-2"></div>
        <div class="decoration-circle circle-3"></div>
      </div>
    </div>

    <!-- 统计卡片区域 -->
    <div class="stats-section">
      <div class="section-header">
        <h2>📊 系统概览</h2>
        <div class="section-actions">
          <select v-model="selectedPeriod" @change="loadStats" class="period-selector">
            <option value="today">今日</option>
            <option value="week">本周</option>
            <option value="month">本月</option>
            <option value="year">本年</option>
          </select>
        </div>
      </div>
      
      <div class="stats-grid">
        <div class="stat-card primary" :class="{ loading }">
          <div class="stat-header">
            <div class="stat-icon">📄</div>
            <div class="stat-trend positive">+12%</div>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ loading ? '...' : stats.totalLicenses }}</div>
            <div class="stat-label">总许可证数</div>
            <div class="stat-description">较上{{ selectedPeriod === 'today' ? '日' : '期' }}增长</div>
          </div>
          <div class="stat-chart">
            <div class="mini-chart">
              <div class="chart-bar" style="height: 60%"></div>
              <div class="chart-bar" style="height: 80%"></div>
              <div class="chart-bar" style="height: 45%"></div>
              <div class="chart-bar" style="height: 90%"></div>
              <div class="chart-bar" style="height: 100%"></div>
            </div>
          </div>
        </div>

        <div class="stat-card success" :class="{ loading }">
          <div class="stat-header">
            <div class="stat-icon">✅</div>
            <div class="stat-trend positive">+8%</div>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ loading ? '...' : stats.activeLicenses }}</div>
            <div class="stat-label">活跃许可证</div>
            <div class="stat-description">正常运行中</div>
          </div>
          <div class="stat-progress">
            <div class="progress-bar">
              <div class="progress-fill" :style="{ width: progressPercentage + '%' }"></div>
            </div>
            <div class="progress-text">{{ progressPercentage }}% 活跃率</div>
          </div>
        </div>

        <div class="stat-card warning" :class="{ loading }">
          <div class="stat-header">
            <div class="stat-icon">🔍</div>
            <div class="stat-trend positive">+15%</div>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ loading ? '...' : stats.todayVerifications }}</div>
            <div class="stat-label">今日验证次数</div>
            <div class="stat-description">系统验证请求</div>
          </div>
          <div class="stat-footer">
            <div class="footer-item">
              <span class="footer-label">成功率</span>
              <span class="footer-value">98.5%</span>
            </div>
            <div class="footer-item">
              <span class="footer-label">平均响应</span>
              <span class="footer-value">45ms</span>
            </div>
          </div>
        </div>

        <div class="stat-card info" :class="{ loading }">
          <div class="stat-header">
            <div class="stat-icon">💻</div>
            <div class="stat-trend positive">+3%</div>
          </div>
          <div class="stat-content">
            <div class="stat-value">{{ loading ? '...' : stats.onlineDevices }}</div>
            <div class="stat-label">在线设备</div>
            <div class="stat-description">当前连接设备</div>
          </div>
          <div class="device-status">
            <div class="status-item">
              <div class="status-dot online"></div>
              <span>{{ Math.floor(stats.onlineDevices * 0.8) }} 在线</span>
            </div>
            <div class="status-item">
              <div class="status-dot offline"></div>
              <span>{{ Math.floor(stats.onlineDevices * 0.2) }} 离线</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <div class="content-left">
        <!-- 最近活动 -->
        <div class="content-card">
          <div class="card-header">
            <h3>📋 最近活动</h3>
            <div class="card-actions">
              <button @click="loadActivities" class="icon-btn" :disabled="loading">
                {{ loading ? '🔄' : '↻' }}
              </button>
              <button @click="$router.push('/reports')" class="text-btn">
                查看全部
              </button>
            </div>
          </div>
          <div class="activity-timeline">
            <div v-for="activity in activities" :key="activity.id" class="timeline-item">
              <div class="timeline-marker" :class="activity.type">
                <span class="marker-icon">{{ getActivityIcon(activity.type) }}</span>
              </div>
              <div class="timeline-content">
                <div class="activity-header">
                  <span class="activity-title">{{ activity.title }}</span>
                  <span class="activity-time">{{ activity.time }}</span>
                </div>
                <div class="activity-description">{{ activity.description }}</div>
                <div class="activity-meta">
                  <span class="activity-user">{{ activity.user }}</span>
                  <span class="activity-status" :class="activity.status">
                    {{ getStatusText(activity.status) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 系统性能 -->
        <div class="content-card">
          <div class="card-header">
            <h3>⚡ 系统性能</h3>
            <div class="performance-indicator" :class="systemHealth.status">
              <div class="indicator-dot"></div>
              <span>{{ systemHealth.text }}</span>
            </div>
          </div>
          <div class="performance-metrics">
            <div class="metric-item">
              <div class="metric-label">CPU 使用率</div>
              <div class="metric-bar">
                <div class="metric-fill" :style="{ width: systemHealth.cpu + '%' }"></div>
              </div>
              <div class="metric-value">{{ systemHealth.cpu }}%</div>
            </div>
            <div class="metric-item">
              <div class="metric-label">内存使用率</div>
              <div class="metric-bar">
                <div class="metric-fill" :style="{ width: systemHealth.memory + '%' }"></div>
              </div>
              <div class="metric-value">{{ systemHealth.memory }}%</div>
            </div>
            <div class="metric-item">
              <div class="metric-label">磁盘使用率</div>
              <div class="metric-bar">
                <div class="metric-fill" :style="{ width: systemHealth.disk + '%' }"></div>
              </div>
              <div class="metric-value">{{ systemHealth.disk }}%</div>
            </div>
          </div>
        </div>
      </div>

      <div class="content-right">
        <!-- 快捷操作 -->
        <div class="content-card">
          <div class="card-header">
            <h3>🚀 快捷操作</h3>
          </div>
          <div class="quick-actions">
            <button @click="$router.push('/licenses')" class="quick-action-btn">
              <div class="action-icon">📄</div>
              <div class="action-content">
                <div class="action-title">许可证管理</div>
                <div class="action-description">查看和管理所有许可证</div>
              </div>
              <div class="action-arrow">→</div>
            </button>
            
            <button @click="$router.push('/users')" class="quick-action-btn">
              <div class="action-icon">👥</div>
              <div class="action-content">
                <div class="action-title">用户管理</div>
                <div class="action-description">管理系统用户和权限</div>
              </div>
              <div class="action-arrow">→</div>
            </button>
            
            <button @click="$router.push('/reports')" class="quick-action-btn">
              <div class="action-icon">📊</div>
              <div class="action-content">
                <div class="action-title">统计报告</div>
                <div class="action-description">查看详细的使用统计</div>
              </div>
              <div class="action-arrow">→</div>
            </button>
            
            <button @click="showSettings" class="quick-action-btn">
              <div class="action-icon">⚙️</div>
              <div class="action-content">
                <div class="action-title">系统设置</div>
                <div class="action-description">配置系统参数</div>
              </div>
              <div class="action-arrow">→</div>
            </button>
          </div>
        </div>

        <!-- 系统状态 -->
        <div class="content-card">
          <div class="card-header">
            <h3>🔧 系统状态</h3>
            <div class="status-indicator healthy">
              <div class="status-dot"></div>
              <span>运行正常</span>
            </div>
          </div>
          <div class="system-status">
            <div class="status-group">
              <div class="status-item">
                <div class="status-icon">🗄️</div>
                <div class="status-info">
                  <div class="status-name">数据库</div>
                  <div class="status-detail">SQLite 连接正常</div>
                </div>
                <div class="status-badge healthy">正常</div>
              </div>
              
              <div class="status-item">
                <div class="status-icon">⚡</div>
                <div class="status-info">
                  <div class="status-name">缓存服务</div>
                  <div class="status-detail">Redis 连接异常</div>
                </div>
                <div class="status-badge warning">离线</div>
              </div>
              
              <div class="status-item">
                <div class="status-icon">🌐</div>
                <div class="status-info">
                  <div class="status-name">API 服务</div>
                  <div class="status-detail">响应时间 45ms</div>
                </div>
                <div class="status-badge healthy">正常</div>
              </div>
              
              <div class="status-item">
                <div class="status-icon">🔒</div>
                <div class="status-info">
                  <div class="status-name">安全服务</div>
                  <div class="status-detail">JWT 认证正常</div>
                </div>
                <div class="status-badge healthy">正常</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 最新通知 -->
        <div class="content-card">
          <div class="card-header">
            <h3>🔔 系统通知</h3>
            <button class="text-btn">标记已读</button>
          </div>
          <div class="notifications">
            <div class="notification-item unread">
              <div class="notification-icon">⚠️</div>
              <div class="notification-content">
                <div class="notification-title">许可证即将过期</div>
                <div class="notification-message">有 3 个许可证将在 7 天内过期</div>
                <div class="notification-time">2 小时前</div>
              </div>
            </div>
            
            <div class="notification-item">
              <div class="notification-icon">✅</div>
              <div class="notification-content">
                <div class="notification-title">系统更新完成</div>
                <div class="notification-message">系统已成功更新到最新版本</div>
                <div class="notification-time">1 天前</div>
              </div>
            </div>
            
            <div class="notification-item">
              <div class="notification-icon">📊</div>
              <div class="notification-content">
                <div class="notification-title">月度报告已生成</div>
                <div class="notification-message">本月使用统计报告已准备就绪</div>
                <div class="notification-time">3 天前</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const username = ref(localStorage.getItem('username') || '管理员')
const currentTime = ref('')
const loading = ref(false)
const selectedPeriod = ref('today')

// 统计数据
const stats = reactive({
  totalLicenses: 0,
  activeLicenses: 0,
  todayVerifications: 0,
  onlineDevices: 0
})

// 计算活跃率
const progressPercentage = computed(() => {
  if (stats.totalLicenses === 0) return 0
  return Math.round((stats.activeLicenses / stats.totalLicenses) * 100)
})

// 系统健康状态
const systemHealth = reactive({
  status: 'healthy',
  text: '系统运行正常',
  cpu: 35,
  memory: 68,
  disk: 42
})

// 活动数据
const activities = ref([
  {
    id: 1,
    type: 'create',
    title: '创建新许可证',
    description: '为客户 ABC Corp 创建了新的企业版许可证',
    user: 'admin',
    time: '30分钟前',
    status: 'success'
  },
  {
    id: 2,
    type: 'verify',
    title: '许可证验证',
    description: '许可证 DEMO-LICENSE-002 验证成功，设备 Device-001',
    user: 'system',
    time: '2小时前',
    status: 'success'
  },
  {
    id: 3,
    type: 'login',
    title: '用户登录',
    description: '用户 testuser 从 IP ************* 登录系统',
    user: 'testuser',
    time: '4小时前',
    status: 'info'
  },
  {
    id: 4,
    type: 'expire',
    title: '许可证过期警告',
    description: '许可证 OLD-LICENSE-003 将在 3 天后过期',
    user: 'system',
    time: '6小时前',
    status: 'warning'
  },
  {
    id: 5,
    type: 'device',
    title: '新设备连接',
    description: '设备 Device-005 首次连接到系统',
    user: 'system',
    time: '8小时前',
    status: 'info'
  }
])

// 更新时间
const updateTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit',
    weekday: 'long'
  })
}

// 获取活动图标
const getActivityIcon = (type: string) => {
  const icons = {
    create: '➕',
    verify: '✅',
    login: '👤',
    expire: '⚠️',
    device: '💻',
    update: '🔄',
    delete: '🗑️'
  }
  return icons[type] || '📋'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    success: '成功',
    info: '信息',
    warning: '警告',
    error: '错误'
  }
  return statusMap[status] || '未知'
}

// 加载统计数据
const loadStats = async () => {
  loading.value = true
  
  try {
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // 根据选择的时间段加载不同数据
    const multiplier = {
      today: 1,
      week: 7,
      month: 30,
      year: 365
    }[selectedPeriod.value] || 1
    
    // 动画效果
    const animateNumber = (target: number, key: string) => {
      let current = 0
      const increment = target / 30
      const timer = setInterval(() => {
        current += increment
        if (current >= target) {
          stats[key] = target
          clearInterval(timer)
        } else {
          stats[key] = Math.floor(current)
        }
      }, 30)
    }
    
    animateNumber(156 * multiplier, 'totalLicenses')
    animateNumber(142 * multiplier, 'activeLicenses')
    animateNumber(1247 * multiplier, 'todayVerifications')
    animateNumber(89, 'onlineDevices')
    
  } catch (error) {
    console.error('加载统计数据失败:', error)
  } finally {
    loading.value = false
  }
}

// 加载活动数据
const loadActivities = async () => {
  // 模拟刷新活动数据
  console.log('刷新活动数据')
}

// 刷新所有数据
const refreshData = async () => {
  await Promise.all([
    loadStats(),
    loadActivities()
  ])
}

// 显示设置
const showSettings = () => {
  alert('系统设置功能开发中...')
}

// 退出登录
const handleLogout = () => {
  if (confirm('确定要退出登录吗？')) {
    localStorage.removeItem('isLoggedIn')
    localStorage.removeItem('username')
    router.push('/login')
  }
}

onMounted(() => {
  updateTime()
  loadStats()
  
  // 每秒更新时间
  setInterval(updateTime, 1000)
  
  // 每30秒更新系统状态
  setInterval(() => {
    systemHealth.cpu = Math.max(20, Math.min(80, systemHealth.cpu + (Math.random() - 0.5) * 10))
    systemHealth.memory = Math.max(30, Math.min(90, systemHealth.memory + (Math.random() - 0.5) * 5))
    systemHealth.disk = Math.max(20, Math.min(70, systemHealth.disk + (Math.random() - 0.5) * 3))
  }, 30000)
})
</script>

<style scoped>
.enhanced-dashboard {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  background-attachment: fixed;
}

/* 顶部导航栏 */
.top-navbar {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  padding: 16px 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 20px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.navbar-left {
  display: flex;
  align-items: center;
  gap: 24px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
  color: #1890ff;
}

.logo-icon {
  font-size: 24px;
}

.logo-text {
  font-size: 18px;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #666;
}

.breadcrumb-item.active {
  color: #1890ff;
  font-weight: 500;
}

.navbar-right {
  display: flex;
  align-items: center;
  gap: 24px;
}

.time-display {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 16px;
  background: rgba(24, 144, 255, 0.1);
  border-radius: 20px;
  font-family: 'Courier New', monospace;
  font-size: 14px;
  color: #1890ff;
}

.user-menu {
  display: flex;
  align-items: center;
  gap: 12px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #1890ff, #722ed1);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-weight: bold;
  font-size: 16px;
}

.user-info {
  display: flex;
  flex-direction: column;
}

.user-name {
  font-weight: 500;
  color: #262626;
}

.user-role {
  font-size: 12px;
  color: #8c8c8c;
}

.user-actions {
  display: flex;
  gap: 8px;
}

.action-btn {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.profile-btn {
  background: #f0f0f0;
  color: #666;
}

.profile-btn:hover {
  background: #e6f7ff;
  color: #1890ff;
}

.logout-btn {
  background: #ff4d4f;
  color: white;
}

.logout-btn:hover {
  background: #ff7875;
}

/* 欢迎横幅 */
.welcome-banner {
  margin: 24px;
  padding: 32px;
  background: linear-gradient(135deg, rgba(255, 255, 255, 0.95), rgba(255, 255, 255, 0.85));
  backdrop-filter: blur(10px);
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: hidden;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.banner-content {
  flex: 1;
}

.welcome-text h1 {
  margin: 0 0 8px 0;
  font-size: 28px;
  color: #262626;
  font-weight: 600;
}

.welcome-text p {
  margin: 0 0 24px 0;
  color: #666;
  font-size: 16px;
}

.banner-actions {
  display: flex;
  gap: 12px;
}

.primary-btn {
  padding: 12px 24px;
  background: linear-gradient(135deg, #1890ff, #722ed1);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s;
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.3);
}

.primary-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(24, 144, 255, 0.4);
}

.secondary-btn {
  padding: 12px 24px;
  background: rgba(255, 255, 255, 0.8);
  color: #666;
  border: 1px solid #d9d9d9;
  border-radius: 8px;
  font-size: 16px;
  cursor: pointer;
  transition: all 0.2s;
}

.secondary-btn:hover:not(:disabled) {
  background: white;
  border-color: #1890ff;
  color: #1890ff;
}

.secondary-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.banner-decoration {
  position: absolute;
  right: -50px;
  top: -50px;
  width: 200px;
  height: 200px;
}

.decoration-circle {
  position: absolute;
  border-radius: 50%;
  opacity: 0.1;
}

.circle-1 {
  width: 100px;
  height: 100px;
  background: #1890ff;
  top: 0;
  right: 0;
}

.circle-2 {
  width: 60px;
  height: 60px;
  background: #722ed1;
  top: 40px;
  right: 60px;
}

.circle-3 {
  width: 40px;
  height: 40px;
  background: #52c41a;
  top: 80px;
  right: 20px;
}

/* 统计区域 */
.stats-section {
  margin: 0 24px 24px;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.section-header h2 {
  margin: 0;
  color: white;
  font-size: 20px;
  font-weight: 600;
  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.period-selector {
  padding: 8px 16px;
  border: none;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.9);
  color: #666;
  cursor: pointer;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.stat-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);
}

.stat-card.loading {
  opacity: 0.7;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
}

.stat-card.primary::before {
  background: linear-gradient(90deg, #1890ff, #722ed1);
}

.stat-card.success::before {
  background: linear-gradient(90deg, #52c41a, #73d13d);
}

.stat-card.warning::before {
  background: linear-gradient(90deg, #faad14, #ffc53d);
}

.stat-card.info::before {
  background: linear-gradient(90deg, #13c2c2, #36cfc9);
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  background: rgba(24, 144, 255, 0.1);
}

.stat-trend {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.stat-trend.positive {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.stat-content {
  margin-bottom: 16px;
}

.stat-value {
  font-size: 36px;
  font-weight: bold;
  color: #262626;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 16px;
  color: #666;
  margin-bottom: 4px;
}

.stat-description {
  font-size: 12px;
  color: #8c8c8c;
}

.stat-chart {
  height: 40px;
  display: flex;
  align-items: end;
}

.mini-chart {
  display: flex;
  align-items: end;
  gap: 4px;
  height: 100%;
}

.chart-bar {
  width: 8px;
  background: linear-gradient(to top, #1890ff, #722ed1);
  border-radius: 4px 4px 0 0;
  opacity: 0.7;
}

.stat-progress {
  margin-top: 16px;
}

.progress-bar {
  height: 6px;
  background: #f0f0f0;
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #52c41a, #73d13d);
  border-radius: 3px;
  transition: width 0.3s;
}

.progress-text {
  font-size: 12px;
  color: #666;
  text-align: center;
}

.stat-footer {
  display: flex;
  justify-content: space-between;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #f0f0f0;
}

.footer-item {
  text-align: center;
}

.footer-label {
  display: block;
  font-size: 12px;
  color: #8c8c8c;
  margin-bottom: 4px;
}

.footer-value {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.device-status {
  display: flex;
  gap: 16px;
  margin-top: 16px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: #666;
}

.status-dot {
  width: 8px;
  height: 8px;
  border-radius: 50%;
}

.status-dot.online {
  background: #52c41a;
}

.status-dot.offline {
  background: #d9d9d9;
}

/* 主要内容区域 */
.main-content {
  display: grid;
  grid-template-columns: 2fr 1fr;
  gap: 24px;
  margin: 0 24px 24px;
}

.content-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  margin-bottom: 24px;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  color: #262626;
  font-weight: 600;
}

.card-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.icon-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background: #f5f5f5;
  color: #666;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.icon-btn:hover:not(:disabled) {
  background: #e6f7ff;
  color: #1890ff;
}

.text-btn {
  padding: 6px 12px;
  border: none;
  background: none;
  color: #1890ff;
  cursor: pointer;
  font-size: 14px;
  border-radius: 4px;
  transition: all 0.2s;
}

.text-btn:hover {
  background: #e6f7ff;
}

/* 活动时间线 */
.activity-timeline {
  position: relative;
}

.timeline-item {
  display: flex;
  margin-bottom: 24px;
  position: relative;
}

.timeline-item:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 23px;
  top: 48px;
  bottom: -24px;
  width: 2px;
  background: #f0f0f0;
}

.timeline-marker {
  width: 48px;
  height: 48px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  flex-shrink: 0;
  position: relative;
  z-index: 1;
}

.timeline-marker.create {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.timeline-marker.verify {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

.timeline-marker.login {
  background: rgba(114, 46, 209, 0.1);
  color: #722ed1;
}

.timeline-marker.expire {
  background: rgba(250, 173, 20, 0.1);
  color: #faad14;
}

.timeline-marker.device {
  background: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
}

.marker-icon {
  font-size: 20px;
}

.timeline-content {
  flex: 1;
  padding-top: 4px;
}

.activity-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.activity-title {
  font-weight: 500;
  color: #262626;
}

.activity-time {
  font-size: 12px;
  color: #8c8c8c;
}

.activity-description {
  color: #666;
  font-size: 14px;
  margin-bottom: 8px;
  line-height: 1.5;
}

.activity-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.activity-user {
  font-size: 12px;
  color: #8c8c8c;
}

.activity-status {
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 500;
}

.activity-status.success {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.activity-status.info {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

.activity-status.warning {
  background: rgba(250, 173, 20, 0.1);
  color: #faad14;
}

/* 性能指标 */
.performance-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.performance-indicator.healthy {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.indicator-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
}

.performance-metrics {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.metric-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.metric-label {
  width: 80px;
  font-size: 14px;
  color: #666;
}

.metric-bar {
  flex: 1;
  height: 8px;
  background: #f0f0f0;
  border-radius: 4px;
  overflow: hidden;
}

.metric-fill {
  height: 100%;
  background: linear-gradient(90deg, #52c41a, #faad14, #ff4d4f);
  border-radius: 4px;
  transition: width 0.3s;
}

.metric-value {
  width: 40px;
  text-align: right;
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

/* 快捷操作 */
.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.quick-action-btn {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 12px;
  background: white;
  cursor: pointer;
  transition: all 0.2s;
  text-align: left;
}

.quick-action-btn:hover {
  border-color: #1890ff;
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.1);
  transform: translateY(-2px);
}

.action-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  margin-right: 16px;
}

.action-content {
  flex: 1;
}

.action-title {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.action-description {
  font-size: 12px;
  color: #8c8c8c;
}

.action-arrow {
  font-size: 18px;
  color: #d9d9d9;
  transition: all 0.2s;
}

.quick-action-btn:hover .action-arrow {
  color: #1890ff;
  transform: translateX(4px);
}

/* 系统状态 */
.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-indicator.healthy {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
}

.system-status {
  display: flex;
  flex-direction: column;
}

.status-group {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.status-info {
  flex: 1;
}

.status-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 2px;
}

.status-detail {
  font-size: 12px;
  color: #8c8c8c;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 500;
}

.status-badge.healthy {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.status-badge.warning {
  background: rgba(250, 173, 20, 0.1);
  color: #faad14;
}

/* 通知 */
.notifications {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  border-radius: 12px;
  background: #fafafa;
  transition: all 0.2s;
}

.notification-item.unread {
  background: rgba(24, 144, 255, 0.05);
  border-left: 3px solid #1890ff;
}

.notification-item:hover {
  background: #f0f0f0;
}

.notification-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  flex-shrink: 0;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.notification-message {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  line-height: 1.4;
}

.notification-time {
  font-size: 12px;
  color: #8c8c8c;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .main-content {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .enhanced-dashboard {
    background: #f5f5f5;
  }
  
  .top-navbar {
    flex-direction: column;
    gap: 16px;
    align-items: flex-start;
  }
  
  .navbar-left,
  .navbar-right {
    width: 100%;
    justify-content: space-between;
  }
  
  .welcome-banner {
    margin: 16px;
    padding: 24px;
    flex-direction: column;
    text-align: center;
  }
  
  .banner-actions {
    width: 100%;
    justify-content: center;
  }
  
  .stats-section {
    margin: 0 16px 16px;
  }
  
  .stats-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .main-content {
    margin: 0 16px 16px;
    gap: 16px;
  }
  
  .content-card {
    margin-bottom: 16px;
  }
}

@media (max-width: 480px) {
  .top-navbar {
    padding: 12px 16px;
  }
  
  .logo-text {
    display: none;
  }
  
  .time-display {
    font-size: 12px;
  }
  
  .user-actions {
    flex-direction: column;
    width: 100%;
  }
  
  .welcome-banner {
    padding: 20px;
  }
  
  .welcome-text h1 {
    font-size: 24px;
  }
  
  .banner-actions {
    flex-direction: column;
    gap: 8px;
  }
  
  .primary-btn,
  .secondary-btn {
    width: 100%;
  }
}
</style>