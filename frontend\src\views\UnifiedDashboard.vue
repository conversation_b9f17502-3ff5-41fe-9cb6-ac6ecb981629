<template>
  <MainLayout>
    <div class="dashboard-container">
      <!-- 欢迎卡片 -->
      <div class="welcome-card">
        <div class="welcome-content">
          <div class="welcome-text">
            <h2>欢迎回来，{{ username }}！</h2>
            <p>今天是 {{ todayDate }}，系统运行正常</p>
          </div>
          <div class="welcome-actions">
            <button @click="$router.push('/licenses/create')" class="primary-btn">
              ➕ 创建许可证
            </button>
            <button @click="refreshData" class="secondary-btn" :disabled="loading">
              {{ loading ? '🔄' : '↻' }} 刷新数据
            </button>
          </div>
        </div>
      </div>

      <!-- 统计卡片 -->
      <div class="stats-grid">
        <div class="stat-card primary">
          <div class="stat-header">
            <div class="stat-icon">📄</div>
            <div class="stat-trend positive">+12%</div>
          </div>
          <div class="stat-body">
            <div class="stat-value">{{ stats.totalLicenses }}</div>
            <div class="stat-label">总许可证数</div>
            <div class="stat-description">较上月增长 12%</div>
          </div>
        </div>

        <div class="stat-card success">
          <div class="stat-header">
            <div class="stat-icon">✅</div>
            <div class="stat-trend positive">+8%</div>
          </div>
          <div class="stat-body">
            <div class="stat-value">{{ stats.activeLicenses }}</div>
            <div class="stat-label">活跃许可证</div>
            <div class="stat-description">正常运行中</div>
          </div>
        </div>

        <div class="stat-card warning">
          <div class="stat-header">
            <div class="stat-icon">🔍</div>
            <div class="stat-trend positive">+15%</div>
          </div>
          <div class="stat-body">
            <div class="stat-value">{{ stats.todayVerifications }}</div>
            <div class="stat-label">今日验证</div>
            <div class="stat-description">验证请求次数</div>
          </div>
        </div>

        <div class="stat-card info">
          <div class="stat-header">
            <div class="stat-icon">💻</div>
            <div class="stat-trend positive">+3%</div>
          </div>
          <div class="stat-body">
            <div class="stat-value">{{ stats.onlineDevices }}</div>
            <div class="stat-label">在线设备</div>
            <div class="stat-description">当前连接设备</div>
          </div>
        </div>
      </div>

      <!-- 主要内容区域 -->
      <div class="content-grid">
        <!-- 最近活动 -->
        <div class="content-card">
          <div class="card-header">
            <h3>📋 最近活动</h3>
            <button @click="loadActivities" class="refresh-btn">刷新</button>
          </div>
          <div class="activity-list">
            <div v-for="activity in activities" :key="activity.id" class="activity-item">
              <div class="activity-icon" :class="activity.type">
                {{ getActivityIcon(activity.type) }}
              </div>
              <div class="activity-content">
                <div class="activity-title">{{ activity.title }}</div>
                <div class="activity-description">{{ activity.description }}</div>
                <div class="activity-meta">
                  <span class="activity-time">{{ activity.time }}</span>
                  <span class="activity-status" :class="activity.status">
                    {{ getStatusText(activity.status) }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 快捷操作 -->
        <div class="content-card">
          <div class="card-header">
            <h3>🚀 快捷操作</h3>
          </div>
          <div class="quick-actions">
            <button @click="$router.push('/licenses')" class="action-item">
              <div class="action-icon">📄</div>
              <div class="action-text">
                <div class="action-title">许可证管理</div>
                <div class="action-desc">查看和管理所有许可证</div>
              </div>
              <div class="action-arrow">→</div>
            </button>

            <button @click="$router.push('/users')" class="action-item">
              <div class="action-icon">👥</div>
              <div class="action-text">
                <div class="action-title">用户管理</div>
                <div class="action-desc">管理系统用户和权限</div>
              </div>
              <div class="action-arrow">→</div>
            </button>

            <button @click="$router.push('/reports')" class="action-item">
              <div class="action-icon">📊</div>
              <div class="action-text">
                <div class="action-title">统计报告</div>
                <div class="action-desc">查看详细的使用统计</div>
              </div>
              <div class="action-arrow">→</div>
            </button>

            <button @click="showSettings" class="action-item">
              <div class="action-icon">⚙️</div>
              <div class="action-text">
                <div class="action-title">系统设置</div>
                <div class="action-desc">配置系统参数</div>
              </div>
              <div class="action-arrow">→</div>
            </button>
          </div>
        </div>

        <!-- 系统状态 -->
        <div class="content-card">
          <div class="card-header">
            <h3>🔧 系统状态</h3>
            <div class="status-indicator healthy">
              <div class="status-dot"></div>
              <span>运行正常</span>
            </div>
          </div>
          <div class="system-status">
            <div class="status-item">
              <div class="status-icon">🗄️</div>
              <div class="status-info">
                <div class="status-name">数据库</div>
                <div class="status-detail">SQLite 连接正常</div>
              </div>
              <div class="status-badge healthy">正常</div>
            </div>

            <div class="status-item">
              <div class="status-icon">⚡</div>
              <div class="status-info">
                <div class="status-name">缓存服务</div>
                <div class="status-detail">Redis 连接异常</div>
              </div>
              <div class="status-badge warning">离线</div>
            </div>

            <div class="status-item">
              <div class="status-icon">🌐</div>
              <div class="status-info">
                <div class="status-name">API 服务</div>
                <div class="status-detail">响应时间 45ms</div>
              </div>
              <div class="status-badge healthy">正常</div>
            </div>

            <div class="status-item">
              <div class="status-icon">🔒</div>
              <div class="status-info">
                <div class="status-name">安全服务</div>
                <div class="status-detail">JWT 认证正常</div>
              </div>
              <div class="status-badge healthy">正常</div>
            </div>
          </div>
        </div>

        <!-- 系统通知 -->
        <div class="content-card">
          <div class="card-header">
            <h3>🔔 系统通知</h3>
            <button class="text-btn">标记已读</button>
          </div>
          <div class="notifications">
            <div class="notification-item unread">
              <div class="notification-icon">⚠️</div>
              <div class="notification-content">
                <div class="notification-title">许可证即将过期</div>
                <div class="notification-message">有 3 个许可证将在 7 天内过期</div>
                <div class="notification-time">2 小时前</div>
              </div>
            </div>

            <div class="notification-item">
              <div class="notification-icon">✅</div>
              <div class="notification-content">
                <div class="notification-title">系统更新完成</div>
                <div class="notification-message">系统已成功更新到最新版本</div>
                <div class="notification-time">1 天前</div>
              </div>
            </div>

            <div class="notification-item">
              <div class="notification-icon">📊</div>
              <div class="notification-content">
                <div class="notification-title">月度报告已生成</div>
                <div class="notification-message">本月使用统计报告已准备就绪</div>
                <div class="notification-time">3 天前</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </MainLayout>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import MainLayout from '@/layouts/MainLayout.vue'
import { getDashboardOverview, getDashboardStats, getRecentActivities } from '@/api/dashboard'
import type { DashboardOverview } from '@/api/dashboard'

const router = useRouter()
const username = ref(localStorage.getItem('username') || '管理员')
const loading = ref(false)
const todayDate = ref('')

// 统计数据
const stats = reactive({
  totalLicenses: 0,
  activeLicenses: 0,
  todayVerifications: 0,
  onlineDevices: 0
})

// 活动数据
const activities = ref([
  {
    id: 1,
    type: 'create',
    title: '创建新许可证',
    description: '为客户 ABC Corp 创建了新的企业版许可证',
    time: '30分钟前',
    status: 'success'
  },
  {
    id: 2,
    type: 'verify',
    title: '许可证验证',
    description: '许可证 DEMO-LICENSE-002 验证成功',
    time: '2小时前',
    status: 'success'
  },
  {
    id: 3,
    type: 'login',
    title: '用户登录',
    description: '用户 testuser 登录系统',
    time: '4小时前',
    status: 'info'
  },
  {
    id: 4,
    type: 'expire',
    title: '许可证过期警告',
    description: '许可证 OLD-LICENSE-003 将在 3 天后过期',
    time: '6小时前',
    status: 'warning'
  }
])

// 获取活动图标
const getActivityIcon = (type: string) => {
  const icons = {
    create: '➕',
    verify: '✅',
    login: '👤',
    expire: '⚠️',
    device: '💻'
  }
  return icons[type] || '📋'
}

// 获取状态文本
const getStatusText = (status: string) => {
  const statusMap = {
    success: '成功',
    info: '信息',
    warning: '警告',
    error: '错误'
  }
  return statusMap[status] || '未知'
}

// 加载仪表板数据
const loadDashboardData = async () => {
  loading.value = true
  try {
    const overview = await getDashboardOverview()

    // 更新统计数据
    stats.totalLicenses = overview.overview.total_licenses
    stats.activeLicenses = overview.license_status.active
    stats.todayVerifications = overview.recent_activity.verifications
    stats.onlineDevices = overview.overview.active_devices

    console.log('仪表板数据加载成功', overview)
  } catch (error) {
    console.error('加载仪表板数据失败:', error)
    // 保持默认值或显示错误提示
  } finally {
    loading.value = false
  }
}

// 刷新数据
const refreshData = async () => {
  await loadDashboardData()
  await loadActivities()
}

// 加载活动
const loadActivities = async () => {
  try {
    const newActivities = await getRecentActivities()
    activities.value = newActivities
  } catch (error) {
    console.error('加载活动数据失败:', error)
  }
}

// 显示设置
const showSettings = () => {
  alert('系统设置功能开发中...')
}

// 更新日期
const updateDate = () => {
  const now = new Date()
  todayDate.value = now.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    weekday: 'long'
  })
}

onMounted(async () => {
  updateDate()
  await loadDashboardData()
  await loadActivities()
})
</script>

<style scoped>
.dashboard-container {
  max-width: 1400px;
  margin: 0 auto;
}

/* 欢迎卡片 */
.welcome-card {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  padding: 32px;
  margin-bottom: 24px;
  color: white;
  box-shadow: 0 8px 32px rgba(102, 126, 234, 0.3);
}

.welcome-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.welcome-text h2 {
  margin: 0 0 8px 0;
  font-size: 28px;
  font-weight: 600;
}

.welcome-text p {
  margin: 0;
  opacity: 0.9;
  font-size: 16px;
}

.welcome-actions {
  display: flex;
  gap: 12px;
}

.primary-btn {
  padding: 12px 24px;
  background: rgba(255, 255, 255, 0.2);
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.3);
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  font-weight: 500;
  transition: all 0.2s;
  backdrop-filter: blur(10px);
}

.primary-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-2px);
}

.secondary-btn {
  padding: 12px 24px;
  background: transparent;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.5);
  border-radius: 8px;
  cursor: pointer;
  font-size: 16px;
  transition: all 0.2s;
}

.secondary-btn:hover:not(:disabled) {
  background: rgba(255, 255, 255, 0.1);
}

.secondary-btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

/* 统计卡片网格 */
.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  margin-bottom: 24px;
}

.stat-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 30px rgba(0, 0, 0, 0.12);
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
}

.stat-card.primary::before {
  background: linear-gradient(90deg, #1890ff, #722ed1);
}

.stat-card.success::before {
  background: linear-gradient(90deg, #52c41a, #73d13d);
}

.stat-card.warning::before {
  background: linear-gradient(90deg, #faad14, #ffc53d);
}

.stat-card.info::before {
  background: linear-gradient(90deg, #13c2c2, #36cfc9);
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: rgba(24, 144, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
}

.stat-trend {
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.stat-trend.positive {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.stat-body {
  text-align: left;
}

.stat-value {
  font-size: 36px;
  font-weight: bold;
  color: #262626;
  margin-bottom: 8px;
}

.stat-label {
  font-size: 16px;
  color: #666;
  margin-bottom: 4px;
}

.stat-description {
  font-size: 12px;
  color: #8c8c8c;
}

/* 内容网格 */
.content-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.content-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
  height: fit-content;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding-bottom: 16px;
  border-bottom: 1px solid #f0f0f0;
}

.card-header h3 {
  margin: 0;
  font-size: 18px;
  color: #262626;
  font-weight: 600;
}

.refresh-btn {
  padding: 6px 12px;
  background: #f5f5f5;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-size: 12px;
  color: #666;
  transition: all 0.2s;
}

.refresh-btn:hover {
  background: #e6f7ff;
  color: #1890ff;
}

.text-btn {
  padding: 6px 12px;
  background: none;
  border: none;
  color: #1890ff;
  cursor: pointer;
  font-size: 14px;
  border-radius: 4px;
  transition: all 0.2s;
}

.text-btn:hover {
  background: #e6f7ff;
}

/* 活动列表 */
.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  border-radius: 12px;
  background: #fafafa;
  transition: all 0.2s;
}

.activity-item:hover {
  background: #f0f0f0;
}

.activity-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  flex-shrink: 0;
}

.activity-icon.create {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.activity-icon.verify {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

.activity-icon.login {
  background: rgba(114, 46, 209, 0.1);
  color: #722ed1;
}

.activity-icon.expire {
  background: rgba(250, 173, 20, 0.1);
  color: #faad14;
}

.activity-content {
  flex: 1;
}

.activity-title {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.activity-description {
  color: #666;
  font-size: 14px;
  margin-bottom: 8px;
  line-height: 1.4;
}

.activity-meta {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.activity-time {
  font-size: 12px;
  color: #8c8c8c;
}

.activity-status {
  padding: 2px 8px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 500;
}

.activity-status.success {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.activity-status.info {
  background: rgba(24, 144, 255, 0.1);
  color: #1890ff;
}

.activity-status.warning {
  background: rgba(250, 173, 20, 0.1);
  color: #faad14;
}

/* 快捷操作 */
.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 16px;
  border: 1px solid #f0f0f0;
  border-radius: 12px;
  background: white;
  cursor: pointer;
  transition: all 0.2s;
  text-align: left;
  width: 100%;
}

.action-item:hover {
  border-color: #1890ff;
  box-shadow: 0 4px 16px rgba(24, 144, 255, 0.1);
  transform: translateY(-2px);
}

.action-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  margin-right: 16px;
}

.action-text {
  flex: 1;
}

.action-title {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.action-desc {
  font-size: 12px;
  color: #8c8c8c;
}

.action-arrow {
  font-size: 18px;
  color: #d9d9d9;
  transition: all 0.2s;
}

.action-item:hover .action-arrow {
  color: #1890ff;
  transform: translateX(4px);
}

/* 系统状态 */
.status-indicator {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.status-indicator.healthy {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.status-dot {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background: currentColor;
}

.system-status {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.status-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.status-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
}

.status-info {
  flex: 1;
}

.status-name {
  font-weight: 500;
  color: #262626;
  margin-bottom: 2px;
}

.status-detail {
  font-size: 12px;
  color: #8c8c8c;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 10px;
  font-size: 11px;
  font-weight: 500;
}

.status-badge.healthy {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.status-badge.warning {
  background: rgba(250, 173, 20, 0.1);
  color: #faad14;
}

/* 通知 */
.notifications {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.notification-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  border-radius: 12px;
  background: #fafafa;
  transition: all 0.2s;
}

.notification-item.unread {
  background: rgba(24, 144, 255, 0.05);
  border-left: 3px solid #1890ff;
}

.notification-item:hover {
  background: #f0f0f0;
}

.notification-icon {
  width: 32px;
  height: 32px;
  border-radius: 8px;
  background: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  flex-shrink: 0;
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-weight: 500;
  color: #262626;
  margin-bottom: 4px;
}

.notification-message {
  font-size: 14px;
  color: #666;
  margin-bottom: 8px;
  line-height: 1.4;
}

.notification-time {
  font-size: 12px;
  color: #8c8c8c;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .content-grid {
    grid-template-columns: 1fr;
  }
}

@media (max-width: 768px) {
  .welcome-content {
    flex-direction: column;
    text-align: center;
    gap: 20px;
  }
  
  .welcome-actions {
    width: 100%;
    justify-content: center;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 16px;
  }
  
  .content-grid {
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
  
  .welcome-actions {
    flex-direction: column;
    gap: 8px;
  }
  
  .primary-btn,
  .secondary-btn {
    width: 100%;
  }
}
</style>