package model

import (
	"time"
	"gorm.io/gorm"
)

// VerificationLog 验证日志模型
type VerificationLog struct {
	ID                 uint           `json:"id" gorm:"primarykey"`
	LicenseKey         string         `json:"license_key" gorm:"size:255;not null;index"`
	DeviceID           string         `json:"device_id" gorm:"size:255;index"`
	IPAddress          string         `json:"ip_address" gorm:"size:45"`
	UserAgent          string         `json:"user_agent" gorm:"type:text"`
	VerificationResult string         `json:"verification_result" gorm:"size:20;not null"`
	ErrorMessage       string         `json:"error_message" gorm:"type:text"`
	ResponseTime       int64          `json:"response_time"` // 响应时间（毫秒）
	CreatedAt          time.Time      `json:"created_at"`
	UpdatedAt          time.Time      `json:"updated_at"`
	DeletedAt          gorm.DeletedAt `json:"-" gorm:"index"`
	
	// 关联关系
	License License `json:"license,omitempty" gorm:"foreignKey:LicenseKey;references:LicenseKey"`
}

// TableName 指定表名
func (VerificationLog) TableName() string {
	return "verification_logs"
}

// VerificationResult 验证结果常量
const (
	VerificationResultSuccess          = "success"
	VerificationResultFailed           = "failed"
	VerificationResultExpired          = "expired"
	VerificationResultDisabled         = "disabled"
	VerificationResultDeviceLimitExceeded = "device_limit_exceeded"
	VerificationResultFeatureNotAllowed   = "feature_not_allowed"
	VerificationResultInvalidKey       = "invalid_key"
	VerificationResultNetworkError     = "network_error"
)

// IsSuccess 检查验证是否成功
func (v *VerificationLog) IsSuccess() bool {
	return v.VerificationResult == VerificationResultSuccess
}

// SetError 设置错误信息
func (v *VerificationLog) SetError(result, message string) {
	v.VerificationResult = result
	v.ErrorMessage = message
}

// SetSuccess 设置成功状态
func (v *VerificationLog) SetSuccess() {
	v.VerificationResult = VerificationResultSuccess
	v.ErrorMessage = ""
}