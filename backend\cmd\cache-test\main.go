package main

import (
	"context"
	"fmt"
	"log"
	"time"
	"software-auth/internal/config"
	"software-auth/internal/cache"
)

func main() {
	fmt.Println("Cache System Test")
	fmt.Println("=================")

	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	fmt.Printf("Redis Address: %s\n", cfg.Redis.Addr)
	fmt.Printf("Redis DB: %d\n", cfg.Redis.DB)

	// 尝试初始化Redis
	fmt.Println("\nTrying to connect to Redis...")
	err = cache.Initialize(&cfg.Redis)
	if err != nil {
		fmt.Printf("❌ Redis connection failed: %v\n", err)
		fmt.Println("\n📝 To fix this issue:")
		fmt.Println("1. Install Redis for Windows:")
		fmt.Println("   - Download from: https://github.com/microsoftarchive/redis/releases")
		fmt.Println("   - Or use Chocolatey: choco install redis-64")
		fmt.Println("2. Start Redis server: redis-server")
		fmt.Println("3. Or run: .\\scripts\\setup-redis.bat")
		fmt.Println("\n✅ The application can still work without Redis (with reduced performance)")
		return
	}
	defer cache.Close()

	fmt.Println("✅ Redis connected successfully!")

	// 运行缓存测试
	if err := runCacheTests(); err != nil {
		log.Fatalf("Cache tests failed: %v", err)
	}

	fmt.Println("\n🎉 All cache tests passed!")
}

func runCacheTests() error {
	ctx := context.Background()
	manager := cache.NewManager()

	fmt.Println("\n🔍 Testing cache operations...")

	// 测试1: 基本字符串缓存
	fmt.Println("1. Testing basic string cache...")
	service := cache.NewCacheService()
	
	testKey := "test:basic"
	testValue := "Hello Cache!"
	
	if err := service.Set(ctx, testKey, testValue, 1*time.Minute); err != nil {
		return fmt.Errorf("failed to set cache: %v", err)
	}
	
	result, err := service.Get(ctx, testKey)
	if err != nil {
		return fmt.Errorf("failed to get cache: %v", err)
	}
	
	if result != testValue {
		return fmt.Errorf("cache value mismatch: expected %s, got %s", testValue, result)
	}
	fmt.Println("   ✅ Basic cache operations work")

	// 测试2: JWT令牌缓存
	fmt.Println("2. Testing JWT token cache...")
	tokenData := &cache.AuthTokenData{
		UserID:   1,
		Username: "admin",
		Role:     "admin",
		IssuedAt: time.Now().Unix(),
	}

	tokenID := "test-jwt-token"
	if err := manager.SetAuthToken(ctx, tokenID, tokenData); err != nil {
		return fmt.Errorf("failed to set auth token: %v", err)
	}

	retrievedToken, err := manager.GetAuthToken(ctx, tokenID)
	if err != nil {
		return fmt.Errorf("failed to get auth token: %v", err)
	}

	if retrievedToken.Username != tokenData.Username {
		return fmt.Errorf("token data mismatch")
	}
	fmt.Println("   ✅ JWT token cache works")

	// 测试3: 许可证验证缓存
	fmt.Println("3. Testing license verification cache...")
	licenseData := &cache.LicenseVerifyData{
		Valid:       true,
		LicenseKey:  "TEST-LICENSE-KEY",
		ProductName: "Test Software",
		MaxDevices:  5,
		Features:    []string{"basic", "premium"},
		DeviceCount: 2,
		VerifiedAt:  time.Now(),
	}

	if err := manager.SetLicenseVerifyResult(ctx, licenseData.LicenseKey, licenseData); err != nil {
		return fmt.Errorf("failed to set license cache: %v", err)
	}

	retrievedLicense, err := manager.GetLicenseVerifyResult(ctx, licenseData.LicenseKey)
	if err != nil {
		return fmt.Errorf("failed to get license cache: %v", err)
	}

	if retrievedLicense.ProductName != licenseData.ProductName {
		return fmt.Errorf("license data mismatch")
	}
	fmt.Println("   ✅ License verification cache works")

	// 测试4: 用户会话缓存
	fmt.Println("4. Testing user session cache...")
	sessionData := &cache.UserSessionData{
		UserID:    1,
		Username:  "admin",
		Role:      "admin",
		LastLogin: time.Now(),
		IPAddress: "127.0.0.1",
	}

	if err := manager.SetUserSession(ctx, sessionData.UserID, sessionData); err != nil {
		return fmt.Errorf("failed to set user session: %v", err)
	}

	retrievedSession, err := manager.GetUserSession(ctx, sessionData.UserID)
	if err != nil {
		return fmt.Errorf("failed to get user session: %v", err)
	}

	if retrievedSession.Username != sessionData.Username {
		return fmt.Errorf("session data mismatch")
	}
	fmt.Println("   ✅ User session cache works")

	// 测试5: 限流功能
	fmt.Println("5. Testing rate limiting...")
	allowed, err := manager.CheckRateLimit(ctx, "127.0.0.1", "/api/test", 10)
	if err != nil {
		return fmt.Errorf("failed to check rate limit: %v", err)
	}

	if !allowed {
		return fmt.Errorf("rate limit should allow first request")
	}
	fmt.Println("   ✅ Rate limiting works")

	// 测试6: 缓存过期和清理
	fmt.Println("6. Testing cache expiration and cleanup...")
	shortKey := "test:expire"
	if err := service.Set(ctx, shortKey, "expire-test", 1*time.Second); err != nil {
		return fmt.Errorf("failed to set expiring cache: %v", err)
	}

	// 等待过期
	time.Sleep(2 * time.Second)
	
	_, err = service.Get(ctx, shortKey)
	if err != cache.ErrCacheNotFound {
		return fmt.Errorf("cache should have expired")
	}
	fmt.Println("   ✅ Cache expiration works")

	// 清理测试数据
	fmt.Println("7. Cleaning up test data...")
	if err := manager.DeleteAuthToken(ctx, tokenID); err != nil {
		return fmt.Errorf("failed to delete auth token: %v", err)
	}

	if err := manager.InvalidateLicenseCache(ctx, licenseData.LicenseKey); err != nil {
		return fmt.Errorf("failed to invalidate license cache: %v", err)
	}

	if err := manager.DeleteUserSession(ctx, sessionData.UserID); err != nil {
		return fmt.Errorf("failed to delete user session: %v", err)
	}

	if err := service.Delete(ctx, testKey); err != nil {
		return fmt.Errorf("failed to delete test key: %v", err)
	}
	fmt.Println("   ✅ Cache cleanup works")

	return nil
}