<template>
  <div class="report-list-page">
    <PageHeader 
      title="统计报告" 
      description="查看系统使用统计和分析报告"
    >
      <template #extra>
        <a-space>
          <a-button @click="handleRefresh" :loading="loading">
            <template #icon>
              <icon-refresh />
            </template>
            刷新
          </a-button>
          <a-button type="primary">
            <template #icon>
              <icon-download />
            </template>
            导出报告
          </a-button>
        </a-space>
      </template>
    </PageHeader>

    <div class="coming-soon">
      <EmptyState 
        type="no-data"
        title="功能开发中"
        description="统计报告功能正在开发中，敬请期待"
      >
        <template #action>
          <a-button type="primary" @click="$router.push('/dashboard')">
            返回仪表板
          </a-button>
        </template>
      </EmptyState>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import PageHeader from '@/components/common/PageHeader.vue'
import EmptyState from '@/components/common/EmptyState.vue'

const loading = ref(false)

const handleRefresh = () => {
  // TODO: 实现刷新逻辑
}
</script>

<style scoped>
.report-list-page {
  padding: 24px;
}

.coming-soon {
  margin-top: 100px;
}
</style>