package api

import (
	"net/http"
	"time"

	"github.com/gin-gonic/gin"
)

// SuccessResult 成功响应结构
type SuccessResult struct {
	Message   string      `json:"message"`
	Data      interface{} `json:"data,omitempty"`
	Timestamp int64       `json:"timestamp"`
}

// ErrorResult 错误响应结构
type ErrorResult struct {
	Error     string      `json:"error"`
	Message   string      `json:"message,omitempty"`
	Code      string      `json:"code,omitempty"`
	Details   interface{} `json:"details,omitempty"`
	Timestamp int64       `json:"timestamp"`
}

// PaginatedResult 分页响应结构
type PaginatedResult struct {
	Message    string      `json:"message"`
	Data       interface{} `json:"data"`
	Pagination *Pagination `json:"pagination"`
	Timestamp  int64       `json:"timestamp"`
}

// Pagination 分页信息
type Pagination struct {
	Page       int   `json:"page"`
	Limit      int   `json:"limit"`
	Total      int64 `json:"total"`
	TotalPages int   `json:"total_pages"`
	HasNext    bool  `json:"has_next"`
	HasPrev    bool  `json:"has_prev"`
}

// NewSuccessResponse 创建成功响应
func NewSuccessResponse(message string, data interface{}) *SuccessResult {
	return &SuccessResult{
		Message:   message,
		Data:      data,
		Timestamp: time.Now().Unix(),
	}
}

// NewErrorResponse 创建错误响应
func NewErrorResponse(error, message, code string, details interface{}) *ErrorResult {
	return &ErrorResult{
		Error:     error,
		Message:   message,
		Code:      code,
		Details:   details,
		Timestamp: time.Now().Unix(),
	}
}

// NewPaginatedResponse 创建分页响应
func NewPaginatedResponse(message string, data interface{}, pagination *Pagination) *PaginatedResult {
	return &PaginatedResult{
		Message:    message,
		Data:       data,
		Pagination: pagination,
		Timestamp:  time.Now().Unix(),
	}
}

// NewPagination 创建分页信息
func NewPagination(page, limit int, total int64) *Pagination {
	totalPages := int((total + int64(limit) - 1) / int64(limit))

	return &Pagination{
		Page:       page,
		Limit:      limit,
		Total:      total,
		TotalPages: totalPages,
		HasNext:    page < totalPages,
		HasPrev:    page > 1,
	}
}

// 响应辅助函数

// SuccessResponse 发送成功响应
func SuccessResponse(c *gin.Context, message string, data interface{}) {
	c.JSON(http.StatusOK, NewSuccessResponse(message, data))
}

// ErrorResponse 发送错误响应（兼容多个场景，最后一个参数可作为错误码或详情）
func ErrorResponse(c *gin.Context, statusCode int, err string, codeOrDetails interface{}) {
	code := ""
	var details interface{}
	if codeOrDetails != nil {
		if s, ok := codeOrDetails.(string); ok {
			// 简单判断是否像错误码
			isCode := true
			for _, ch := range s {
				if !((ch >= 'A' && ch <= 'Z') || ch == '_' || (ch >= '0' && ch <= '9')) {
					isCode = false
					break
				}
			}
			if isCode {
				code = s
			} else {
				details = s
			}
		} else {
			details = codeOrDetails
		}
	}
	c.JSON(statusCode, NewErrorResponse(err, "", code, details))
}

// ErrorResponseWithMessage 发送带消息的错误响应
func ErrorResponseWithMessage(c *gin.Context, statusCode int, error, message, code string) {
	c.JSON(statusCode, NewErrorResponse(error, message, code, nil))
}

// ErrorResponseWithDetails 发送带详情的错误响应
func ErrorResponseWithDetails(c *gin.Context, statusCode int, error, message, code string, details interface{}) {
	c.JSON(statusCode, NewErrorResponse(error, message, code, details))
}

// PaginatedResponse 发送分页响应
func PaginatedResponse(c *gin.Context, message string, data interface{}, pagination *Pagination) {
	c.JSON(http.StatusOK, NewPaginatedResponse(message, data, pagination))
}
