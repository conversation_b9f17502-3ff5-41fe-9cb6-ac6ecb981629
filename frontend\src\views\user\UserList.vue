<template>
  <div class="user-list-page">
    <PageHeader
      title="用户管理"
      description="管理系统用户和权限"
    >
      <template #extra>
        <a-space>
          <a-button @click="handleRefresh" :loading="loading">
            <template #icon>
              <icon-refresh />
            </template>
            刷新
          </a-button>
          <a-button type="primary" @click="showCreateModal = true">
            <template #icon>
              <icon-plus />
            </template>
            创建用户
          </a-button>
        </a-space>
      </template>
    </PageHeader>

    <!-- 搜索和筛选 -->
    <a-card class="search-card" :bordered="false">
      <a-row :gutter="16">
        <a-col :span="8">
          <a-input
            v-model="searchParams.keyword"
            placeholder="搜索用户名或邮箱"
            allow-clear
            @press-enter="handleSearch"
          >
            <template #prefix>
              <icon-search />
            </template>
          </a-input>
        </a-col>
        <a-col :span="4">
          <a-select
            v-model="searchParams.role"
            placeholder="角色"
            allow-clear
            @change="handleSearch"
          >
            <a-option value="admin">管理员</a-option>
            <a-option value="user">普通用户</a-option>
          </a-select>
        </a-col>
        <a-col :span="4">
          <a-select
            v-model="searchParams.status"
            placeholder="状态"
            allow-clear
            @change="handleSearch"
          >
            <a-option value="active">活跃</a-option>
            <a-option value="inactive">禁用</a-option>
          </a-select>
        </a-col>
        <a-col :span="8">
          <a-space>
            <a-button type="primary" @click="handleSearch">
              <template #icon>
                <icon-search />
              </template>
              搜索
            </a-button>
            <a-button @click="handleReset">重置</a-button>
          </a-space>
        </a-col>
      </a-row>
    </a-card>

    <!-- 用户表格 -->
    <a-card :bordered="false">
      <a-table
        :columns="columns"
        :data="userList"
        :loading="loading"
        :pagination="pagination"
        @page-change="handlePageChange"
        @page-size-change="handlePageSizeChange"
      >
        <template #role="{ record }">
          <a-tag :color="record.role === 'admin' ? 'red' : 'blue'">
            {{ record.role === 'admin' ? '管理员' : '普通用户' }}
          </a-tag>
        </template>

        <template #status="{ record }">
          <a-tag :color="record.status === 'active' ? 'green' : 'gray'">
            {{ record.status === 'active' ? '活跃' : '禁用' }}
          </a-tag>
        </template>

        <template #actions="{ record }">
          <a-space>
            <a-button type="text" size="small" @click="handleEdit(record)">
              编辑
            </a-button>
            <a-button type="text" size="small" @click="handleResetPassword(record)">
              重置密码
            </a-button>
            <a-popconfirm
              content="确定要删除这个用户吗？"
              @ok="handleDelete(record.id)"
            >
              <a-button type="text" size="small" status="danger">
                删除
              </a-button>
            </a-popconfirm>
          </a-space>
        </template>
      </a-table>
    </a-card>

    <!-- 创建用户模态框 -->
    <a-modal
      v-model:visible="showCreateModal"
      title="创建用户"
      @ok="handleCreateUser"
      @cancel="resetCreateForm"
    >
      <a-form :model="createForm" layout="vertical">
        <a-form-item label="用户名" required>
          <a-input v-model="createForm.username" placeholder="请输入用户名" />
        </a-form-item>
        <a-form-item label="邮箱" required>
          <a-input v-model="createForm.email" placeholder="请输入邮箱" />
        </a-form-item>
        <a-form-item label="密码" required>
          <a-input-password v-model="createForm.password" placeholder="请输入密码" />
        </a-form-item>
        <a-form-item label="角色">
          <a-select v-model="createForm.role" placeholder="选择角色">
            <a-option value="admin">管理员</a-option>
            <a-option value="user">普通用户</a-option>
          </a-select>
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { Message } from '@arco-design/web-vue'
import {
  IconRefresh,
  IconPlus,
  IconSearch
} from '@arco-design/web-vue/es/icon'
import PageHeader from '@/components/common/PageHeader.vue'
import {
  getUserList,
  createUser,
  deleteUser,
  resetUserPassword,
  type User,
  type UserListParams,
  type CreateUserRequest
} from '@/api/user'

const loading = ref(false)
const userList = ref<User[]>([])
const showCreateModal = ref(false)

// 搜索参数
const searchParams = reactive<UserListParams>({
  page: 1,
  page_size: 10,
  keyword: '',
  role: '',
  status: ''
})

// 分页信息
const pagination = reactive({
  current: 1,
  pageSize: 10,
  total: 0,
  showTotal: true,
  showPageSize: true
})

// 创建用户表单
const createForm = reactive<CreateUserRequest>({
  username: '',
  email: '',
  password: '',
  role: 'user'
})

// 表格列定义
const columns = [
  {
    title: 'ID',
    dataIndex: 'id',
    width: 80
  },
  {
    title: '用户名',
    dataIndex: 'username',
    width: 120
  },
  {
    title: '邮箱',
    dataIndex: 'email',
    width: 200
  },
  {
    title: '角色',
    dataIndex: 'role',
    slotName: 'role',
    width: 100
  },
  {
    title: '状态',
    dataIndex: 'status',
    slotName: 'status',
    width: 100
  },
  {
    title: '创建时间',
    dataIndex: 'created_at',
    width: 180
  },
  {
    title: '最后登录',
    dataIndex: 'last_login_at',
    width: 180
  },
  {
    title: '操作',
    slotName: 'actions',
    width: 200,
    fixed: 'right'
  }
]

// 加载用户列表
const loadUserList = async () => {
  loading.value = true
  try {
    const response = await getUserList(searchParams)
    userList.value = response.users
    pagination.total = response.total
    pagination.current = response.page
    pagination.pageSize = response.page_size
  } catch (error) {
    console.error('加载用户列表失败:', error)
    Message.error('加载用户列表失败')
  } finally {
    loading.value = false
  }
}

// 搜索
const handleSearch = () => {
  searchParams.page = 1
  pagination.current = 1
  loadUserList()
}

// 重置搜索
const handleReset = () => {
  searchParams.keyword = ''
  searchParams.role = ''
  searchParams.status = ''
  searchParams.page = 1
  pagination.current = 1
  loadUserList()
}

// 刷新
const handleRefresh = () => {
  loadUserList()
}

// 分页变化
const handlePageChange = (page: number) => {
  searchParams.page = page
  pagination.current = page
  loadUserList()
}

const handlePageSizeChange = (pageSize: number) => {
  searchParams.page_size = pageSize
  searchParams.page = 1
  pagination.pageSize = pageSize
  pagination.current = 1
  loadUserList()
}

// 创建用户
const handleCreateUser = async () => {
  try {
    await createUser(createForm)
    Message.success('用户创建成功')
    showCreateModal.value = false
    resetCreateForm()
    loadUserList()
  } catch (error) {
    console.error('创建用户失败:', error)
    Message.error('创建用户失败')
  }
}

// 重置创建表单
const resetCreateForm = () => {
  createForm.username = ''
  createForm.email = ''
  createForm.password = ''
  createForm.role = 'user'
}

// 编辑用户
const handleEdit = (user: User) => {
  Message.info('编辑功能开发中...')
}

// 重置密码
const handleResetPassword = async (user: User) => {
  try {
    const newPassword = 'Password123!'
    await resetUserPassword(user.id, newPassword)
    Message.success(`用户 ${user.username} 的密码已重置为: ${newPassword}`)
  } catch (error) {
    console.error('重置密码失败:', error)
    Message.error('重置密码失败')
  }
}

// 删除用户
const handleDelete = async (id: number) => {
  try {
    await deleteUser(id)
    Message.success('用户删除成功')
    loadUserList()
  } catch (error) {
    console.error('删除用户失败:', error)
    Message.error('删除用户失败')
  }
}

onMounted(() => {
  loadUserList()
})
</script>

<style scoped>
.user-list-page {
  padding: 24px;
}

.search-card {
  margin-bottom: 16px;
}
</style>