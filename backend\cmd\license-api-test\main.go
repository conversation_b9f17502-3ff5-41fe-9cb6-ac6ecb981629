package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"time"
)

const baseURL = "http://localhost:8080/api/v1"

type LoginRequest struct {
	Username   string `json:"username"`
	Password   string `json:"password"`
	RememberMe bool   `json:"remember_me"`
}

type LoginResponse struct {
	Message string `json:"message"`
	Data    struct {
		User struct {
			ID       uint   `json:"id"`
			Username string `json:"username"`
			Email    string `json:"email"`
			Role     string `json:"role"`
			Status   string `json:"status"`
		} `json:"user"`
		Tokens struct {
			AccessToken  string `json:"access_token"`
			RefreshToken string `json:"refresh_token"`
			TokenType    string `json:"token_type"`
			ExpiresIn    int    `json:"expires_in"`
		} `json:"tokens"`
	} `json:"data"`
}

type CreateLicenseRequest struct {
	CustomerName   string    `json:"customer_name"`
	CustomerEmail  string    `json:"customer_email"`
	ProductName    string    `json:"product_name"`
	ProductVersion string    `json:"product_version"`
	LicenseType    string    `json:"license_type"`
	MaxDevices     int       `json:"max_devices"`
	Features       []string  `json:"features"`
	ExpiresAt      time.Time `json:"expires_at"`
	Notes          string    `json:"notes"`
}

type UpdateLicenseRequest struct {
	CustomerName   string    `json:"customer_name,omitempty"`
	CustomerEmail  string    `json:"customer_email,omitempty"`
	ProductName    string    `json:"product_name,omitempty"`
	ProductVersion string    `json:"product_version,omitempty"`
	LicenseType    string    `json:"license_type,omitempty"`
	MaxDevices     int       `json:"max_devices,omitempty"`
	Features       []string  `json:"features,omitempty"`
	ExpiresAt      time.Time `json:"expires_at,omitempty"`
	Status         string    `json:"status,omitempty"`
	Notes          string    `json:"notes,omitempty"`
}

func main() {
	fmt.Println("License Management API Test Tool")
	fmt.Println("===============================")

	// 1. 登录获取访问令牌
	fmt.Println("\n1. Testing Login...")
	accessToken, err := login()
	if err != nil {
		log.Fatalf("❌ Login failed: %v", err)
	}
	fmt.Println("✅ Login successful")

	// 2. 测试创建许可证
	fmt.Println("\n2. Testing Create License...")
	licenseID, err := testCreateLicense(accessToken)
	if err != nil {
		log.Printf("❌ Create license failed: %v", err)
	} else {
		fmt.Printf("✅ License created with ID: %d\n", licenseID)
	}

	// 3. 测试获取许可证列表
	fmt.Println("\n3. Testing Get License List...")
	if err := testGetLicenseList(accessToken); err != nil {
		log.Printf("❌ Get license list failed: %v", err)
	} else {
		fmt.Println("✅ License list retrieved successfully")
	}

	// 4. 测试获取单个许可证
	if licenseID > 0 {
		fmt.Println("\n4. Testing Get Single License...")
		if err := testGetLicense(accessToken, licenseID); err != nil {
			log.Printf("❌ Get license failed: %v", err)
		} else {
			fmt.Println("✅ License retrieved successfully")
		}

		// 5. 测试更新许可证
		fmt.Println("\n5. Testing Update License...")
		if err := testUpdateLicense(accessToken, licenseID); err != nil {
			log.Printf("❌ Update license failed: %v", err)
		} else {
			fmt.Println("✅ License updated successfully")
		}
	}

	// 6. 测试获取许可证统计
	fmt.Println("\n6. Testing Get License Stats...")
	if err := testGetLicenseStats(accessToken); err != nil {
		log.Printf("❌ Get license stats failed: %v", err)
	} else {
		fmt.Println("✅ License stats retrieved successfully")
	}

	// 7. 测试删除许可证（可选）
	if licenseID > 0 {
		fmt.Println("\n7. Testing Delete License...")
		if err := testDeleteLicense(accessToken, licenseID); err != nil {
			log.Printf("❌ Delete license failed: %v", err)
		} else {
			fmt.Println("✅ License deleted successfully")
		}
	}

	fmt.Println("\n🎉 All license API tests completed!")
}

func login() (string, error) {
	req := LoginRequest{
		Username:   "admin",
		Password:   "AdminPass123!",
		RememberMe: true,
	}

	jsonData, _ := json.Marshal(req)
	resp, err := http.Post(baseURL+"/auth/login", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("login failed with status %d: %s", resp.StatusCode, string(body))
	}

	var loginResp LoginResponse
	if err := json.Unmarshal(body, &loginResp); err != nil {
		return "", err
	}

	fmt.Printf("   User: %s (%s)\n", loginResp.Data.User.Username, loginResp.Data.User.Role)
	return loginResp.Data.Tokens.AccessToken, nil
}

func testCreateLicense(accessToken string) (int, error) {
	req := CreateLicenseRequest{
		CustomerName:   "Test Customer",
		CustomerEmail:  "<EMAIL>",
		ProductName:    "Test Software",
		ProductVersion: "1.0.0",
		LicenseType:    "professional",
		MaxDevices:     5,
		Features:       []string{"feature1", "feature2", "advanced_reporting"},
		ExpiresAt:      time.Now().AddDate(1, 0, 0), // 1年后过期
		Notes:          "Test license for API testing",
	}

	jsonData, _ := json.Marshal(req)
	client := &http.Client{}
	httpReq, _ := http.NewRequest("POST", baseURL+"/licenses", bytes.NewBuffer(jsonData))
	httpReq.Header.Set("Authorization", "Bearer "+accessToken)
	httpReq.Header.Set("Content-Type", "application/json")

	resp, err := client.Do(httpReq)
	if err != nil {
		return 0, err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return 0, err
	}

	if resp.StatusCode != http.StatusOK {
		return 0, fmt.Errorf("create license failed with status %d: %s", resp.StatusCode, string(body))
	}

	var response struct {
		Message string `json:"message"`
		Data    struct {
			ID         int    `json:"id"`
			LicenseKey string `json:"license_key"`
		} `json:"data"`
	}

	if err := json.Unmarshal(body, &response); err != nil {
		return 0, err
	}

	fmt.Printf("   License Key: %s\n", response.Data.LicenseKey)
	return response.Data.ID, nil
}

func testGetLicenseList(accessToken string) error {
	client := &http.Client{}
	req, _ := http.NewRequest("GET", baseURL+"/licenses?page=1&page_size=10", nil)
	req.Header.Set("Authorization", "Bearer "+accessToken)

	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("get license list failed with status %d: %s", resp.StatusCode, string(body))
	}

	var response struct {
		Message string `json:"message"`
		Data    struct {
			Total      int64 `json:"total"`
			Page       int   `json:"page"`
			PageSize   int   `json:"page_size"`
			TotalPages int   `json:"total_pages"`
		} `json:"data"`
	}

	if err := json.Unmarshal(body, &response); err != nil {
		return err
	}

	fmt.Printf("   Total Licenses: %d\n", response.Data.Total)
	fmt.Printf("   Page: %d/%d\n", response.Data.Page, response.Data.TotalPages)
	return nil
}

func testGetLicense(accessToken string, licenseID int) error {
	client := &http.Client{}
	req, _ := http.NewRequest("GET", fmt.Sprintf("%s/licenses/%d", baseURL, licenseID), nil)
	req.Header.Set("Authorization", "Bearer "+accessToken)

	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("get license failed with status %d: %s", resp.StatusCode, string(body))
	}

	var response struct {
		Message string `json:"message"`
		Data    struct {
			ID           int      `json:"id"`
			LicenseKey   string   `json:"license_key"`
			CustomerName string   `json:"customer_name"`
			Status       string   `json:"status"`
			Features     []string `json:"features"`
		} `json:"data"`
	}

	if err := json.Unmarshal(body, &response); err != nil {
		return err
	}

	fmt.Printf("   License: %s\n", response.Data.LicenseKey)
	fmt.Printf("   Customer: %s\n", response.Data.CustomerName)
	fmt.Printf("   Status: %s\n", response.Data.Status)
	fmt.Printf("   Features: %v\n", response.Data.Features)
	return nil
}

func testUpdateLicense(accessToken string, licenseID int) error {
	req := UpdateLicenseRequest{
		CustomerName: "Updated Customer Name",
		Notes:        "Updated notes for testing",
		MaxDevices:   10, // 增加设备限制
	}

	jsonData, _ := json.Marshal(req)
	client := &http.Client{}
	httpReq, _ := http.NewRequest("PUT", fmt.Sprintf("%s/licenses/%d", baseURL, licenseID), bytes.NewBuffer(jsonData))
	httpReq.Header.Set("Authorization", "Bearer "+accessToken)
	httpReq.Header.Set("Content-Type", "application/json")

	resp, err := client.Do(httpReq)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("update license failed with status %d: %s", resp.StatusCode, string(body))
	}

	fmt.Printf("   License updated successfully\n")
	return nil
}

func testGetLicenseStats(accessToken string) error {
	client := &http.Client{}
	req, _ := http.NewRequest("GET", baseURL+"/licenses/stats", nil)
	req.Header.Set("Authorization", "Bearer "+accessToken)

	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("get license stats failed with status %d: %s", resp.StatusCode, string(body))
	}

	var response struct {
		Message string `json:"message"`
		Data    struct {
			Total     int64 `json:"total"`
			Active    int64 `json:"active"`
			Expired   int64 `json:"expired"`
			Disabled  int64 `json:"disabled"`
			Suspended int64 `json:"suspended"`
			Expiring  int64 `json:"expiring"`
		} `json:"data"`
	}

	if err := json.Unmarshal(body, &response); err != nil {
		return err
	}

	fmt.Printf("   Total: %d\n", response.Data.Total)
	fmt.Printf("   Active: %d\n", response.Data.Active)
	fmt.Printf("   Expired: %d\n", response.Data.Expired)
	fmt.Printf("   Expiring Soon: %d\n", response.Data.Expiring)
	return nil
}

func testDeleteLicense(accessToken string, licenseID int) error {
	client := &http.Client{}
	req, _ := http.NewRequest("DELETE", fmt.Sprintf("%s/licenses/%d", baseURL, licenseID), nil)
	req.Header.Set("Authorization", "Bearer "+accessToken)

	resp, err := client.Do(req)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("delete license failed with status %d: %s", resp.StatusCode, string(body))
	}

	fmt.Printf("   License deleted successfully\n")
	return nil
}