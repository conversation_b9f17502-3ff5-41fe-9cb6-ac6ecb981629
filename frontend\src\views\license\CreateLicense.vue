<template>
  <div class="create-license-page">
    <PageHeader 
      title="创建许可证" 
      description="为客户创建新的软件许可证"
    >
      <template #extra>
        <a-space>
          <a-button @click="$router.back()">
            <template #icon>
              <icon-left />
            </template>
            返回
          </a-button>
          <a-button type="primary" @click="handleSubmit" :loading="loading">
            <template #icon>
              <icon-check />
            </template>
            创建许可证
          </a-button>
        </a-space>
      </template>
    </PageHeader>

    <div class="form-container">
      <a-row :gutter="24">
        <!-- 左侧：基本信息表单 -->
        <a-col :span="16">
          <a-card title="基本信息" class="form-card">
            <a-form
              ref="formRef"
              :model="form"
              :rules="rules"
              layout="vertical"
              @submit="handleSubmit"
            >
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item field="product_name" label="产品名称">
                    <a-select
                      v-model="form.product_name"
                      placeholder="选择或输入产品名称"
                      allow-create
                      allow-search
                    >
                      <a-option v-for="product in productOptions" :key="product" :value="product">
                        {{ product }}
                      </a-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item field="max_devices" label="最大设备数">
                    <a-input-number
                      v-model="form.max_devices"
                      :min="1"
                      :max="100"
                      placeholder="设备数量限制"
                    />
                  </a-form-item>
                </a-col>
              </a-row>

              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item field="customer_name" label="客户名称">
                    <a-input
                      v-model="form.customer_name"
                      placeholder="请输入客户名称"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item field="customer_email" label="客户邮箱">
                    <a-input
                      v-model="form.customer_email"
                      placeholder="请输入客户邮箱"
                    />
                  </a-form-item>
                </a-col>
              </a-row>

              <a-form-item field="expires_at" label="过期时间">
                <a-radio-group v-model="expireType" @change="handleExpireTypeChange">
                  <a-radio value="never">永不过期</a-radio>
                  <a-radio value="custom">自定义时间</a-radio>
                  <a-radio value="duration">指定时长</a-radio>
                </a-radio-group>
                
                <div v-if="expireType === 'custom'" class="expire-input">
                  <a-date-picker
                    v-model="form.expires_at"
                    show-time
                    format="YYYY-MM-DD HH:mm:ss"
                    placeholder="选择过期时间"
                    :disabled-date="(date) => date.isBefore(dayjs(), 'day')"
                  />
                </div>
                
                <div v-if="expireType === 'duration'" class="expire-input">
                  <a-space>
                    <a-input-number
                      v-model="expireDuration.value"
                      :min="1"
                      placeholder="时长"
                      style="width: 120px"
                    />
                    <a-select
                      v-model="expireDuration.unit"
                      style="width: 100px"
                    >
                      <a-option value="days">天</a-option>
                      <a-option value="months">月</a-option>
                      <a-option value="years">年</a-option>
                    </a-select>
                  </a-space>
                </div>
              </a-form-item>

              <a-form-item field="features" label="功能特性">
                <div class="features-section">
                  <div class="feature-checkboxes">
                    <a-checkbox-group v-model="form.features">
                      <a-checkbox 
                        v-for="feature in availableFeatures" 
                        :key="feature.value" 
                        :value="feature.value"
                      >
                        {{ feature.label }}
                      </a-checkbox>
                    </a-checkbox-group>
                  </div>
                  
                  <div class="custom-feature">
                    <a-input
                      v-model="customFeature"
                      placeholder="添加自定义功能"
                      @keyup.enter="handleAddCustomFeature"
                    >
                      <template #suffix>
                        <a-button 
                          type="text" 
                          size="small"
                          @click="handleAddCustomFeature"
                          :disabled="!customFeature.trim()"
                        >
                          添加
                        </a-button>
                      </template>
                    </a-input>
                  </div>
                  
                  <div v-if="customFeatures.length > 0" class="custom-features-list">
                    <a-tag
                      v-for="feature in customFeatures"
                      :key="feature"
                      closable
                      @close="handleRemoveCustomFeature(feature)"
                    >
                      {{ feature }}
                    </a-tag>
                  </div>
                </div>
              </a-form-item>
            </a-form>
          </a-card>
        </a-col>

        <!-- 右侧：预览和帮助 -->
        <a-col :span="8">
          <!-- 许可证预览 -->
          <a-card title="许可证预览" class="preview-card">
            <div class="license-preview">
              <div class="preview-item">
                <span class="preview-label">许可证密钥：</span>
                <span class="preview-value">{{ generatedKey || '将自动生成' }}</span>
              </div>
              
              <div class="preview-item">
                <span class="preview-label">产品名称：</span>
                <span class="preview-value">{{ form.product_name || '-' }}</span>
              </div>
              
              <div class="preview-item">
                <span class="preview-label">客户名称：</span>
                <span class="preview-value">{{ form.customer_name || '-' }}</span>
              </div>
              
              <div class="preview-item">
                <span class="preview-label">设备限制：</span>
                <span class="preview-value">{{ form.max_devices || 1 }} 台</span>
              </div>
              
              <div class="preview-item">
                <span class="preview-label">过期时间：</span>
                <span class="preview-value">{{ getExpirePreview() }}</span>
              </div>
              
              <div class="preview-item">
                <span class="preview-label">功能特性：</span>
                <div class="preview-features">
                  <a-tag 
                    v-for="feature in getAllFeatures()" 
                    :key="feature" 
                    size="small"
                  >
                    {{ getFeatureLabel(feature) }}
                  </a-tag>
                  <span v-if="getAllFeatures().length === 0" class="preview-value">无</span>
                </div>
              </div>
            </div>
            
            <a-button 
              type="outline" 
              long 
              @click="handleGenerateKey"
              :loading="generatingKey"
            >
              <template #icon>
                <icon-refresh />
              </template>
              生成新密钥
            </a-button>
          </a-card>

          <!-- 帮助信息 -->
          <a-card title="创建指南" class="help-card">
            <div class="help-content">
              <div class="help-item">
                <h4>产品名称</h4>
                <p>选择现有产品或创建新产品名称</p>
              </div>
              
              <div class="help-item">
                <h4>设备限制</h4>
                <p>限制可以使用此许可证的设备数量</p>
              </div>
              
              <div class="help-item">
                <h4>过期时间</h4>
                <p>设置许可证的有效期，可选择永不过期</p>
              </div>
              
              <div class="help-item">
                <h4>功能特性</h4>
                <p>选择此许可证包含的功能模块</p>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { Message } from '@arco-design/web-vue'
import type { FormInstance } from '@arco-design/web-vue'
import PageHeader from '@/components/common/PageHeader.vue'
import type { CreateLicenseRequest } from '@/types/license'
import { licenseApi } from '@/api/license'
import dayjs from 'dayjs'

const router = useRouter()
const formRef = ref<FormInstance>()
const loading = ref(false)
const generatingKey = ref(false)
const generatedKey = ref('')

// 过期类型
const expireType = ref<'never' | 'custom' | 'duration'>('never')

// 过期时长
const expireDuration = reactive({
  value: 1,
  unit: 'years' as 'days' | 'months' | 'years'
})

// 自定义功能
const customFeature = ref('')
const customFeatures = ref<string[]>([])

// 表单数据
const form = reactive<CreateLicenseRequest>({
  product_name: '',
  customer_name: '',
  customer_email: '',
  max_devices: 1,
  features: [],
  expires_at: undefined
})

// 表单验证规则
const rules = {
  product_name: [
    { required: true, message: '请输入产品名称' }
  ],
  customer_name: [
    { required: true, message: '请输入客户名称' }
  ],
  customer_email: [
    { required: true, message: '请输入客户邮箱' },
    { type: 'email', message: '请输入有效的邮箱地址' }
  ],
  max_devices: [
    { required: true, message: '请设置设备数量限制' },
    { type: 'number', min: 1, message: '设备数量至少为1' }
  ]
}

// 产品选项
const productOptions = [
  'Demo Software',
  'Pro Software',
  'Enterprise Suite',
  'Mobile App',
  'Web Service'
]

// 可用功能特性
const availableFeatures = [
  { label: '基础功能', value: 'basic' },
  { label: '高级功能', value: 'advanced' },
  { label: '专业功能', value: 'professional' },
  { label: '企业功能', value: 'enterprise' },
  { label: 'API访问', value: 'api_access' },
  { label: '数据导出', value: 'data_export' },
  { label: '多用户支持', value: 'multi_user' },
  { label: '云同步', value: 'cloud_sync' }
]

// 获取所有功能（包括自定义）
const getAllFeatures = () => {
  return [...form.features, ...customFeatures.value]
}

// 获取功能标签
const getFeatureLabel = (value: string) => {
  const feature = availableFeatures.find(f => f.value === value)
  return feature ? feature.label : value
}

// 获取过期时间预览
const getExpirePreview = () => {
  if (expireType.value === 'never') {
    return '永不过期'
  } else if (expireType.value === 'custom' && form.expires_at) {
    return dayjs(form.expires_at).format('YYYY-MM-DD HH:mm:ss')
  } else if (expireType.value === 'duration') {
    const unitMap = {
      days: '天',
      months: '个月',
      years: '年'
    }
    return `${expireDuration.value}${unitMap[expireDuration.unit]}后过期`
  }
  return '未设置'
}

// 过期类型变化处理
const handleExpireTypeChange = () => {
  if (expireType.value === 'never') {
    form.expires_at = undefined
  } else if (expireType.value === 'duration') {
    updateExpireTimeFromDuration()
  }
}

// 根据时长更新过期时间
const updateExpireTimeFromDuration = () => {
  const now = dayjs()
  let expireTime = now
  
  switch (expireDuration.unit) {
    case 'days':
      expireTime = now.add(expireDuration.value, 'day')
      break
    case 'months':
      expireTime = now.add(expireDuration.value, 'month')
      break
    case 'years':
      expireTime = now.add(expireDuration.value, 'year')
      break
  }
  
  form.expires_at = expireTime.toISOString()
}

// 监听时长变化
watch([() => expireDuration.value, () => expireDuration.unit], () => {
  if (expireType.value === 'duration') {
    updateExpireTimeFromDuration()
  }
})

// 添加自定义功能
const handleAddCustomFeature = () => {
  const feature = customFeature.value.trim()
  if (feature && !customFeatures.value.includes(feature)) {
    customFeatures.value.push(feature)
    customFeature.value = ''
  }
}

// 移除自定义功能
const handleRemoveCustomFeature = (feature: string) => {
  const index = customFeatures.value.indexOf(feature)
  if (index > -1) {
    customFeatures.value.splice(index, 1)
  }
}

// 生成许可证密钥
const handleGenerateKey = async () => {
  try {
    generatingKey.value = true
    
    // 生成格式：PRODUCT-YYYYMMDD-RANDOM
    const product = form.product_name.replace(/\s+/g, '').toUpperCase().substring(0, 8) || 'SOFTWARE'
    const date = dayjs().format('YYYYMMDD')
    const random = Math.random().toString(36).substring(2, 8).toUpperCase()
    
    generatedKey.value = `${product}-${date}-${random}`
    
    await new Promise(resolve => setTimeout(resolve, 500))
  } catch (error) {
    console.error('Generate key error:', error)
    Message.error('生成密钥失败')
  } finally {
    generatingKey.value = false
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    const valid = await formRef.value?.validate()
    if (!valid) return

    loading.value = true

    // 准备提交数据
    const submitData: CreateLicenseRequest = {
      ...form,
      features: getAllFeatures()
    }

    // TODO: 调用实际API
    // const response = await licenseApi.create(submitData)
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    Message.success('许可证创建成功')
    router.push('/licenses')
    
  } catch (error: any) {
    console.error('Create license error:', error)
    Message.error(error.message || '创建许可证失败')
  } finally {
    loading.value = false
  }
}

// 初始化生成密钥
const initializeKey = () => {
  if (form.product_name) {
    handleGenerateKey()
  }
}

// 监听产品名称变化，自动生成密钥
watch(() => form.product_name, () => {
  if (form.product_name) {
    handleGenerateKey()
  }
})
</script>

<style scoped>
.create-license-page {
  padding: 24px;
}

.form-container {
  margin-top: 24px;
}

.form-card,
.preview-card,
.help-card {
  margin-bottom: 24px;
}

.expire-input {
  margin-top: 12px;
}

.features-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.feature-checkboxes {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.custom-features-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.license-preview {
  margin-bottom: 16px;
}

.preview-item {
  display: flex;
  margin-bottom: 12px;
  align-items: flex-start;
}

.preview-label {
  font-weight: 500;
  color: #595959;
  min-width: 80px;
  flex-shrink: 0;
}

.preview-value {
  color: #262626;
  word-break: break-all;
}

.preview-features {
  display: flex;
  flex-wrap: wrap;
  gap: 4px;
  flex: 1;
}

.help-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.help-item h4 {
  margin: 0 0 4px 0;
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.help-item p {
  margin: 0;
  font-size: 12px;
  color: #8c8c8c;
  line-height: 1.4;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .form-container .arco-row {
    flex-direction: column;
  }
  
  .form-container .arco-col {
    width: 100%;
  }
  
  .preview-card,
  .help-card {
    margin-top: 24px;
  }
}

@media (max-width: 768px) {
  .create-license-page {
    padding: 16px;
  }
  
  .feature-checkboxes {
    grid-template-columns: 1fr;
  }
}
</style>