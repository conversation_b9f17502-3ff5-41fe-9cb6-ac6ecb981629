# 设计文档

## 概述

软件网络授权系统采用前后端分离架构，前端使用Vue3+Vite+Arco Design构建现代化管理界面，后端使用Gin框架提供RESTful API服务，数据存储采用SQLite+Redis组合方案，确保系统在Windows服务器环境下的高效运行。

## 架构设计

### 整体架构

```mermaid
graph TB
    subgraph "客户端层"
        A[软件客户端] --> B[许可证验证API]
        C[Web管理界面<br/>Vue3+Vite+Arco Design] --> D[管理API]
    end
    
    subgraph "服务层"
        B --> E[Gin Web服务器]
        D --> E
        E --> F[认证中间件]
        E --> G[业务逻辑层]
    end
    
    subgraph "数据层"
        G --> H[Redis缓存]
        G --> I[SQLite数据库]
    end
    
    subgraph "外部服务"
        E --> J[日志系统]
        E --> K[监控系统]
    end
```

### 技术栈架构

**前端架构：**
- **Vue3**：响应式框架，提供组合式API
- **Vite**：构建工具，提供快速开发体验
- **Arco Design**：UI组件库，提供企业级组件
- **Vue Router**：路由管理
- **Pinia**：状态管理
- **Axios**：HTTP客户端

**后端架构：**
- **Gin**：Web框架，提供高性能HTTP服务
- **GORM**：ORM框架，简化数据库操作
- **JWT**：身份认证令牌
- **Redis**：缓存和会话存储
- **SQLite**：主数据库

## 组件和接口

### 前端组件结构

```
src/
├── components/          # 通用组件
│   ├── Layout/         # 布局组件
│   ├── Auth/           # 认证组件
│   └── Common/         # 公共组件
├── views/              # 页面组件
│   ├── Dashboard/      # 仪表板
│   ├── License/        # 许可证管理
│   ├── User/           # 用户管理
│   └── Reports/        # 报告页面
├── stores/             # Pinia状态管理
├── api/                # API接口封装
├── utils/              # 工具函数
└── router/             # 路由配置
```

### 后端模块结构

```
cmd/
├── server/             # 服务器启动
internal/
├── api/                # API处理器
│   ├── auth/           # 认证相关
│   ├── license/        # 许可证管理
│   ├── user/           # 用户管理
│   └── report/         # 报告统计
├── middleware/         # 中间件
├── model/              # 数据模型
├── service/            # 业务逻辑
├── repository/         # 数据访问层
└── config/             # 配置管理
```

### API接口设计

**认证接口：**
```
POST /api/v1/auth/login          # 用户登录
POST /api/v1/auth/logout         # 用户登出
POST /api/v1/auth/refresh        # 刷新令牌
GET  /api/v1/auth/profile        # 获取用户信息
```

**许可证管理接口：**
```
GET    /api/v1/licenses          # 获取许可证列表
POST   /api/v1/licenses          # 创建许可证
GET    /api/v1/licenses/:id      # 获取许可证详情
PUT    /api/v1/licenses/:id      # 更新许可证
DELETE /api/v1/licenses/:id      # 删除许可证
POST   /api/v1/licenses/:id/disable  # 禁用许可证
```

**许可证验证接口：**
```
POST /api/v1/verify              # 验证许可证
POST /api/v1/verify/offline      # 离线验证
GET  /api/v1/verify/status       # 获取验证状态
```

**用户管理接口：**
```
GET    /api/v1/users             # 获取用户列表
POST   /api/v1/users             # 创建用户
GET    /api/v1/users/:id         # 获取用户详情
PUT    /api/v1/users/:id         # 更新用户
DELETE /api/v1/users/:id         # 删除用户
```

**报告统计接口：**
```
GET /api/v1/reports/dashboard    # 仪表板数据
GET /api/v1/reports/usage        # 使用统计
GET /api/v1/reports/licenses     # 许可证统计
GET /api/v1/reports/export       # 导出报告
```

## 数据模型

### 数据库表设计

**用户表 (users)：**
```sql
CREATE TABLE users (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    email VARCHAR(100),
    role VARCHAR(20) DEFAULT 'user',
    status VARCHAR(20) DEFAULT 'active',
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);
```

**许可证表 (licenses)：**
```sql
CREATE TABLE licenses (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    license_key VARCHAR(255) UNIQUE NOT NULL,
    product_name VARCHAR(100) NOT NULL,
    customer_name VARCHAR(100),
    customer_email VARCHAR(100),
    max_devices INTEGER DEFAULT 1,
    features TEXT, -- JSON格式存储功能列表
    expires_at DATETIME,
    status VARCHAR(20) DEFAULT 'active',
    created_by INTEGER,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (created_by) REFERENCES users(id)
);
```

**验证日志表 (verification_logs)：**
```sql
CREATE TABLE verification_logs (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    license_key VARCHAR(255) NOT NULL,
    device_id VARCHAR(255),
    ip_address VARCHAR(45),
    user_agent TEXT,
    verification_result VARCHAR(20),
    error_message TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (license_key) REFERENCES licenses(license_key)
);
```

**设备绑定表 (device_bindings)：**
```sql
CREATE TABLE device_bindings (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    license_key VARCHAR(255) NOT NULL,
    device_id VARCHAR(255) NOT NULL,
    device_name VARCHAR(100),
    first_seen_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    last_seen_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    status VARCHAR(20) DEFAULT 'active',
    FOREIGN KEY (license_key) REFERENCES licenses(license_key),
    UNIQUE(license_key, device_id)
);
```

### Redis缓存策略

**缓存键设计：**
```
auth:token:{token_id}           # JWT令牌缓存，TTL: 24小时
license:verify:{license_key}    # 许可证验证结果，TTL: 1小时
license:info:{license_key}      # 许可证信息，TTL: 30分钟
user:session:{user_id}          # 用户会话信息，TTL: 24小时
stats:daily:{date}              # 每日统计数据，TTL: 7天
rate_limit:{ip}:{endpoint}      # API限流，TTL: 1分钟
```

## 错误处理

### 错误码设计

```go
const (
    // 通用错误码
    ErrSuccess           = 0     // 成功
    ErrInternalServer    = 1000  // 服务器内部错误
    ErrInvalidParams     = 1001  // 参数错误
    ErrUnauthorized      = 1002  // 未授权
    ErrForbidden         = 1003  // 禁止访问
    ErrNotFound          = 1004  // 资源不存在
    ErrRateLimit         = 1005  // 请求频率限制
    
    // 认证相关错误码
    ErrInvalidCredentials = 2001  // 凭据无效
    ErrTokenExpired      = 2002  // 令牌过期
    ErrTokenInvalid      = 2003  // 令牌无效
    
    // 许可证相关错误码
    ErrLicenseNotFound   = 3001  // 许可证不存在
    ErrLicenseExpired    = 3002  // 许可证过期
    ErrLicenseDisabled   = 3003  // 许可证已禁用
    ErrDeviceLimitExceeded = 3004 // 设备数量超限
    ErrFeatureNotAllowed = 3005  // 功能未授权
)
```

### 错误响应格式

```json
{
    "code": 3002,
    "message": "许可证已过期",
    "data": null,
    "timestamp": "2024-01-01T12:00:00Z",
    "request_id": "req_123456789"
}
```

## 测试策略

### 单元测试

**后端测试：**
- 使用 `testify` 框架进行单元测试
- 模拟数据库和Redis连接
- 测试覆盖率目标：80%以上

**前端测试：**
- 使用 `Vitest` 进行单元测试
- 使用 `@vue/test-utils` 测试Vue组件
- 测试覆盖率目标：70%以上

### 集成测试

**API测试：**
- 使用真实的SQLite数据库（测试环境）
- 测试完整的API流程
- 包括认证、许可证管理、验证等核心功能

**端到端测试：**
- 使用 `Playwright` 进行E2E测试
- 测试关键用户流程
- 自动化测试部署

### 性能测试

**负载测试：**
- 使用 `Apache Bench` 或 `wrk` 进行API性能测试
- 目标：许可证验证API响应时间 < 100ms
- 目标：管理API响应时间 < 500ms

**压力测试：**
- 模拟高并发许可证验证请求
- 测试Redis缓存性能
- 测试数据库连接池

## 安全设计

### 认证和授权

**JWT令牌设计：**
```json
{
    "sub": "user_id",
    "username": "admin",
    "role": "admin",
    "exp": 1640995200,
    "iat": 1640908800,
    "jti": "token_unique_id"
}
```

**权限控制：**
- 基于角色的访问控制（RBAC）
- 管理员：完全访问权限
- 普通用户：只读权限
- API用户：仅验证接口权限

### 数据安全

**敏感数据处理：**
- 密码使用bcrypt加密存储
- 许可证密钥使用AES加密
- API通信使用HTTPS
- 数据库连接加密

**API安全：**
- 请求频率限制
- IP白名单支持
- API密钥认证
- 请求签名验证

### 审计日志

**日志记录：**
- 用户登录/登出记录
- 许可证创建/修改/删除记录
- 验证请求记录
- 系统错误记录

**日志格式：**
```json
{
    "timestamp": "2024-01-01T12:00:00Z",
    "level": "INFO",
    "action": "license_verify",
    "user_id": "123",
    "ip_address": "*************",
    "details": {
        "license_key": "xxx-xxx-xxx",
        "result": "success"
    }
}
```

## 部署架构

### Windows服务器部署

**目录结构：**
```
C:\SoftwareAuth\
├── bin\
│   ├── server.exe          # 后端服务
│   └── config.yaml         # 配置文件
├── web\                    # 前端静态文件
├── data\
│   ├── database.db         # SQLite数据库
│   └── logs\               # 日志文件
└── scripts\
    ├── install.bat         # 安装脚本
    └── start.bat           # 启动脚本
```

**服务配置：**
- 使用Windows Service包装Go应用
- 自动启动配置
- 日志轮转配置
- 进程监控和自动重启

**反向代理：**
- 使用Nginx或IIS作为反向代理
- 静态文件服务
- HTTPS证书配置
- 负载均衡支持

### 配置管理

**配置文件示例：**
```yaml
server:
  port: 8080
  mode: production
  
database:
  driver: sqlite
  dsn: "./data/database.db"
  max_open_conns: 25
  max_idle_conns: 5
  
redis:
  addr: "localhost:6379"
  password: ""
  db: 0
  pool_size: 10
  
jwt:
  secret: "your-secret-key"
  expire_hours: 24
  
logging:
  level: info
  file: "./data/logs/app.log"
  max_size: 100
  max_backups: 5
  max_age: 30
```

### 监控和维护

**健康检查：**
- HTTP健康检查端点
- 数据库连接检查
- Redis连接检查
- 磁盘空间监控

**性能监控：**
- API响应时间监控
- 数据库查询性能
- 内存使用情况
- CPU使用率

**备份策略：**
- SQLite数据库定期备份
- 配置文件备份
- 日志文件归档
- 自动备份脚本