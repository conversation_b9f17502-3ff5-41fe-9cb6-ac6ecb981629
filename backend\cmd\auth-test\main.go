package main

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"net/http/httptest"
	"software-auth/internal/config"
	"software-auth/internal/auth"
	"software-auth/internal/middleware"
	"software-auth/internal/model"
	"software-auth/internal/cache"

	"github.com/gin-gonic/gin"
)

func main() {
	fmt.Println("Authentication Middleware Test Tool")
	fmt.Println("===================================")

	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化缓存
	if err := cache.Initialize(&cfg.Redis); err != nil {
		fmt.Printf("⚠️  Warning: Redis not available: %v\n", err)
	}

	// 设置Gin为测试模式
	gin.SetMode(gin.TestMode)

	// 创建JWT服务
	jwtService := auth.NewJWTService(&cfg.JWT)

	// 创建测试用户和令牌
	testUser := &model.User{
		ID:       1,
		Username: "admin",
		Role:     "admin",
		Status:   "active",
	}

	tokens, err := jwtService.GenerateToken(testUser)
	if err != nil {
		log.Fatalf("Failed to generate test token: %v", err)
	}

	fmt.Printf("Generated test token for user: %s\n", testUser.Username)

	// 测试1: 无认证头的请求
	fmt.Println("\n1. Testing request without Authorization header...")
	testNoAuthHeader(&cfg.JWT)

	// 测试2: 无效格式的认证头
	fmt.Println("\n2. Testing request with invalid Authorization header format...")
	testInvalidAuthFormat(&cfg.JWT)

	// 测试3: 有效令牌的请求
	fmt.Println("\n3. Testing request with valid token...")
	testValidToken(&cfg.JWT, tokens.AccessToken)

	// 测试4: 无效令牌的请求
	fmt.Println("\n4. Testing request with invalid token...")
	testInvalidToken(&cfg.JWT)

	// 测试5: 角色权限检查
	fmt.Println("\n5. Testing role-based access control...")
	testRoleBasedAccess(&cfg.JWT, tokens.AccessToken)

	// 测试6: 可选认证中间件
	fmt.Println("\n6. Testing optional authentication middleware...")
	testOptionalAuth(&cfg.JWT, tokens.AccessToken)

	// 测试7: 令牌撤销
	if cache.IsHealthy() {
		fmt.Println("\n7. Testing token revocation...")
		testTokenRevocation(&cfg.JWT, jwtService, tokens.AccessToken)
	} else {
		fmt.Println("\n7. Skipping token revocation test (Redis not available)")
	}

	fmt.Println("\n🎉 All authentication middleware tests completed!")
}

func testNoAuthHeader(jwtConfig *config.JWTConfig) {
	router := gin.New()
	router.Use(middleware.AuthMiddleware(jwtConfig))
	router.GET("/protected", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "success"})
	})

	req, _ := http.NewRequest("GET", "/protected", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	if w.Code == http.StatusUnauthorized {
		fmt.Printf("✅ Correctly rejected request without auth header (401)\n")
	} else {
		fmt.Printf("❌ Should reject request without auth header, got %d\n", w.Code)
	}
}

func testInvalidAuthFormat(jwtConfig *config.JWTConfig) {
	router := gin.New()
	router.Use(middleware.AuthMiddleware(jwtConfig))
	router.GET("/protected", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "success"})
	})

	req, _ := http.NewRequest("GET", "/protected", nil)
	req.Header.Set("Authorization", "InvalidFormat token")
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	if w.Code == http.StatusUnauthorized {
		fmt.Printf("✅ Correctly rejected invalid auth format (401)\n")
	} else {
		fmt.Printf("❌ Should reject invalid auth format, got %d\n", w.Code)
	}
}

func testValidToken(jwtConfig *config.JWTConfig, token string) {
	router := gin.New()
	router.Use(middleware.AuthMiddleware(jwtConfig))
	router.GET("/protected", func(c *gin.Context) {
		currentUser := middleware.GetCurrentUser(c)
		c.JSON(http.StatusOK, gin.H{
			"message": "success",
			"user":    currentUser,
		})
	})

	req, _ := http.NewRequest("GET", "/protected", nil)
	req.Header.Set("Authorization", "Bearer "+token)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	if w.Code == http.StatusOK {
		fmt.Printf("✅ Valid token accepted (200)\n")
		
		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)
		if user, ok := response["user"].(map[string]interface{}); ok {
			fmt.Printf("   User ID: %.0f\n", user["id"])
			fmt.Printf("   Username: %s\n", user["username"])
			fmt.Printf("   Role: %s\n", user["role"])
		}
	} else {
		fmt.Printf("❌ Valid token should be accepted, got %d\n", w.Code)
		fmt.Printf("   Response: %s\n", w.Body.String())
	}
}

func testInvalidToken(jwtConfig *config.JWTConfig) {
	router := gin.New()
	router.Use(middleware.AuthMiddleware(jwtConfig))
	router.GET("/protected", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "success"})
	})

	req, _ := http.NewRequest("GET", "/protected", nil)
	req.Header.Set("Authorization", "Bearer invalid.token.here")
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	if w.Code == http.StatusUnauthorized {
		fmt.Printf("✅ Invalid token correctly rejected (401)\n")
	} else {
		fmt.Printf("❌ Invalid token should be rejected, got %d\n", w.Code)
	}
}

func testRoleBasedAccess(jwtConfig *config.JWTConfig, token string) {
	router := gin.New()
	router.Use(middleware.AuthMiddleware(jwtConfig))
	
	// 管理员专用路由
	router.GET("/admin", middleware.RequireAdmin(), func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "admin access granted"})
	})
	
	// 用户角色路由
	router.GET("/user", middleware.RequireRole("user", "admin"), func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "user access granted"})
	})

	// 测试管理员访问
	req, _ := http.NewRequest("GET", "/admin", nil)
	req.Header.Set("Authorization", "Bearer "+token)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	if w.Code == http.StatusOK {
		fmt.Printf("✅ Admin role correctly granted access to admin endpoint\n")
	} else {
		fmt.Printf("❌ Admin should have access to admin endpoint, got %d\n", w.Code)
	}

	// 测试用户访问
	req, _ = http.NewRequest("GET", "/user", nil)
	req.Header.Set("Authorization", "Bearer "+token)
	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	if w.Code == http.StatusOK {
		fmt.Printf("✅ Admin role correctly granted access to user endpoint\n")
	} else {
		fmt.Printf("❌ Admin should have access to user endpoint, got %d\n", w.Code)
	}
}

func testOptionalAuth(jwtConfig *config.JWTConfig, token string) {
	router := gin.New()
	router.Use(middleware.OptionalAuth(jwtConfig))
	router.GET("/optional", func(c *gin.Context) {
		currentUser := middleware.GetCurrentUser(c)
		if currentUser != nil {
			c.JSON(http.StatusOK, gin.H{
				"message": "authenticated",
				"user":    currentUser,
			})
		} else {
			c.JSON(http.StatusOK, gin.H{
				"message": "anonymous",
			})
		}
	})

	// 测试无认证的请求
	req, _ := http.NewRequest("GET", "/optional", nil)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	if w.Code == http.StatusOK {
		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)
		if response["message"] == "anonymous" {
			fmt.Printf("✅ Optional auth correctly allows anonymous access\n")
		} else {
			fmt.Printf("❌ Should allow anonymous access\n")
		}
	}

	// 测试有认证的请求
	req, _ = http.NewRequest("GET", "/optional", nil)
	req.Header.Set("Authorization", "Bearer "+token)
	w = httptest.NewRecorder()
	router.ServeHTTP(w, req)

	if w.Code == http.StatusOK {
		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)
		if response["message"] == "authenticated" {
			fmt.Printf("✅ Optional auth correctly recognizes authenticated user\n")
		} else {
			fmt.Printf("❌ Should recognize authenticated user\n")
		}
	}
}

func testTokenRevocation(jwtConfig *config.JWTConfig, jwtService auth.JWTService, token string) {
	// 首先验证令牌有效
	claims, err := jwtService.ValidateToken(token)
	if err != nil {
		fmt.Printf("❌ Token should be valid before revocation: %v\n", err)
		return
	}

	// 撤销令牌
	err = jwtService.RevokeToken(claims.TokenID)
	if err != nil {
		fmt.Printf("❌ Failed to revoke token: %v\n", err)
		return
	}

	// 创建路由测试撤销的令牌
	router := gin.New()
	router.Use(middleware.AuthMiddleware(jwtConfig))
	router.GET("/protected", func(c *gin.Context) {
		c.JSON(http.StatusOK, gin.H{"message": "success"})
	})

	req, _ := http.NewRequest("GET", "/protected", nil)
	req.Header.Set("Authorization", "Bearer "+token)
	w := httptest.NewRecorder()
	router.ServeHTTP(w, req)

	if w.Code == http.StatusUnauthorized {
		var response map[string]interface{}
		json.Unmarshal(w.Body.Bytes(), &response)
		if response["code"] == "TOKEN_REVOKED" {
			fmt.Printf("✅ Revoked token correctly rejected with TOKEN_REVOKED code\n")
		} else {
			fmt.Printf("✅ Revoked token rejected (code: %v)\n", response["code"])
		}
	} else {
		fmt.Printf("❌ Revoked token should be rejected, got %d\n", w.Code)
	}
}