import { request } from './http'
import type { 
  License,
  CreateLicenseRequest,
  UpdateLicenseRequest,
  VerifyLicenseRequest,
  VerifyLicenseResponse,
  LicenseListParams,
  PaginatedResponse
} from '@/types/license'

export const licenseApi = {
  // 获取许可证列表
  getList: (params?: LicenseListParams) => {
    return request.get<PaginatedResponse<License>>('/licenses', { params })
  },

  // 获取许可证详情
  getById: (id: number) => {
    return request.get<License>(`/licenses/${id}`)
  },

  // 创建许可证
  create: (data: CreateLicenseRequest) => {
    return request.post<License>('/licenses', data)
  },

  // 更新许可证
  update: (id: number, data: UpdateLicenseRequest) => {
    return request.put<License>(`/licenses/${id}`, data)
  },

  // 删除许可证
  delete: (id: number) => {
    return request.delete(`/licenses/${id}`)
  },

  // 禁用许可证
  disable: (id: number) => {
    return request.post(`/licenses/${id}/disable`)
  },

  // 启用许可证
  enable: (id: number) => {
    return request.post(`/licenses/${id}/enable`)
  },

  // 验证许可证
  verify: (data: VerifyLicenseRequest) => {
    return request.post<VerifyLicenseResponse>('/verify', data)
  },

  // 获取许可证统计信息
  getStats: () => {
    return request.get('/licenses/stats')
  },

  // 导出许可证数据
  export: (params?: LicenseListParams) => {
    return request.get('/licenses/export', { 
      params,
      responseType: 'blob'
    })
  }
}