package cache

import (
	"context"
	"fmt"
	"time"
)

// HealthStatus 缓存健康状态
type HealthStatus struct {
	Available    bool                   `json:"available"`
	Connected    bool                   `json:"connected"`
	ResponseTime time.Duration          `json:"response_time_ms"`
	Error        string                 `json:"error,omitempty"`
	Info         map[string]interface{} `json:"info,omitempty"`
}

// CheckHealth 检查缓存健康状态
func CheckHealth() *HealthStatus {
	status := &HealthStatus{
		Available: false,
		Connected: false,
		Info:      make(map[string]interface{}),
	}

	// 检查Redis客户端是否初始化
	if RedisClient == nil {
		status.Error = "Redis client not initialized"
		return status
	}

	// 测试连接和响应时间
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	start := time.Now()
	err := RedisClient.Ping(ctx).Err()
	status.ResponseTime = time.Since(start)

	if err != nil {
		status.Error = fmt.Sprintf("Redis ping failed: %v", err)
		return status
	}

	status.Connected = true
	status.Available = true

	// 获取Redis信息
	info, err := RedisClient.Info(ctx, "server", "memory", "stats").Result()
	if err == nil {
		status.Info["redis_info"] = info
	}

	// 获取数据库大小
	dbSize, err := RedisClient.DBSize(ctx).Result()
	if err == nil {
		status.Info["db_size"] = dbSize
	}

	// 获取内存使用情况
	memInfo, err := RedisClient.Info(ctx, "memory").Result()
	if err == nil {
		status.Info["memory_info"] = memInfo
	}

	return status
}

// IsHealthy 简单的健康检查
func IsHealthy() bool {
	if RedisClient == nil {
		return false
	}

	ctx, cancel := context.WithTimeout(context.Background(), 1*time.Second)
	defer cancel()

	return RedisClient.Ping(ctx).Err() == nil
}

// GetConnectionInfo 获取连接信息
func GetConnectionInfo() map[string]interface{} {
	info := make(map[string]interface{})

	if RedisClient == nil {
		info["status"] = "not_initialized"
		return info
	}

	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	// 基本连接信息
	info["client_initialized"] = true

	// 测试连接
	err := RedisClient.Ping(ctx).Err()
	if err != nil {
		info["status"] = "disconnected"
		info["error"] = err.Error()
		return info
	}

	info["status"] = "connected"

	// 获取连接池信息
	poolStats := RedisClient.PoolStats()
	info["pool_stats"] = map[string]interface{}{
		"hits":         poolStats.Hits,
		"misses":       poolStats.Misses,
		"timeouts":     poolStats.Timeouts,
		"total_conns":  poolStats.TotalConns,
		"idle_conns":   poolStats.IdleConns,
		"stale_conns":  poolStats.StaleConns,
	}

	// 获取数据库信息
	dbSize, err := RedisClient.DBSize(ctx).Result()
	if err == nil {
		info["db_size"] = dbSize
	}

	return info
}

// WarmupCache 预热缓存
func WarmupCache() error {
	if RedisClient == nil {
		return fmt.Errorf("Redis client not initialized")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	service := NewCacheService()

	// 预热一些基础缓存
	warmupData := []struct {
		key   string
		value interface{}
		ttl   time.Duration
	}{
		{"warmup:test", "cache_ready", 1 * time.Minute},
		{"warmup:timestamp", time.Now().Unix(), 1 * time.Minute},
	}

	for _, item := range warmupData {
		if err := service.SetJSON(ctx, item.key, item.value, item.ttl); err != nil {
			return fmt.Errorf("failed to warmup cache key %s: %v", item.key, err)
		}
	}

	return nil
}

// CleanupExpiredKeys 清理过期键（开发环境使用）
func CleanupExpiredKeys(pattern string) (int64, error) {
	if RedisClient == nil {
		return 0, fmt.Errorf("Redis client not initialized")
	}

	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	// 注意：KEYS命令在生产环境中应该谨慎使用
	keys, err := RedisClient.Keys(ctx, pattern).Result()
	if err != nil {
		return 0, err
	}

	if len(keys) == 0 {
		return 0, nil
	}

	deleted, err := RedisClient.Del(ctx, keys...).Result()
	return deleted, err
}

// GetCacheMetrics 获取缓存指标
func GetCacheMetrics() map[string]interface{} {
	metrics := make(map[string]interface{})

	if RedisClient == nil {
		metrics["status"] = "not_available"
		return metrics
	}

	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	// 基本指标
	metrics["available"] = IsHealthy()

	if !IsHealthy() {
		return metrics
	}

	// 连接池统计
	poolStats := RedisClient.PoolStats()
	metrics["pool"] = map[string]interface{}{
		"hits":        poolStats.Hits,
		"misses":      poolStats.Misses,
		"timeouts":    poolStats.Timeouts,
		"total_conns": poolStats.TotalConns,
		"idle_conns":  poolStats.IdleConns,
		"stale_conns": poolStats.StaleConns,
	}

	// 数据库大小
	if dbSize, err := RedisClient.DBSize(ctx).Result(); err == nil {
		metrics["db_size"] = dbSize
	}

	// 内存使用情况
	if info, err := RedisClient.Info(ctx, "memory").Result(); err == nil {
		metrics["memory_info"] = info
	}

	// 统计信息
	if info, err := RedisClient.Info(ctx, "stats").Result(); err == nil {
		metrics["stats_info"] = info
	}

	return metrics
}