package main

import (
	"context"
	"fmt"
	"log"
	"time"
	"software-auth/internal/config"
	"software-auth/internal/cache"
)

func main() {
	fmt.Println("Redis连接测试工具")
	fmt.Println("==================")

	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化Redis
	if err := cache.Initialize(&cfg.Redis); err != nil {
		log.Fatalf("Failed to initialize Redis: %v", err)
	}
	defer cache.Close()

	fmt.Printf("Redis地址: %s\n", cfg.Redis.Addr)
	fmt.Printf("数据库: %d\n", cfg.Redis.DB)

	// 测试基本连接
	if err := testConnection(); err != nil {
		log.Fatalf("Connection test failed: %v", err)
	}

	// 测试基本操作
	if err := testBasicOperations(); err != nil {
		log.Fatalf("Basic operations test failed: %v", err)
	}

	// 测试缓存管理器
	if err := testCacheManager(); err != nil {
		log.Fatalf("Cache manager test failed: %v", err)
	}

	fmt.Println("\n✅ 所有测试通过！Redis连接正常。")
}

// testConnection 测试连接
func testConnection() error {
	fmt.Println("\n🔍 测试Redis连接...")
	
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	service := cache.NewCacheService()
	if err := service.Ping(ctx); err != nil {
		return fmt.Errorf("ping failed: %v", err)
	}

	fmt.Println("✅ Redis连接成功")
	return nil
}

// testBasicOperations 测试基本操作
func testBasicOperations() error {
	fmt.Println("\n🔍 测试基本缓存操作...")
	
	ctx := context.Background()
	service := cache.NewCacheService()

	// 测试字符串操作
	testKey := "test:string"
	testValue := "Hello Redis!"
	
	// 设置
	if err := service.Set(ctx, testKey, testValue, 1*time.Minute); err != nil {
		return fmt.Errorf("set failed: %v", err)
	}
	fmt.Println("✅ SET操作成功")

	// 获取
	result, err := service.Get(ctx, testKey)
	if err != nil {
		return fmt.Errorf("get failed: %v", err)
	}
	if result != testValue {
		return fmt.Errorf("value mismatch: expected %s, got %s", testValue, result)
	}
	fmt.Println("✅ GET操作成功")

	// 测试JSON操作
	testData := map[string]interface{}{
		"name": "Test User",
		"age":  25,
		"active": true,
	}
	
	jsonKey := "test:json"
	if err := service.SetJSON(ctx, jsonKey, testData, 1*time.Minute); err != nil {
		return fmt.Errorf("setJSON failed: %v", err)
	}
	fmt.Println("✅ SetJSON操作成功")

	var retrievedData map[string]interface{}
	if err := service.GetJSON(ctx, jsonKey, &retrievedData); err != nil {
		return fmt.Errorf("getJSON failed: %v", err)
	}
	fmt.Println("✅ GetJSON操作成功")

	// 测试存在性检查
	exists, err := service.Exists(ctx, testKey)
	if err != nil {
		return fmt.Errorf("exists check failed: %v", err)
	}
	if !exists {
		return fmt.Errorf("key should exist")
	}
	fmt.Println("✅ EXISTS操作成功")

	// 测试删除
	if err := service.Delete(ctx, testKey, jsonKey); err != nil {
		return fmt.Errorf("delete failed: %v", err)
	}
	fmt.Println("✅ DELETE操作成功")

	// 验证删除
	exists, err = service.Exists(ctx, testKey)
	if err != nil {
		return fmt.Errorf("exists check after delete failed: %v", err)
	}
	if exists {
		return fmt.Errorf("key should not exist after delete")
	}
	fmt.Println("✅ 删除验证成功")

	return nil
}

// testCacheManager 测试缓存管理器
func testCacheManager() error {
	fmt.Println("\n🔍 测试缓存管理器...")
	
	ctx := context.Background()
	manager := cache.NewManager()

	// 测试JWT令牌缓存
	tokenData := &cache.AuthTokenData{
		UserID:   1,
		Username: "testuser",
		Role:     "admin",
		IssuedAt: time.Now().Unix(),
	}

	tokenID := "test-token-123"
	if err := manager.SetAuthToken(ctx, tokenID, tokenData); err != nil {
		return fmt.Errorf("setAuthToken failed: %v", err)
	}
	fmt.Println("✅ JWT令牌缓存设置成功")

	retrievedToken, err := manager.GetAuthToken(ctx, tokenID)
	if err != nil {
		return fmt.Errorf("getAuthToken failed: %v", err)
	}
	if retrievedToken.Username != tokenData.Username {
		return fmt.Errorf("token data mismatch")
	}
	fmt.Println("✅ JWT令牌缓存获取成功")

	// 测试许可证验证缓存
	licenseData := &cache.LicenseVerifyData{
		Valid:       true,
		LicenseKey:  "TEST-LICENSE-123",
		ProductName: "Test Product",
		MaxDevices:  5,
		Features:    []string{"basic", "advanced"},
		DeviceCount: 2,
		VerifiedAt:  time.Now(),
	}

	if err := manager.SetLicenseVerifyResult(ctx, licenseData.LicenseKey, licenseData); err != nil {
		return fmt.Errorf("setLicenseVerifyResult failed: %v", err)
	}
	fmt.Println("✅ 许可证验证缓存设置成功")

	retrievedLicense, err := manager.GetLicenseVerifyResult(ctx, licenseData.LicenseKey)
	if err != nil {
		return fmt.Errorf("getLicenseVerifyResult failed: %v", err)
	}
	if retrievedLicense.ProductName != licenseData.ProductName {
		return fmt.Errorf("license data mismatch")
	}
	fmt.Println("✅ 许可证验证缓存获取成功")

	// 测试限流
	allowed, err := manager.CheckRateLimit(ctx, "127.0.0.1", "/api/test", 10)
	if err != nil {
		return fmt.Errorf("checkRateLimit failed: %v", err)
	}
	if !allowed {
		return fmt.Errorf("rate limit should allow first request")
	}
	fmt.Println("✅ 限流检查成功")

	// 清理测试数据
	if err := manager.DeleteAuthToken(ctx, tokenID); err != nil {
		return fmt.Errorf("deleteAuthToken failed: %v", err)
	}
	
	if err := manager.InvalidateLicenseCache(ctx, licenseData.LicenseKey); err != nil {
		return fmt.Errorf("invalidateLicenseCache failed: %v", err)
	}
	fmt.Println("✅ 缓存清理成功")

	return nil
}