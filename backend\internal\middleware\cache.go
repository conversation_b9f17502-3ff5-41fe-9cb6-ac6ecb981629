package middleware

import (
	"bytes"
	"context"
	"crypto/md5"
	"fmt"
	"net/http"
	"strings"
	"time"

	"github.com/gin-gonic/gin"
	"software-auth/internal/cache"
)

// CacheConfig 缓存中间件配置
type CacheConfig struct {
	TTL           time.Duration // 缓存时间
	KeyPrefix     string        // 键前缀
	SkipMethods   []string      // 跳过的HTTP方法
	SkipPaths     []string      // 跳过的路径
	SkipQuery     bool          // 是否跳过查询参数
	SkipHeaders   []string      // 跳过的请求头
}

// DefaultCacheConfig 默认缓存配置
func DefaultCacheConfig() *CacheConfig {
	return &CacheConfig{
		TTL:         5 * time.Minute,
		KeyPrefix:   "api_cache",
		SkipMethods: []string{"POST", "PUT", "DELETE", "PATCH"},
		SkipPaths:   []string{"/health", "/metrics"},
		SkipQuery:   false,
		SkipHeaders: []string{"Authorization", "Cookie"},
	}
}

// CacheMiddleware 缓存中间件
func CacheMiddleware(config *CacheConfig) gin.HandlerFunc {
	if config == nil {
		config = DefaultCacheConfig()
	}

	return func(c *gin.Context) {
		// 检查是否跳过缓存
		if shouldSkipCache(c, config) {
			c.Next()
			return
		}

		// 检查Redis是否可用
		if !isCacheAvailable() {
			c.Header("X-Cache", "DISABLED")
			c.Next()
			return
		}

		cacheService := cache.NewCacheService()

		// 生成缓存键
		cacheKey := generateCacheKey(c, config)

		// 尝试从缓存获取响应
		cachedResponse, err := cacheService.Get(c.Request.Context(), cacheKey)
		if err == nil {
			// 缓存命中
			c.Header("X-Cache", "HIT")
			c.Header("Content-Type", "application/json")
			c.String(http.StatusOK, cachedResponse)
			c.Abort()
			return
		}

		// 缓存未命中或出错，继续处理请求
		if err == cache.ErrCacheNotFound {
			c.Header("X-Cache", "MISS")
		} else {
			c.Header("X-Cache", "ERROR")
		}

		// 创建响应写入器来捕获响应
		writer := &cacheResponseWriter{
			ResponseWriter: c.Writer,
			body:          &bytes.Buffer{},
		}
		c.Writer = writer

		// 处理请求
		c.Next()

		// 如果响应成功且缓存可用，缓存响应
		if writer.status == http.StatusOK && writer.body.Len() > 0 && isCacheAvailable() {
			responseBody := writer.body.String()
			if err := cacheService.Set(c.Request.Context(), cacheKey, responseBody, config.TTL); err != nil {
				// 缓存失败不影响响应，只记录日志
				fmt.Printf("Failed to cache response for key %s: %v\n", cacheKey, err)
			}
		}
	}
}

// cacheResponseWriter 缓存响应写入器
type cacheResponseWriter struct {
	gin.ResponseWriter
	body   *bytes.Buffer
	status int
}

// Write 写入响应体
func (w *cacheResponseWriter) Write(data []byte) (int, error) {
	w.body.Write(data)
	return w.ResponseWriter.Write(data)
}

// WriteHeader 写入响应头
func (w *cacheResponseWriter) WriteHeader(statusCode int) {
	w.status = statusCode
	w.ResponseWriter.WriteHeader(statusCode)
}

// shouldSkipCache 检查是否应该跳过缓存
func shouldSkipCache(c *gin.Context, config *CacheConfig) bool {
	// 检查HTTP方法
	for _, method := range config.SkipMethods {
		if c.Request.Method == method {
			return true
		}
	}

	// 检查路径
	for _, path := range config.SkipPaths {
		if strings.HasPrefix(c.Request.URL.Path, path) {
			return true
		}
	}

	// 检查是否有认证头（通常不缓存需要认证的请求）
	if c.GetHeader("Authorization") != "" {
		return true
	}

	return false
}

// generateCacheKey 生成缓存键
func generateCacheKey(c *gin.Context, config *CacheConfig) string {
	var keyParts []string

	// 添加前缀
	keyParts = append(keyParts, config.KeyPrefix)

	// 添加路径
	keyParts = append(keyParts, c.Request.URL.Path)

	// 添加查询参数（如果不跳过）
	if !config.SkipQuery && c.Request.URL.RawQuery != "" {
		keyParts = append(keyParts, c.Request.URL.RawQuery)
	}

	// 添加相关请求头
	for key, values := range c.Request.Header {
		// 跳过指定的请求头
		skip := false
		for _, skipHeader := range config.SkipHeaders {
			if strings.EqualFold(key, skipHeader) {
				skip = true
				break
			}
		}
		if !skip && len(values) > 0 {
			keyParts = append(keyParts, fmt.Sprintf("%s:%s", key, values[0]))
		}
	}

	// 生成MD5哈希
	keyString := strings.Join(keyParts, "|")
	hash := md5.Sum([]byte(keyString))
	return fmt.Sprintf("%x", hash)
}

// CacheInvalidationMiddleware 缓存失效中间件
func CacheInvalidationMiddleware(patterns []string) gin.HandlerFunc {
	return func(c *gin.Context) {
		// 处理请求
		c.Next()

		// 如果是修改操作且成功，清除相关缓存
		if isModifyingMethod(c.Request.Method) && c.Writer.Status() < 400 {
			for _, pattern := range patterns {
				// 这里可以实现更复杂的缓存失效逻辑
				// 例如根据请求路径和参数确定要清除的缓存键
				if strings.Contains(c.Request.URL.Path, pattern) {
					// 实际实现中可能需要更精确的缓存键匹配
					fmt.Printf("Invalidating cache for pattern: %s\n", pattern)
				}
			}
		}
	}
}

// isModifyingMethod 检查是否为修改方法
func isModifyingMethod(method string) bool {
	modifyingMethods := []string{"POST", "PUT", "DELETE", "PATCH"}
	for _, m := range modifyingMethods {
		if method == m {
			return true
		}
	}
	return false
}



// isCacheAvailable 检查缓存是否可用
func isCacheAvailable() bool {
	client := cache.GetClient()
	if client == nil {
		return false
	}
	
	// 快速ping检查
	ctx, cancel := context.WithTimeout(context.Background(), 100*time.Millisecond)
	defer cancel()
	
	return client.Ping(ctx).Err() == nil
}