package api

import (
	"crypto/hmac"
	"crypto/sha256"
	"encoding/base64"
	"encoding/json"
	"fmt"
	"net/http"
	"strings"
	"time"

	"software-auth/internal/database"
	"software-auth/internal/model"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// OfflineHandler 离线验证处理器
type OfflineHandler struct {
	secretKey string // 用于签名的密钥
}

// NewOfflineHandler 创建离线验证处理器
func NewOfflineHandler() *OfflineHandler {
	return &OfflineHandler{
		secretKey: "your-secret-key-for-offline-verification", // 实际应用中应该从配置文件读取
	}
}

// GenerateOfflineTokenRequest 生成离线令牌请求
type GenerateOfflineTokenRequest struct {
	LicenseKey string `json:"license_key" binding:"required"`
	DeviceID   string `json:"device_id" binding:"required"`
	ValidDays  int    `json:"valid_days" binding:"required,min=1,max=365"`
}

// OfflineTokenResponse 离线令牌响应
type OfflineTokenResponse struct {
	Token     string    `json:"token"`
	ExpiresAt time.Time `json:"expires_at"`
	ValidDays int       `json:"valid_days"`
}

// VerifyOfflineTokenRequest 验证离线令牌请求
type VerifyOfflineTokenRequest struct {
	Token       string `json:"token" binding:"required"`
	DeviceID    string `json:"device_id" binding:"required"`
	ProductName string `json:"product_name" binding:"required"`
}

// OfflineTokenData 离线令牌数据结构
type OfflineTokenData struct {
	LicenseKey     string    `json:"license_key"`
	DeviceID       string    `json:"device_id"`
	CustomerName   string    `json:"customer_name"`
	ProductName    string    `json:"product_name"`
	ProductVersion string    `json:"product_version"`
	LicenseType    string    `json:"license_type"`
	Features       []string  `json:"features"`
	IssuedAt       time.Time `json:"issued_at"`
	ExpiresAt      time.Time `json:"expires_at"`
	MaxDevices     int       `json:"max_devices"`
}

// OfflineVerificationResponse 离线验证响应
type OfflineVerificationResponse struct {
	Valid          bool      `json:"valid"`
	LicenseKey     string    `json:"license_key"`
	CustomerName   string    `json:"customer_name"`
	ProductName    string    `json:"product_name"`
	ProductVersion string    `json:"product_version"`
	LicenseType    string    `json:"license_type"`
	Features       []string  `json:"features"`
	MaxDevices     int       `json:"max_devices"`
	ExpiresAt      time.Time `json:"expires_at"`
	DaysRemaining  int       `json:"days_remaining"`
	Status         string    `json:"status"`
	Message        string    `json:"message"`
	IsOffline      bool      `json:"is_offline"`
}

// SyncOfflineDataRequest 同步离线数据请求
type SyncOfflineDataRequest struct {
	DeviceID       string               `json:"device_id" binding:"required"`
	OfflineRecords []OfflineUsageRecord `json:"offline_records"`
}

// OfflineUsageRecord 离线使用记录
type OfflineUsageRecord struct {
	LicenseKey string    `json:"license_key"`
	DeviceID   string    `json:"device_id"`
	UsedAt     time.Time `json:"used_at"`
	Features   []string  `json:"features"`
	Duration   int       `json:"duration"` // 使用时长（分钟）
}

// GenerateOfflineToken 生成离线验证令牌
func (h *OfflineHandler) GenerateOfflineToken(c *gin.Context) {
	var req GenerateOfflineTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ErrorResponse(c, http.StatusBadRequest, "Invalid request format", err.Error())
		return
	}

	// 查找许可证
	db := database.GetDB()
	var license model.License
	if err := db.Where("license_key = ?", req.LicenseKey).First(&license).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			ErrorResponse(c, http.StatusNotFound, "License not found", "INVALID_LICENSE")
			return
		}
		ErrorResponse(c, http.StatusInternalServerError, "Database error", err.Error())
		return
	}

	// 检查许可证状态
	if license.Status != "active" {
		ErrorResponse(c, http.StatusForbidden, fmt.Sprintf("License is %s", license.Status), "LICENSE_INACTIVE")
		return
	}

	// 检查许可证是否过期
	if license.ExpiresAt != nil && license.ExpiresAt.Before(time.Now()) {
		ErrorResponse(c, http.StatusForbidden, "License has expired", "LICENSE_EXPIRED")
		return
	}

	// 检查设备是否已绑定
	var binding model.DeviceBinding
	if err := db.Where("license_key = ? AND device_id = ?", license.LicenseKey, req.DeviceID).First(&binding).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			ErrorResponse(c, http.StatusForbidden, "Device not bound to this license", "DEVICE_NOT_BOUND")
			return
		}
		ErrorResponse(c, http.StatusInternalServerError, "Database error", err.Error())
		return
	}

	// 计算离线令牌过期时间（不能超过许可证过期时间）
	now := time.Now()
	tokenExpiresAt := now.AddDate(0, 0, req.ValidDays)
	if license.ExpiresAt != nil && tokenExpiresAt.After(*license.ExpiresAt) {
		tokenExpiresAt = *license.ExpiresAt
	}

	// 解析功能列表
	var features []string
	if license.Features != "" {
		features = strings.Split(license.Features, ",")
	}

	// 创建离线令牌数据
	tokenData := OfflineTokenData{
		LicenseKey:   license.LicenseKey,
		DeviceID:     req.DeviceID,
		CustomerName: license.CustomerName,
		ProductName:  license.ProductName,
		Features:     features,
		IssuedAt:     now,
		ExpiresAt:    tokenExpiresAt,
		MaxDevices:   license.MaxDevices,
	}

	// 生成签名令牌
	token, err := h.createSignedToken(tokenData)
	if err != nil {
		ErrorResponse(c, http.StatusInternalServerError, "Failed to generate offline token", err.Error())
		return
	}

	response := OfflineTokenResponse{
		Token:     token,
		ExpiresAt: tokenExpiresAt,
		ValidDays: int(tokenExpiresAt.Sub(now).Hours() / 24),
	}

	SuccessResponse(c, "Offline token generated successfully", response)
}

// VerifyOfflineToken 验证离线令牌
func (h *OfflineHandler) VerifyOfflineToken(c *gin.Context) {
	var req VerifyOfflineTokenRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ErrorResponse(c, http.StatusBadRequest, "Invalid request format", err.Error())
		return
	}

	// 验证并解析令牌
	tokenData, err := h.verifySignedToken(req.Token)
	if err != nil {
		c.JSON(http.StatusOK, OfflineVerificationResponse{
			Valid:     false,
			Status:    "invalid_token",
			Message:   "Invalid or corrupted offline token",
			IsOffline: true,
		})
		return
	}

	// 检查设备ID匹配
	if tokenData.DeviceID != req.DeviceID {
		c.JSON(http.StatusOK, OfflineVerificationResponse{
			Valid:     false,
			Status:    "device_mismatch",
			Message:   "Token is not valid for this device",
			IsOffline: true,
		})
		return
	}

	// 检查产品名称匹配
	if tokenData.ProductName != req.ProductName {
		c.JSON(http.StatusOK, OfflineVerificationResponse{
			Valid:     false,
			Status:    "product_mismatch",
			Message:   "Token is not valid for this product",
			IsOffline: true,
		})
		return
	}

	// 检查令牌是否过期
	now := time.Now()
	if tokenData.ExpiresAt.Before(now) {
		c.JSON(http.StatusOK, OfflineVerificationResponse{
			Valid:         false,
			Status:        "expired",
			Message:       "Offline token has expired",
			IsOffline:     true,
			ExpiresAt:     tokenData.ExpiresAt,
			DaysRemaining: 0,
		})
		return
	}

	// 计算剩余天数
	daysRemaining := int(tokenData.ExpiresAt.Sub(now).Hours() / 24)

	// 返回验证结果
	response := OfflineVerificationResponse{
		Valid:          true,
		LicenseKey:     tokenData.LicenseKey,
		CustomerName:   tokenData.CustomerName,
		ProductName:    tokenData.ProductName,
		ProductVersion: tokenData.ProductVersion,
		LicenseType:    tokenData.LicenseType,
		Features:       tokenData.Features,
		MaxDevices:     tokenData.MaxDevices,
		ExpiresAt:      tokenData.ExpiresAt,
		DaysRemaining:  daysRemaining,
		Status:         "valid",
		Message:        "Offline token is valid",
		IsOffline:      true,
	}

	c.JSON(http.StatusOK, response)
}

// SyncOfflineData 同步离线使用数据
func (h *OfflineHandler) SyncOfflineData(c *gin.Context) {
	var req SyncOfflineDataRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ErrorResponse(c, http.StatusBadRequest, "Invalid request format", err.Error())
		return
	}

	db := database.GetDB()

	// 处理每个离线使用记录
	syncedCount := 0
	for _, record := range req.OfflineRecords {
		// 查找许可证
		var license model.License
		if err := db.Where("license_key = ?", record.LicenseKey).First(&license).Error; err != nil {
			continue // 跳过无效的许可证
		}

		// 检查是否已经同步过这条记录（避免重复）
		var existingLog model.VerificationLog
		if err := db.Where("license_key = ? AND device_id = ? AND created_at = ?",
			license.LicenseKey, record.DeviceID, record.UsedAt).First(&existingLog).Error; err == nil {
			continue // 记录已存在，跳过
		}

		// 创建验证日志记录
		log := model.VerificationLog{
			LicenseKey:         license.LicenseKey,
			DeviceID:           record.DeviceID,
			IPAddress:          c.ClientIP(),
			VerificationResult: model.VerificationResultSuccess,
			ErrorMessage:       fmt.Sprintf("Offline usage synced - Features: %s, Duration: %d minutes", strings.Join(record.Features, ","), record.Duration),
			CreatedAt:          record.UsedAt,
		}

		if err := db.Create(&log).Error; err == nil {
			syncedCount++
		}

		// 更新设备最后见到时间
		db.Model(&model.DeviceBinding{}).
			Where("license_key = ? AND device_id = ?", license.LicenseKey, record.DeviceID).
			Update("last_seen_at", record.UsedAt)
	}

	SuccessResponse(c, "Offline data synchronized successfully", gin.H{
		"total_records":  len(req.OfflineRecords),
		"synced_records": syncedCount,
		"device_id":      req.DeviceID,
	})
}

// GetOfflineTokenInfo 获取离线令牌信息（不验证，仅解析）
func (h *OfflineHandler) GetOfflineTokenInfo(c *gin.Context) {
	token := c.Query("token")
	if token == "" {
		ErrorResponse(c, http.StatusBadRequest, "Token parameter is required", "MISSING_TOKEN")
		return
	}

	// 尝试解析令牌（不验证签名）
	tokenData, err := h.parseTokenData(token)
	if err != nil {
		ErrorResponse(c, http.StatusBadRequest, "Invalid token format", err.Error())
		return
	}

	// 计算状态
	now := time.Now()
	status := "valid"
	if tokenData.ExpiresAt.Before(now) {
		status = "expired"
	}

	daysRemaining := int(tokenData.ExpiresAt.Sub(now).Hours() / 24)
	if daysRemaining < 0 {
		daysRemaining = 0
	}

	response := gin.H{
		"license_key":     tokenData.LicenseKey,
		"device_id":       tokenData.DeviceID,
		"customer_name":   tokenData.CustomerName,
		"product_name":    tokenData.ProductName,
		"product_version": tokenData.ProductVersion,
		"license_type":    tokenData.LicenseType,
		"features":        tokenData.Features,
		"max_devices":     tokenData.MaxDevices,
		"issued_at":       tokenData.IssuedAt,
		"expires_at":      tokenData.ExpiresAt,
		"days_remaining":  daysRemaining,
		"status":          status,
		"is_offline":      true,
	}

	SuccessResponse(c, "Token information retrieved successfully", response)
}

// createSignedToken 创建签名令牌
func (h *OfflineHandler) createSignedToken(data OfflineTokenData) (string, error) {
	// 序列化数据
	jsonData, err := json.Marshal(data)
	if err != nil {
		return "", err
	}

	// Base64编码
	encodedData := base64.StdEncoding.EncodeToString(jsonData)

	// 创建HMAC签名
	mac := hmac.New(sha256.New, []byte(h.secretKey))
	mac.Write([]byte(encodedData))
	signature := base64.StdEncoding.EncodeToString(mac.Sum(nil))

	// 组合令牌：数据.签名
	token := encodedData + "." + signature

	return token, nil
}

// verifySignedToken 验证签名令牌
func (h *OfflineHandler) verifySignedToken(token string) (*OfflineTokenData, error) {
	// 分割令牌
	parts := strings.Split(token, ".")
	if len(parts) != 2 {
		return nil, fmt.Errorf("invalid token format")
	}

	encodedData := parts[0]
	signature := parts[1]

	// 验证签名
	mac := hmac.New(sha256.New, []byte(h.secretKey))
	mac.Write([]byte(encodedData))
	expectedSignature := base64.StdEncoding.EncodeToString(mac.Sum(nil))

	if !hmac.Equal([]byte(signature), []byte(expectedSignature)) {
		return nil, fmt.Errorf("invalid token signature")
	}

	// 解码数据
	jsonData, err := base64.StdEncoding.DecodeString(encodedData)
	if err != nil {
		return nil, fmt.Errorf("failed to decode token data")
	}

	// 反序列化数据
	var tokenData OfflineTokenData
	if err := json.Unmarshal(jsonData, &tokenData); err != nil {
		return nil, fmt.Errorf("failed to parse token data")
	}

	return &tokenData, nil
}

// parseTokenData 解析令牌数据（不验证签名）
func (h *OfflineHandler) parseTokenData(token string) (*OfflineTokenData, error) {
	// 分割令牌
	parts := strings.Split(token, ".")
	if len(parts) != 2 {
		return nil, fmt.Errorf("invalid token format")
	}

	encodedData := parts[0]

	// 解码数据
	jsonData, err := base64.StdEncoding.DecodeString(encodedData)
	if err != nil {
		return nil, fmt.Errorf("failed to decode token data")
	}

	// 反序列化数据
	var tokenData OfflineTokenData
	if err := json.Unmarshal(jsonData, &tokenData); err != nil {
		return nil, fmt.Errorf("failed to parse token data")
	}

	return &tokenData, nil
}
