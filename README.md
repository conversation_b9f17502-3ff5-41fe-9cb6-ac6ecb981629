# 软件网络授权系统

一个完整的软件许可证管理和验证平台，支持许可证生成、验证、管理和监控功能。

## 🚀 功能特性

- 🔐 **用户认证和权限管理** - JWT认证、角色权限控制
- 📄 **许可证生成和管理** - 灵活的许可证配置和管理
- ✅ **在线/离线许可证验证** - 支持多种验证模式
- 📊 **使用统计和报告** - 详细的使用数据分析
- 🛡️ **安全审计和日志** - 完整的操作记录
- 🖥️ **现代化管理界面** - 响应式Web界面
- 🚀 **高性能缓存机制** - Redis缓存优化
- 📱 **响应式设计** - 支持多设备访问

## 🛠️ 技术栈

### 前端
- **Vue3** - 响应式前端框架
- **Vite** - 快速构建工具
- **Arco Design** - 企业级UI组件库
- **Vue Router** - 路由管理
- **Pinia** - 状态管理
- **Axios** - HTTP客户端
- **TypeScript** - 类型安全

### 后端
- **Gin** - 高性能Web框架
- **GORM** - ORM数据库操作
- **SQLite** - 轻量级数据库
- **Redis** - 缓存和会话存储
- **JWT** - 身份认证
- **bcrypt** - 密码加密

### 部署环境
- **Windows Server** - 主要部署平台
- **支持Windows服务部署**

## 📁 项目结构

```
software-auth/
├── backend/                 # 后端Go服务
│   ├── cmd/                # 应用入口
│   │   ├── server/         # 主服务器
│   │   ├── migrate/        # 数据库迁移工具
│   │   ├── jwt-test/       # JWT测试工具
│   │   └── redis-test/     # Redis测试工具
│   ├── internal/           # 内部包
│   │   ├── api/            # API处理器
│   │   ├── auth/           # 认证服务
│   │   ├── cache/          # 缓存服务
│   │   ├── config/         # 配置管理
│   │   ├── database/       # 数据库操作
│   │   ├── middleware/     # 中间件
│   │   ├── model/          # 数据模型
│   │   ├── server/         # 服务器核心
│   │   └── service/        # 业务逻辑
│   ├── configs/            # 配置文件
│   └── scripts/            # 管理脚本
├── frontend/               # 前端Vue应用
│   ├── src/                # 源代码
│   │   ├── api/            # API接口
│   │   ├── components/     # 组件
│   │   ├── layouts/        # 布局
│   │   ├── router/         # 路由配置
│   │   ├── stores/         # 状态管理
│   │   ├── types/          # 类型定义
│   │   └── views/          # 页面组件
│   ├── public/             # 静态资源
│   └── scripts/            # 构建脚本
├── .kiro/                  # Kiro IDE配置
│   └── specs/              # 项目规格文档
└── docs/                   # 项目文档
```

## 🚀 快速开始

### 环境要求
- **Go 1.21+**
- **Node.js 18+**
- **Redis 6+** (可选，用于缓存)

### 一键启动
```bash
# Windows环境
start-dev.bat

# 选择选项3同时启动前后端
```

### 手动启动

#### 后端服务
```bash
cd backend
go mod tidy
go run cmd/server/main.go
```

#### 前端服务
```bash
cd frontend
npm install
npm run dev
```

### 数据库初始化
```bash
cd backend
go run cmd/migrate/main.go -migrate
go run cmd/migrate/main.go -seed
```

## 🔑 默认账户

| 角色 | 用户名 | 密码 | 说明 |
|------|--------|------|------|
| 管理员 | admin | AdminPass123! | 系统管理员 |
| 普通用户 | testuser | TestPass123! | 测试用户 |
| API用户 | apiuser | ApiPass123! | API访问用户 |

## 🌐 访问地址

- **前端界面**: http://localhost:5173
- **后端API**: http://localhost:8080
- **API文档**: http://localhost:8080/swagger/index.html (开发中)
- **健康检查**: http://localhost:8080/health

## 📖 主要页面

### 前端界面
- **登录页面** - 用户认证
- **仪表板** - 系统概览和统计
- **许可证管理** - 许可证CRUD操作
- **许可证详情** - 详细信息和设备绑定
- **个人资料** - 用户信息管理
- **用户管理** - 用户和权限管理(管理员)
- **统计报告** - 使用数据分析(管理员)

### API接口
- **认证接口** - 登录、登出、令牌刷新
- **许可证接口** - 许可证管理和验证
- **用户接口** - 用户管理
- **报告接口** - 统计数据

## 🛠️ 开发工具

### 后端工具
```bash
# 数据库管理
backend/scripts/db.bat

# Redis管理
backend/scripts/redis.bat

# 认证测试
backend/scripts/auth.bat
```

### 前端工具
```bash
# 构建工具
frontend/scripts/build.bat
```

## 🔧 配置说明

### 后端配置 (backend/configs/config.yaml)
```yaml
server:
  port: 8080
  mode: debug

database:
  driver: sqlite
  dsn: "./data/database.db"

redis:
  addr: "localhost:6379"
  db: 0

jwt:
  secret: "your-secret-key"
  expire_hours: 24
```

### 前端配置 (frontend/vite.config.ts)
```typescript
export default defineConfig({
  server: {
    port: 5173,
    proxy: {
      '/api': 'http://localhost:8080'
    }
  }
})
```

## 📝 开发进度

### 已完成功能 ✅
- [x] 项目基础架构搭建
- [x] 数据库模型和连接
- [x] Redis缓存集成
- [x] JWT认证中间件
- [x] Vue3前端项目搭建
- [x] 基础布局和组件
- [x] 登录和认证页面
- [x] 许可证列表和搜索
- [x] 许可证创建和编辑
- [x] 许可证详情和操作

### 开发中功能 🚧
- [ ] 用户认证API
- [ ] 用户管理API
- [ ] 许可证生成和管理API
- [ ] 许可证验证服务
- [ ] 统计报告功能
- [ ] 用户管理界面
- [ ] 系统监控和日志

## 🤝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 📞 支持

如有问题或建议，请通过以下方式联系：

- 创建 [Issue](https://github.com/your-repo/software-auth/issues)
- 发送邮件至 <EMAIL>

---

**软件网络授权系统** - 让软件许可证管理变得简单高效 🚀