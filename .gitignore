# 编译输出
/backend/bin/
/frontend/dist/
/frontend/dist-ssr/

# 依赖
/backend/vendor/
/frontend/node_modules/

# 日志
*.log
/backend/data/logs/

# 数据库
/backend/data/*.db
/backend/data/*.db-*

# 配置文件（生产环境）
/backend/configs/production.yaml
/backend/configs/config.local.yaml

# IDE
.vscode/
.idea/
*.swp
*.swo

# 操作系统
.DS_Store
Thumbs.db

# 临时文件
*.tmp
*.temp

# 环境变量
.env
.env.local
.env.production

# Go
*.exe
*.exe~
*.dll
*.so
*.dylib
*.test
*.out
go.work

# Node.js
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*

# TypeScript
*.tsbuildinfo

# 自动生成的文件
/frontend/auto-imports.d.ts
/frontend/components.d.ts