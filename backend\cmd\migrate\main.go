package main

import (
	"flag"
	"fmt"
	"log"
	"os"
	"software-auth/internal/config"
	"software-auth/internal/database"
)

func main() {
	var (
		migrate = flag.Bool("migrate", false, "Run database migrations")
		seed    = flag.Bool("seed", false, "Seed test data")
		reset   = flag.Bool("reset", false, "Reset database (drop all tables)")
	)
	flag.Parse()

	// 加载配置
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// 初始化数据库
	if err := database.Initialize(&cfg.Database); err != nil {
		log.Fatalf("Failed to initialize database: %v", err)
	}
	defer database.Close()

	switch {
	case *reset:
		if err := resetDatabase(); err != nil {
			log.Fatalf("Failed to reset database: %v", err)
		}
		fmt.Println("Database reset completed")

	case *migrate:
		if err := database.RunMigrations(); err != nil {
			log.Fatalf("Failed to run migrations: %v", err)
		}
		fmt.Println("Migrations completed")

	case *seed:
		if err := database.SeedData(); err != nil {
			log.Fatalf("Failed to seed data: %v", err)
		}
		fmt.Println("Data seeding completed")

	default:
		fmt.Println("Usage:")
		fmt.Println("  -migrate  Run database migrations")
		fmt.Println("  -seed     Seed test data")
		fmt.Println("  -reset    Reset database (drop all tables)")
		os.Exit(1)
	}
}

// resetDatabase 重置数据库
func resetDatabase() error {
	db := database.GetDB()
	
	// 删除所有表
	tables := []string{
		"verification_logs",
		"device_bindings", 
		"licenses",
		"users",
		"migration_versions",
	}

	for _, table := range tables {
		if err := db.Exec(fmt.Sprintf("DROP TABLE IF EXISTS %s", table)).Error; err != nil {
			return err
		}
	}

	log.Println("All tables dropped")
	return nil
}