package api

import (
	"crypto/rand"
	"encoding/hex"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"software-auth/internal/cache"
	"software-auth/internal/database"
	"software-auth/internal/model"

	"github.com/gin-gonic/gin"
	"gorm.io/gorm"
)

// LicenseHandler 许可证处理器
type LicenseHandler struct {
	cacheManager *cache.Manager
}

// NewLicenseHandler 创建许可证处理器
func NewLicenseHandler() *LicenseHandler {
	return &LicenseHandler{
		cacheManager: cache.NewManager(),
	}
}

// CreateLicenseRequest 创建许可证请求
type CreateLicenseRequest struct {
	CustomerName  string    `json:"customer_name" binding:"required"`
	CustomerEmail string    `json:"customer_email" binding:"required,email"`
	ProductName   string    `json:"product_name" binding:"required"`
	MaxDevices    int       `json:"max_devices" binding:"required,min=1"`
	Features      []string  `json:"features"`
	ExpiresAt     time.Time `json:"expires_at" binding:"required"`
}

// UpdateLicenseRequest 更新许可证请求
type UpdateLicenseRequest struct {
	CustomerName  string    `json:"customer_name"`
	CustomerEmail string    `json:"customer_email" binding:"omitempty,email"`
	ProductName   string    `json:"product_name"`
	MaxDevices    int       `json:"max_devices" binding:"omitempty,min=1"`
	Features      []string  `json:"features"`
	ExpiresAt     time.Time `json:"expires_at"`
	Status        string    `json:"status" binding:"omitempty,oneof=active expired disabled suspended"`
}

// LicenseListRequest 许可证列表请求
type LicenseListRequest struct {
	Page         int    `form:"page,default=1" binding:"min=1"`
	PageSize     int    `form:"page_size,default=10" binding:"min=1,max=100"`
	Keyword      string `form:"keyword"`
	Status       string `form:"status" binding:"omitempty,oneof=active expired disabled suspended"`
	LicenseType  string `form:"license_type" binding:"omitempty,oneof=trial standard professional enterprise"`
	CustomerName string `form:"customer_name"`
	SortBy       string `form:"sort_by,default=created_at" binding:"omitempty,oneof=created_at expires_at customer_name product_name"`
	SortOrder    string `form:"sort_order,default=desc" binding:"omitempty,oneof=asc desc"`
}

// LicenseResponse 许可证响应
type LicenseResponse struct {
	ID            uint       `json:"id"`
	LicenseKey    string     `json:"license_key"`
	CustomerName  string     `json:"customer_name"`
	CustomerEmail string     `json:"customer_email"`
	ProductName   string     `json:"product_name"`
	MaxDevices    int        `json:"max_devices"`
	Features      []string   `json:"features"`
	Status        string     `json:"status"`
	CreatedAt     time.Time  `json:"created_at"`
	ExpiresAt     *time.Time `json:"expires_at"`
	DeviceCount   int        `json:"device_count"`
	LastUsedAt    time.Time  `json:"last_used_at"`
}

// LicenseListResponse 许可证列表响应
type LicenseListResponse struct {
	Licenses   []LicenseResponse `json:"licenses"`
	Total      int64             `json:"total"`
	Page       int               `json:"page"`
	PageSize   int               `json:"page_size"`
	TotalPages int               `json:"total_pages"`
}

// LicenseStatsResponse 许可证统计响应
type LicenseStatsResponse struct {
	Total     int64 `json:"total"`
	Active    int64 `json:"active"`
	Expired   int64 `json:"expired"`
	Disabled  int64 `json:"disabled"`
	Suspended int64 `json:"suspended"`
	Expiring  int64 `json:"expiring"` // 30天内过期
}

// CreateLicense 创建许可证
func (h *LicenseHandler) CreateLicense(c *gin.Context) {
	// 检查权限
	userRole, exists := c.Get("user_role")
	if !exists || userRole != "admin" {
		ErrorResponse(c, http.StatusForbidden, "Insufficient permissions", "ADMIN_REQUIRED")
		return
	}

	var req CreateLicenseRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ErrorResponse(c, http.StatusBadRequest, "Invalid request format", err.Error())
		return
	}

	// 验证过期时间
	if req.ExpiresAt.Before(time.Now()) {
		ErrorResponse(c, http.StatusBadRequest, "Expiration date must be in the future", "INVALID_EXPIRY_DATE")
		return
	}

	// 生成许可证密钥
	licenseKey, err := generateLicenseKey(req.ProductName)
	if err != nil {
		ErrorResponse(c, http.StatusInternalServerError, "Failed to generate license key", err.Error())
		return
	}

	// 创建许可证
	license := model.License{
		LicenseKey:    licenseKey,
		CustomerName:  req.CustomerName,
		CustomerEmail: req.CustomerEmail,
		ProductName:   req.ProductName,
		MaxDevices:    req.MaxDevices,
		Features:      strings.Join(req.Features, ","),
		Status:        "active",
		ExpiresAt:     &req.ExpiresAt,
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	db := database.GetDB()
	if err := db.Create(&license).Error; err != nil {
		ErrorResponse(c, http.StatusInternalServerError, "Failed to create license", err.Error())
		return
	}

	// 构建响应
	response := buildLicenseResponse(&license)

	SuccessResponse(c, "License created successfully", response)
}

// GetLicenseList 获取许可证列表
func (h *LicenseHandler) GetLicenseList(c *gin.Context) {
	var req LicenseListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		ErrorResponse(c, http.StatusBadRequest, "Invalid query parameters", err.Error())
		return
	}

	db := database.GetDB()
	query := db.Model(&model.License{})

	// 应用筛选条件
	if req.Keyword != "" {
		keyword := "%" + req.Keyword + "%"
		query = query.Where("license_key LIKE ? OR customer_name LIKE ? OR customer_email LIKE ? OR product_name LIKE ?",
			keyword, keyword, keyword, keyword)
	}

	if req.Status != "" {
		query = query.Where("status = ?", req.Status)
	}

	if req.CustomerName != "" {
		query = query.Where("customer_name LIKE ?", "%"+req.CustomerName+"%")
	}

	// 计算总数
	var total int64
	query.Count(&total)

	// 应用排序
	orderClause := req.SortBy + " " + req.SortOrder
	query = query.Order(orderClause)

	// 应用分页
	offset := (req.Page - 1) * req.PageSize
	query = query.Offset(offset).Limit(req.PageSize)

	// 查询数据
	var licenses []model.License
	if err := query.Find(&licenses).Error; err != nil {
		ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch licenses", err.Error())
		return
	}

	// 构建响应
	licenseResponses := make([]LicenseResponse, len(licenses))
	for i, license := range licenses {
		licenseResponses[i] = buildLicenseResponse(&license)
	}

	totalPages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))

	response := LicenseListResponse{
		Licenses:   licenseResponses,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}

	SuccessResponse(c, "Licenses retrieved successfully", response)
}

// GetLicense 获取单个许可证
func (h *LicenseHandler) GetLicense(c *gin.Context) {
	idParam := c.Param("id")
	id, err := strconv.ParseUint(idParam, 10, 32)
	if err != nil {
		ErrorResponse(c, http.StatusBadRequest, "Invalid license ID", "INVALID_ID")
		return
	}

	db := database.GetDB()
	var license model.License
	if err := db.First(&license, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			ErrorResponse(c, http.StatusNotFound, "License not found", "LICENSE_NOT_FOUND")
			return
		}
		ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch license", err.Error())
		return
	}

	response := buildLicenseResponse(&license)
	SuccessResponse(c, "License retrieved successfully", response)
}

// UpdateLicense 更新许可证
func (h *LicenseHandler) UpdateLicense(c *gin.Context) {
	// 检查权限
	userRole, exists := c.Get("user_role")
	if !exists || userRole != "admin" {
		ErrorResponse(c, http.StatusForbidden, "Insufficient permissions", "ADMIN_REQUIRED")
		return
	}

	idParam := c.Param("id")
	id, err := strconv.ParseUint(idParam, 10, 32)
	if err != nil {
		ErrorResponse(c, http.StatusBadRequest, "Invalid license ID", "INVALID_ID")
		return
	}

	var req UpdateLicenseRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		ErrorResponse(c, http.StatusBadRequest, "Invalid request format", err.Error())
		return
	}

	db := database.GetDB()
	var license model.License
	if err := db.First(&license, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			ErrorResponse(c, http.StatusNotFound, "License not found", "LICENSE_NOT_FOUND")
			return
		}
		ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch license", err.Error())
		return
	}

	// 更新字段
	if req.CustomerName != "" {
		license.CustomerName = req.CustomerName
	}
	if req.CustomerEmail != "" {
		license.CustomerEmail = req.CustomerEmail
	}
	if req.ProductName != "" {
		license.ProductName = req.ProductName
	}
	if req.MaxDevices > 0 {
		license.MaxDevices = req.MaxDevices
	}
	if req.Features != nil {
		license.Features = strings.Join(req.Features, ",")
	}
	if !req.ExpiresAt.IsZero() {
		if req.ExpiresAt.Before(time.Now()) {
			ErrorResponse(c, http.StatusBadRequest, "Expiration date must be in the future", "INVALID_EXPIRY_DATE")
			return
		}
		license.ExpiresAt = &req.ExpiresAt
	}
	if req.Status != "" {
		license.Status = req.Status
	}

	license.UpdatedAt = time.Now()

	if err := db.Save(&license).Error; err != nil {
		ErrorResponse(c, http.StatusInternalServerError, "Failed to update license", err.Error())
		return
	}

	response := buildLicenseResponse(&license)
	SuccessResponse(c, "License updated successfully", response)
}

// DeleteLicense 删除许可证
func (h *LicenseHandler) DeleteLicense(c *gin.Context) {
	// 检查权限
	userRole, exists := c.Get("user_role")
	if !exists || userRole != "admin" {
		ErrorResponse(c, http.StatusForbidden, "Insufficient permissions", "ADMIN_REQUIRED")
		return
	}

	idParam := c.Param("id")
	id, err := strconv.ParseUint(idParam, 10, 32)
	if err != nil {
		ErrorResponse(c, http.StatusBadRequest, "Invalid license ID", "INVALID_ID")
		return
	}

	db := database.GetDB()
	var license model.License
	if err := db.First(&license, uint(id)).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			ErrorResponse(c, http.StatusNotFound, "License not found", "LICENSE_NOT_FOUND")
			return
		}
		ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch license", err.Error())
		return
	}

	// 软删除许可证
	if err := db.Delete(&license).Error; err != nil {
		ErrorResponse(c, http.StatusInternalServerError, "Failed to delete license", err.Error())
		return
	}

	SuccessResponse(c, "License deleted successfully", nil)
}

// GetLicenseStats 获取许可证统计
func (h *LicenseHandler) GetLicenseStats(c *gin.Context) {
	db := database.GetDB()

	var stats LicenseStatsResponse

	// 总数
	db.Model(&model.License{}).Count(&stats.Total)

	// 按状态统计
	db.Model(&model.License{}).Where("status = ?", "active").Count(&stats.Active)
	db.Model(&model.License{}).Where("status = ?", "expired").Count(&stats.Expired)
	db.Model(&model.License{}).Where("status = ?", "disabled").Count(&stats.Disabled)
	db.Model(&model.License{}).Where("status = ?", "suspended").Count(&stats.Suspended)

	// 30天内过期的许可证
	expiringDate := time.Now().AddDate(0, 0, 30)
	db.Model(&model.License{}).Where("status = ? AND expires_at <= ?", "active", expiringDate).Count(&stats.Expiring)

	SuccessResponse(c, "License stats retrieved successfully", stats)
}

// generateLicenseKey 生成许可证密钥
func generateLicenseKey(productName string) (string, error) {
	// 生成随机字节
	randomBytes := make([]byte, 16)
	if _, err := rand.Read(randomBytes); err != nil {
		return "", err
	}

	// 前缀取产品名的前三个字母（大写），不足用LIC
	prefix := "LIC"
	if len(productName) >= 3 {
		prefix = strings.ToUpper(productName[:3])
	}

	// 生成许可证密钥格式: PREFIX-XXXX-XXXX-XXXX-XXXX
	randomHex := hex.EncodeToString(randomBytes)
	licenseKey := fmt.Sprintf("%s-%s-%s-%s-%s",
		prefix,
		strings.ToUpper(randomHex[0:4]),
		strings.ToUpper(randomHex[4:8]),
		strings.ToUpper(randomHex[8:12]),
		strings.ToUpper(randomHex[12:16]))

	return licenseKey, nil
}

// buildLicenseResponse 构建许可证响应
func buildLicenseResponse(license *model.License) LicenseResponse {
	var features []string
	if license.Features != "" {
		features = strings.Split(license.Features, ",")
	}

	// 查询设备绑定数量（按 LicenseKey）
	db := database.GetDB()
	var deviceCount int64
	db.Model(&model.DeviceBinding{}).Where("license_key = ?", license.LicenseKey).Count(&deviceCount)

	// 查询最后使用时间（按 LicenseKey）
	var lastLog model.VerificationLog
	var lastUsedAt time.Time
	if err := db.Where("license_key = ?", license.LicenseKey).Order("created_at DESC").First(&lastLog).Error; err == nil {
		lastUsedAt = lastLog.CreatedAt
	}

	return LicenseResponse{
		ID:            license.ID,
		LicenseKey:    license.LicenseKey,
		CustomerName:  license.CustomerName,
		CustomerEmail: license.CustomerEmail,
		ProductName:   license.ProductName,
		MaxDevices:    license.MaxDevices,
		Features:      features,
		Status:        license.Status,
		CreatedAt:     license.CreatedAt,
		ExpiresAt:     license.ExpiresAt,
		DeviceCount:   int(deviceCount),
		LastUsedAt:    lastUsedAt,
	}
}
