<template>
  <div class="profile-page">
    <PageHeader title="个人资料" description="管理您的账户信息和安全设置" />
    
    <div class="profile-content">
      <a-row :gutter="24">
        <!-- 左侧：基本信息 -->
        <a-col :span="16">
          <a-card title="基本信息" class="profile-card">
            <a-form
              ref="profileFormRef"
              :model="profileForm"
              :rules="profileRules"
              layout="vertical"
              @submit="handleUpdateProfile"
            >
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item field="username" label="用户名">
                    <a-input
                      v-model="profileForm.username"
                      placeholder="请输入用户名"
                      disabled
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item field="email" label="邮箱地址">
                    <a-input
                      v-model="profileForm.email"
                      placeholder="请输入邮箱地址"
                    />
                  </a-form-item>
                </a-col>
              </a-row>

              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item field="role" label="用户角色">
                    <a-input
                      :value="getRoleText(profileForm.role)"
                      disabled
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item field="status" label="账户状态">
                    <StatusTag :status="profileForm.status" type="user" />
                  </a-form-item>
                </a-col>
              </a-row>

              <a-form-item>
                <a-button
                  type="primary"
                  html-type="submit"
                  :loading="updateLoading"
                >
                  更新资料
                </a-button>
              </a-form-item>
            </a-form>
          </a-card>

          <!-- 安全设置 -->
          <a-card title="安全设置" class="profile-card">
            <div class="security-item">
              <div class="security-info">
                <h4>登录密码</h4>
                <p>定期更新密码有助于保护账户安全</p>
              </div>
              <a-button @click="showChangePasswordModal">
                修改密码
              </a-button>
            </div>
          </a-card>
        </a-col>

        <!-- 右侧：账户统计 -->
        <a-col :span="8">
          <a-card title="账户统计" class="profile-card">
            <div class="stats-list">
              <div class="stat-item">
                <div class="stat-label">注册时间</div>
                <div class="stat-value">{{ formatDate(authStore.user?.created_at) }}</div>
              </div>
              <div class="stat-item">
                <div class="stat-label">最后登录</div>
                <div class="stat-value">{{ formatDate(new Date()) }}</div>
              </div>
              <div class="stat-item" v-if="authStore.isAdmin">
                <div class="stat-label">管理权限</div>
                <div class="stat-value">
                  <a-tag color="red">管理员</a-tag>
                </div>
              </div>
            </div>
          </a-card>

          <!-- 快捷操作 -->
          <a-card title="快捷操作" class="profile-card">
            <div class="quick-actions">
              <a-button
                type="outline"
                long
                @click="$router.push('/dashboard')"
              >
                <template #icon>
                  <icon-dashboard />
                </template>
                返回仪表板
              </a-button>
              
              <a-button
                type="outline"
                long
                @click="$router.push('/licenses')"
                v-if="authStore.isAdmin || authStore.isUser"
              >
                <template #icon>
                  <icon-key />
                </template>
                许可证管理
              </a-button>
              
              <a-button
                type="outline"
                long
                @click="handleLogout"
                status="danger"
              >
                <template #icon>
                  <icon-export />
                </template>
                退出登录
              </a-button>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <!-- 修改密码模态框 -->
    <ChangePasswordModal
      v-model:visible="changePasswordVisible"
      @success="handlePasswordChangeSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { Message } from '@arco-design/web-vue'
import type { FormInstance } from '@arco-design/web-vue'
import { useAuthStore } from '@/stores/auth'
import PageHeader from '@/components/common/PageHeader.vue'
import StatusTag from '@/components/common/StatusTag.vue'
import ChangePasswordModal from '@/components/auth/ChangePasswordModal.vue'
import dayjs from 'dayjs'

const router = useRouter()
const authStore = useAuthStore()
const profileFormRef = ref<FormInstance>()
const updateLoading = ref(false)
const changePasswordVisible = ref(false)

// 个人资料表单
const profileForm = reactive({
  username: '',
  email: '',
  role: '',
  status: ''
})

// 表单验证规则
const profileRules = {
  email: [
    { 
      type: 'email', 
      message: '请输入有效的邮箱地址' 
    }
  ]
}

// 获取角色文本
const getRoleText = (role: string) => {
  const roleMap = {
    admin: '管理员',
    user: '普通用户',
    api: 'API用户'
  }
  return roleMap[role] || role
}

// 格式化日期
const formatDate = (date: string | Date) => {
  if (!date) return '-'
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

// 更新个人资料
const handleUpdateProfile = async () => {
  try {
    const valid = await profileFormRef.value?.validate()
    if (!valid) return

    updateLoading.value = true
    
    // TODO: 调用更新个人资料API
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    Message.success('个人资料更新成功')
  } catch (error: any) {
    console.error('Update profile error:', error)
    Message.error(error.message || '更新失败')
  } finally {
    updateLoading.value = false
  }
}

// 显示修改密码模态框
const showChangePasswordModal = () => {
  changePasswordVisible.value = true
}

// 密码修改成功处理
const handlePasswordChangeSuccess = () => {
  Message.success('密码修改成功')
  changePasswordVisible.value = false
}

// 退出登录
const handleLogout = async () => {
  try {
    await authStore.logout()
    Message.success('已退出登录')
    router.push('/login')
  } catch (error) {
    console.error('Logout error:', error)
    Message.error('退出登录失败')
  }
}

// 初始化数据
const initializeData = () => {
  if (authStore.user) {
    profileForm.username = authStore.user.username
    profileForm.email = authStore.user.email
    profileForm.role = authStore.user.role
    profileForm.status = authStore.user.status
  }
}

onMounted(() => {
  initializeData()
})
</script>

<style scoped>
.profile-page {
  padding: 24px;
}

.profile-content {
  margin-top: 24px;
}

.profile-card {
  margin-bottom: 24px;
}

.security-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0;
}

.security-info h4 {
  margin: 0 0 4px 0;
  font-size: 16px;
  font-weight: 500;
}

.security-info p {
  margin: 0;
  color: #8c8c8c;
  font-size: 14px;
}

.stats-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.stat-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;
}

.stat-item:last-child {
  border-bottom: none;
}

.stat-label {
  color: #8c8c8c;
  font-size: 14px;
}

.stat-value {
  font-weight: 500;
  color: #262626;
}

.quick-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}
</style>