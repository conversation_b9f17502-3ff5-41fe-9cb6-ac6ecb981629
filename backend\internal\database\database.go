package database

import (
	"fmt"
	"log"
	"os"
	"path/filepath"
	"time"

	"software-auth/internal/auth"
	"software-auth/internal/config"
	"software-auth/internal/model"

	"gorm.io/driver/sqlite"
	"gorm.io/gorm"
	"gorm.io/gorm/logger"
)

// DB 全局数据库实例
var DB *gorm.DB

// Initialize 初始化数据库连接
func Initialize(cfg *config.DatabaseConfig) error {
	// 确保数据目录存在
	dataDir := filepath.Dir(cfg.DSN)
	if err := os.MkdirAll(dataDir, 0755); err != nil {
		return fmt.Errorf("failed to create data directory: %v", err)
	}

	// 配置GORM日志
	logLevel := logger.Info
	if cfg.Driver == "sqlite" {
		logLevel = logger.Warn // SQLite减少日志输出
	}

	// 连接数据库
	db, err := gorm.Open(sqlite.Open(cfg.DSN), &gorm.Config{
		Logger: logger.Default.LogMode(logLevel),
		NowFunc: func() time.Time {
			return time.Now().Local()
		},
	})
	if err != nil {
		return fmt.Errorf("failed to connect to database: %v", err)
	}

	// 获取底层sql.DB对象进行连接池配置
	sqlDB, err := db.DB()
	if err != nil {
		return fmt.Errorf("failed to get underlying sql.DB: %v", err)
	}

	// 设置连接池参数
	sqlDB.SetMaxOpenConns(cfg.MaxOpenConns)
	sqlDB.SetMaxIdleConns(cfg.MaxIdleConns)
	sqlDB.SetConnMaxLifetime(cfg.ConnMaxLifetime)

	// 测试连接
	if err := sqlDB.Ping(); err != nil {
		return fmt.Errorf("failed to ping database: %v", err)
	}

	DB = db
	log.Println("Database connected successfully")

	// 运行数据库迁移
	if err := RunMigrations(); err != nil {
		return fmt.Errorf("failed to run migrations: %v", err)
	}

	// 创建初始数据
	if err := createInitialData(); err != nil {
		return fmt.Errorf("failed to create initial data: %v", err)
	}

	return nil
}

// createInitialData 创建初始数据
func createInitialData() error {
	log.Println("Creating initial data...")

	// 检查是否已存在管理员用户
	var count int64
	DB.Model(&model.User{}).Where("role = ?", model.UserRoleAdmin).Count(&count)

	if count == 0 {
		// 创建默认管理员用户
		passwordService := auth.NewPasswordService()
		hashedPassword, err := passwordService.HashPassword("AdminPass123!")
		if err != nil {
			return fmt.Errorf("failed to hash admin password: %v", err)
		}

		admin := &model.User{
			Username: "admin",
			Password: hashedPassword,
			Email:    "<EMAIL>",
			Role:     model.UserRoleAdmin,
			Status:   model.UserStatusActive,
		}

		if err := DB.Create(admin).Error; err != nil {
			return fmt.Errorf("failed to create admin user: %v", err)
		}

		log.Printf("Default admin user created (username: admin, password: AdminPass123!)")
	}

	log.Println("Initial data creation completed")
	return nil
}

// Close 关闭数据库连接
func Close() error {
	if DB != nil {
		sqlDB, err := DB.DB()
		if err != nil {
			return err
		}
		return sqlDB.Close()
	}
	return nil
}

// GetDB 获取数据库实例
func GetDB() *gorm.DB {
	return DB
}

// Transaction 执行事务
func Transaction(fn func(*gorm.DB) error) error {
	return DB.Transaction(fn)
}
