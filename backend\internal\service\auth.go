package service

import (
	"context"
	"errors"
	"fmt"
	"time"

	"software-auth/internal/auth"
	"software-auth/internal/cache"
	"software-auth/internal/config"
	"software-auth/internal/database"
	"software-auth/internal/model"
)

// AuthService 认证服务接口
type AuthService interface {
	Login(ctx context.Context, username, password string) (*LoginResponse, error)
	Logout(ctx context.Context, tokenID string) error
	RefreshToken(ctx context.Context, refreshToken string) (*auth.TokenPair, error)
	GetProfile(ctx context.Context, userID uint) (*model.User, error)
	ChangePassword(ctx context.Context, userID uint, oldPassword, newPassword string) error
}

// authService 认证服务实现
type authService struct {
	jwtService      auth.JWTService
	passwordService auth.PasswordService
	cacheManager    *cache.Manager
	config          *config.JWTConfig
}

// LoginResponse 登录响应
type LoginResponse struct {
	User   *UserInfo        `json:"user"`
	Tokens *auth.TokenPair  `json:"tokens"`
}

// UserInfo 用户信息
type UserInfo struct {
	ID       uint   `json:"id"`
	Username string `json:"username"`
	Email    string `json:"email"`
	Role     string `json:"role"`
	Status   string `json:"status"`
}

// 错误定义
var (
	ErrInvalidCredentials = errors.New("invalid username or password")
	ErrUserInactive       = errors.New("user account is inactive")
	ErrUserBlocked        = errors.New("user account is blocked")
)

// NewAuthService 创建认证服务
func NewAuthService(config *config.JWTConfig) AuthService {
	return &authService{
		jwtService:      auth.NewJWTService(config),
		passwordService: auth.NewPasswordService(),
		cacheManager:    cache.NewManager(),
		config:          config,
	}
}

// Login 用户登录
func (s *authService) Login(ctx context.Context, username, password string) (*LoginResponse, error) {
	// 查找用户
	var user model.User
	db := database.GetDB()
	if err := db.Where("username = ?", username).First(&user).Error; err != nil {
		return nil, ErrInvalidCredentials
	}

	// 验证密码
	if err := s.passwordService.VerifyPassword(user.Password, password); err != nil {
		return nil, ErrInvalidCredentials
	}

	// 检查用户状态
	if !user.IsActive() {
		switch user.Status {
		case model.UserStatusInactive:
			return nil, ErrUserInactive
		case model.UserStatusBlocked:
			return nil, ErrUserBlocked
		default:
			return nil, ErrUserInactive
		}
	}

	// 生成令牌
	tokens, err := s.jwtService.GenerateToken(&user)
	if err != nil {
		return nil, fmt.Errorf("failed to generate tokens: %v", err)
	}

	// 缓存令牌信息
	tokenData := &cache.AuthTokenData{
		UserID:   user.ID,
		Username: user.Username,
		Role:     user.Role,
		IssuedAt: time.Now().Unix(),
	}

	// 从tokens中提取tokenID（这里需要解析JWT来获取tokenID）
	claims, err := s.jwtService.ValidateToken(tokens.AccessToken)
	if err != nil {
		return nil, fmt.Errorf("failed to validate generated token: %v", err)
	}

	if err := s.cacheManager.SetAuthToken(ctx, claims.TokenID, tokenData); err != nil {
		// 缓存失败不影响登录，只记录错误
		fmt.Printf("Failed to cache auth token: %v\n", err)
	}

	// 缓存用户会话
	sessionData := &cache.UserSessionData{
		UserID:    user.ID,
		Username:  user.Username,
		Role:      user.Role,
		LastLogin: time.Now(),
		IPAddress: "", // 这里可以从context中获取IP地址
	}

	if err := s.cacheManager.SetUserSession(ctx, user.ID, sessionData); err != nil {
		fmt.Printf("Failed to cache user session: %v\n", err)
	}

	// 构造响应
	userInfo := &UserInfo{
		ID:       user.ID,
		Username: user.Username,
		Email:    user.Email,
		Role:     user.Role,
		Status:   user.Status,
	}

	return &LoginResponse{
		User:   userInfo,
		Tokens: tokens,
	}, nil
}

// Logout 用户登出
func (s *authService) Logout(ctx context.Context, tokenID string) error {
	// 从缓存中删除令牌
	if err := s.cacheManager.DeleteAuthToken(ctx, tokenID); err != nil {
		fmt.Printf("Failed to delete auth token from cache: %v\n", err)
	}

	// 撤销令牌（添加到黑名单）
	if err := s.jwtService.RevokeToken(tokenID); err != nil {
		return fmt.Errorf("failed to revoke token: %v", err)
	}

	return nil
}

// RefreshToken 刷新令牌
func (s *authService) RefreshToken(ctx context.Context, refreshToken string) (*auth.TokenPair, error) {
	// 验证刷新令牌
	tokens, err := s.jwtService.RefreshToken(refreshToken)
	if err != nil {
		return nil, err
	}

	// 解析新的访问令牌获取claims
	claims, err := s.jwtService.ValidateToken(tokens.AccessToken)
	if err != nil {
		return nil, fmt.Errorf("failed to validate refreshed token: %v", err)
	}

	// 更新缓存中的令牌信息
	tokenData := &cache.AuthTokenData{
		UserID:   claims.UserID,
		Username: claims.Username,
		Role:     claims.Role,
		IssuedAt: time.Now().Unix(),
	}

	if err := s.cacheManager.SetAuthToken(ctx, claims.TokenID, tokenData); err != nil {
		fmt.Printf("Failed to cache refreshed auth token: %v\n", err)
	}

	return tokens, nil
}

// GetProfile 获取用户资料
func (s *authService) GetProfile(ctx context.Context, userID uint) (*model.User, error) {
	var user model.User
	db := database.GetDB()
	
	if err := db.Where("id = ?", userID).First(&user).Error; err != nil {
		return nil, fmt.Errorf("user not found: %v", err)
	}

	// 清除敏感信息
	user.Password = ""

	return &user, nil
}

// ChangePassword 修改密码
func (s *authService) ChangePassword(ctx context.Context, userID uint, oldPassword, newPassword string) error {
	// 获取用户
	var user model.User
	db := database.GetDB()
	
	if err := db.Where("id = ?", userID).First(&user).Error; err != nil {
		return fmt.Errorf("user not found: %v", err)
	}

	// 验证旧密码
	if err := s.passwordService.VerifyPassword(user.Password, oldPassword); err != nil {
		return ErrInvalidCredentials
	}

	// 加密新密码
	hashedPassword, err := s.passwordService.HashPassword(newPassword)
	if err != nil {
		return fmt.Errorf("failed to hash new password: %v", err)
	}

	// 更新密码
	if err := db.Model(&user).Update("password", hashedPassword).Error; err != nil {
		return fmt.Errorf("failed to update password: %v", err)
	}

	// 清除用户的所有会话（强制重新登录）
	if err := s.cacheManager.DeleteUserSession(ctx, userID); err != nil {
		fmt.Printf("Failed to clear user session: %v\n", err)
	}

	return nil
}