<template>
  <div class="not-found-page">
    <div class="not-found-content">
      <div class="error-illustration">
        <div class="error-code">404</div>
        <div class="error-icon">
          <icon-exclamation-circle />
        </div>
      </div>
      
      <div class="error-info">
        <h1 class="error-title">页面不存在</h1>
        <p class="error-description">
          抱歉，您访问的页面不存在或已被移除。
        </p>
        
        <div class="error-actions">
          <a-button type="primary" size="large" @click="goHome">
            <template #icon>
              <icon-home />
            </template>
            返回首页
          </a-button>
          
          <a-button size="large" @click="goBack">
            <template #icon>
              <icon-left />
            </template>
            返回上页
          </a-button>
        </div>
      </div>
    </div>
    
    <!-- 建议链接 -->
    <div class="suggestions">
      <h3>您可能想要访问：</h3>
      <div class="suggestion-links">
        <a-button type="text" @click="$router.push('/dashboard')">
          <icon-dashboard />
          仪表板
        </a-button>
        <a-button type="text" @click="$router.push('/licenses')">
          <icon-key />
          许可证管理
        </a-button>
        <a-button type="text" @click="$router.push('/profile')" v-if="authStore.isAuthenticated">
          <icon-user />
          个人资料
        </a-button>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth'

const router = useRouter()
const authStore = useAuthStore()

// 返回首页
const goHome = () => {
  if (authStore.isAuthenticated) {
    router.push('/dashboard')
  } else {
    router.push('/login')
  }
}

// 返回上一页
const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1)
  } else {
    goHome()
  }
}
</script>

<style scoped>
.not-found-page {
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  background: #f5f5f5;
}

.not-found-content {
  text-align: center;
  max-width: 500px;
}

.error-illustration {
  position: relative;
  margin-bottom: 40px;
}

.error-code {
  font-size: 120px;
  font-weight: 700;
  color: #1890ff;
  line-height: 1;
  margin-bottom: 20px;
  text-shadow: 0 4px 8px rgba(24, 144, 255, 0.2);
}

.error-icon {
  font-size: 48px;
  color: #ff7875;
  margin-bottom: 20px;
}

.error-info {
  margin-bottom: 40px;
}

.error-title {
  font-size: 32px;
  font-weight: 600;
  color: #262626;
  margin: 0 0 16px 0;
}

.error-description {
  font-size: 16px;
  color: #8c8c8c;
  line-height: 1.6;
  margin: 0 0 32px 0;
}

.error-actions {
  display: flex;
  gap: 16px;
  justify-content: center;
  flex-wrap: wrap;
}

.suggestions {
  margin-top: 60px;
  text-align: center;
}

.suggestions h3 {
  font-size: 18px;
  color: #595959;
  margin: 0 0 20px 0;
}

.suggestion-links {
  display: flex;
  gap: 24px;
  justify-content: center;
  flex-wrap: wrap;
}

.suggestion-links .arco-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: #1890ff;
}

.suggestion-links .arco-btn:hover {
  background: rgba(24, 144, 255, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .error-code {
    font-size: 80px;
  }
  
  .error-title {
    font-size: 24px;
  }
  
  .error-actions {
    flex-direction: column;
    align-items: center;
  }
  
  .error-actions .arco-btn {
    width: 200px;
  }
  
  .suggestion-links {
    flex-direction: column;
    align-items: center;
  }
}

/* 深色主题适配 */
.not-found-page[data-theme="dark"] {
  background: #141414;
}

.not-found-page[data-theme="dark"] .error-title {
  color: #fff;
}

.not-found-page[data-theme="dark"] .error-description {
  color: #bfbfbf;
}

.not-found-page[data-theme="dark"] .suggestions h3 {
  color: #bfbfbf;
}
</style>