package server

import (
	"context"
	"fmt"
	"net/http"
	"os"
	"os/signal"
	"strconv"
	"syscall"
	"time"

	"software-auth/internal/api"
	"software-auth/internal/cache"
	"software-auth/internal/config"
	"software-auth/internal/database"
	"software-auth/internal/middleware"
	"software-auth/internal/model"

	"github.com/gin-gonic/gin"
)

// Server HTTP服务器
type Server struct {
	config *config.Config
	router *gin.Engine
	server *http.Server
}

// New 创建新的服务器实例
func New(cfg *config.Config) *Server {
	// 设置Gin模式
	gin.SetMode(cfg.Server.Mode)

	return &Server{
		config: cfg,
		router: gin.New(),
	}
}

// Initialize 初始化服务器
func (s *Server) Initialize() error {
	// 初始化数据库
	if err := database.Initialize(&s.config.Database); err != nil {
		return fmt.Errorf("failed to initialize database: %v", err)
	}

	// 初始化Redis缓存（可选）
	if err := cache.Initialize(&s.config.Redis); err != nil {
		fmt.Printf("⚠️  Warning: Redis initialization failed: %v\n", err)
		fmt.Println("📝 To enable Redis caching:")
		fmt.Println("   1. Install Redis: https://github.com/microsoftarchive/redis/releases")
		fmt.Println("   2. Start Redis server: redis-server")
		fmt.Println("   3. Or run: .\\scripts\\setup-redis.bat")
		fmt.Println("✅ System will continue without Redis caching (reduced performance)")
	} else {
		fmt.Println("✅ Redis connected successfully")

		// 预热缓存
		if err := cache.WarmupCache(); err != nil {
			fmt.Printf("Warning: Cache warmup failed: %v\n", err)
		}
	}

	return nil
}

// setupRoutes 设置路由
func (s *Server) setupRoutes() {
	// 基础中间件
	s.router.Use(gin.Logger())
	s.router.Use(gin.Recovery())

	// 安全中间件
	s.router.Use(middleware.SecurityHeadersMiddleware())
	s.router.Use(middleware.CORSMiddleware())
	s.router.Use(middleware.RateLimitMiddleware())
	s.router.Use(middleware.IPFilterMiddleware())

	// 审计日志中间件
	s.router.Use(middleware.AuditMiddleware())

	// 健康检查（不需要认证）
	s.router.GET("/health", s.healthCheck)

	// API路由组
	v1 := s.router.Group("/api/v1")

	// 创建处理器
	authHandler := api.NewAuthHandler(&s.config.JWT)
	licenseHandler := api.NewLicenseHandler()
	verifyHandler := api.NewVerifyHandler()
	userHandler := api.NewUserHandler()
	offlineHandler := api.NewOfflineHandler()
	securityHandler := api.NewSecurityHandler()
	auditHandler := api.NewAuditHandler()

	{
		// 认证路由（公开）
		auth := v1.Group("/auth")
		{
			auth.POST("/login", authHandler.Login)
			auth.POST("/refresh", authHandler.RefreshToken)
			// 可选：token校验端点（后续实现）
			// auth.POST("/validate", authHandler.ValidateToken)
		}

		// 需要认证的路由
		authenticated := v1.Group("")
		authenticated.Use(middleware.AuthMiddleware(&s.config.JWT))
		{
			// 认证相关
			authenticated.POST("/auth/logout", authHandler.Logout)
			authenticated.GET("/auth/profile", authHandler.GetProfile)
			authenticated.GET("/auth/me", authHandler.GetCurrentUser)
			authenticated.POST("/auth/change-password", authHandler.ChangePassword)
			// authenticated.PUT("/auth/profile", authHandler.UpdateProfile)
			// authenticated.GET("/auth/sessions", authHandler.GetUserSessions)
			// authenticated.GET("/auth/stats", authHandler.GetUserStats)

			// 用户管理（仅管理员）
			userRoutes := authenticated.Group("/users")
			{
				userRoutes.GET("", userHandler.GetUserList)
				userRoutes.POST("", userHandler.CreateUser)
				userRoutes.GET("/stats", userHandler.GetUserStats)
				userRoutes.GET("/:id", userHandler.GetUser)
				userRoutes.PUT("/:id", userHandler.UpdateUser)
				userRoutes.DELETE("/:id", userHandler.DeleteUser)
				userRoutes.POST("/:id/change-password", userHandler.ChangeUserPassword)
			}

			// 许可证管理
			licenseRoutes := authenticated.Group("/licenses")
			{
				licenseRoutes.GET("", licenseHandler.GetLicenseList)
				licenseRoutes.POST("", licenseHandler.CreateLicense)
				licenseRoutes.GET("/stats", licenseHandler.GetLicenseStats)
				licenseRoutes.GET("/:id", licenseHandler.GetLicense)
				licenseRoutes.PUT("/:id", licenseHandler.UpdateLicense)
				licenseRoutes.DELETE("/:id", licenseHandler.DeleteLicense)
			}

			// 安全管理（仅管理员）
			securityRoutes := authenticated.Group("/security")
			{
				securityRoutes.GET("/stats", securityHandler.GetSecurityStats)
				securityRoutes.GET("/whitelist", securityHandler.GetIPWhitelist)
				securityRoutes.POST("/whitelist", securityHandler.AddIPToWhitelist)
				securityRoutes.DELETE("/whitelist/:ip", securityHandler.RemoveIPFromWhitelist)
				securityRoutes.GET("/blacklist", securityHandler.GetIPBlacklist)
				securityRoutes.POST("/blacklist", securityHandler.AddIPToBlacklist)
				securityRoutes.DELETE("/blacklist/:ip", securityHandler.RemoveIPFromBlacklist)
				securityRoutes.GET("/api-keys", securityHandler.GetAPIKeys)
				securityRoutes.POST("/api-keys", securityHandler.CreateAPIKey)
				securityRoutes.DELETE("/api-keys/:key", securityHandler.RevokeAPIKey)
				securityRoutes.PUT("/rate-limit", securityHandler.UpdateRateLimit)
			}

			// 审计日志管理（仅管理员）
			auditRoutes := authenticated.Group("/audit")
			{
				auditRoutes.GET("/logs", auditHandler.GetAuditLogs)
				auditRoutes.GET("/logs/:id", auditHandler.GetAuditLog)
				auditRoutes.GET("/stats", auditHandler.GetAuditStats)
				auditRoutes.GET("/export", auditHandler.ExportAuditLogs)
			}

			// 仪表板路由（需要认证）
			dashboardRoutes := authenticated.Group("/dashboard")
			{
				dashboardRoutes.GET("/overview", s.getDashboardOverview)
				dashboardRoutes.GET("/stats", s.getDashboardStats)
			}
		}

		// 验证路由（公开，供客户端软件调用，需要API密钥）
		verify := v1.Group("/verify")
		verify.Use(middleware.APIKeyMiddleware())
		{
			verify.POST("/license", verifyHandler.VerifyLicense)
			verify.POST("/bind", verifyHandler.BindDevice)
			verify.POST("/unbind", verifyHandler.UnbindDevice)
			verify.GET("/devices", verifyHandler.GetDeviceList)
		}

		// 离线验证路由（公开，供客户端软件调用）
		offline := v1.Group("/offline")
		{
			// 生成令牌需要API密钥
			offline.POST("/generate-token", middleware.APIKeyMiddleware(), offlineHandler.GenerateOfflineToken)
			// 验证令牌不需要API密钥（离线使用）
			offline.POST("/verify", offlineHandler.VerifyOfflineToken)
			// 同步数据需要API密钥
			offline.POST("/sync", middleware.APIKeyMiddleware(), offlineHandler.SyncOfflineData)
			// 获取令牌信息不需要API密钥
			offline.GET("/token-info", offlineHandler.GetOfflineTokenInfo)
		}

		// 缓存测试路由（开发环境）
		if s.config.Server.Mode == "debug" {
			cacheGroup := v1.Group("/cache")
			{
				cacheGroup.GET("/info", s.getCacheInfo)
				cacheGroup.GET("/health", s.getCacheHealth)
				cacheGroup.GET("/metrics", s.getCacheMetrics)
				cacheGroup.POST("/test", s.testCache)
				cacheGroup.POST("/warmup", s.warmupCache)
				cacheGroup.DELETE("/clear", s.clearCache)
				cacheGroup.DELETE("/cleanup/:pattern", s.cleanupCache)
			}
		}
	}
}

// Run 启动服务器
func (s *Server) Run() error {
	// 初始化服务器
	if err := s.Initialize(); err != nil {
		return err
	}

	// 设置路由
	s.setupRoutes()

	// 创建HTTP服务器（HTTP）
	s.server = &http.Server{
		Addr:         fmt.Sprintf(":%d", s.config.Server.Port),
		Handler:      s.router,
		ReadTimeout:  s.config.Server.ReadTimeout,
		WriteTimeout: s.config.Server.WriteTimeout,
	}

	// 启动HTTP服务器
	go func() {
		fmt.Printf("Server starting on http://localhost:%d\n", s.config.Server.Port)
		if err := s.server.ListenAndServe(); err != nil && err != http.ErrServerClosed {
			fmt.Printf("HTTP server failed to start: %v\n", err)
		}
	}()

	// 如启用TLS，额外启动HTTPS服务器（开发/内网用自签证书）
	if s.config.Server.TLS.Enabled {
		httpsPort := s.config.Server.TLS.Port
		if httpsPort == 0 {
			httpsPort = 8443
		}

		go func() {
			addr := fmt.Sprintf(":%d", httpsPort)
			fmt.Printf("Server starting on https://localhost:%d (TLS)\n", httpsPort)
			if err := s.router.RunTLS(addr, s.config.Server.TLS.CertFile, s.config.Server.TLS.KeyFile); err != nil && err != http.ErrServerClosed {
				fmt.Printf("HTTPS server failed to start: %v\n", err)
			}
		}()
	}

	// 等待中断信号
	quit := make(chan os.Signal, 1)
	signal.Notify(quit, syscall.SIGINT, syscall.SIGTERM)
	<-quit

	fmt.Println("Server shutting down...")

	// 关闭数据库连接
	if err := database.Close(); err != nil {
		fmt.Printf("Error closing database: %v\n", err)
	}

	// 关闭Redis连接
	if err := cache.Close(); err != nil {
		fmt.Printf("Error closing Redis: %v\n", err)
	}

	// 优雅关闭
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	if err := s.server.Shutdown(ctx); err != nil {
		return fmt.Errorf("server forced to shutdown: %v", err)
	}

	fmt.Println("Server exited")
	return nil
}

// healthCheck 健康检查处理器
func (s *Server) healthCheck(c *gin.Context) {
	status := "ok"
	checks := make(map[string]interface{})

	// 检查数据库连接
	if db := database.GetDB(); db != nil {
		sqlDB, err := db.DB()
		if err != nil || sqlDB.Ping() != nil {
			status = "error"
			checks["database"] = "disconnected"
		} else {
			checks["database"] = "connected"
		}
	} else {
		status = "error"
		checks["database"] = "not_initialized"
	}

	// 检查Redis连接
	cacheHealth := cache.CheckHealth()
	if cacheHealth.Available {
		checks["redis"] = map[string]interface{}{
			"status":           "connected",
			"response_time_ms": cacheHealth.ResponseTime.Milliseconds(),
		}
	} else {
		checks["redis"] = map[string]interface{}{
			"status": "disconnected",
			"error":  cacheHealth.Error,
		}
		// Redis不可用不影响整体健康状态，只是性能会降低
		// status = "degraded" // 可以考虑添加降级状态
	}

	response := gin.H{
		"status":    status,
		"timestamp": time.Now().Unix(),
		"version":   "1.0.0",
		"checks":    checks,
	}

	if status == "ok" {
		c.JSON(http.StatusOK, response)
	} else {
		c.JSON(http.StatusServiceUnavailable, response)
	}
}

// getCacheInfo 获取缓存信息
func (s *Server) getCacheInfo(c *gin.Context) {
	manager := cache.NewManager()
	info, err := manager.GetCacheInfo(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get cache info",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data":   info,
	})
}

// testCache 测试缓存功能
func (s *Server) testCache(c *gin.Context) {
	var request struct {
		Key   string      `json:"key" binding:"required"`
		Value interface{} `json:"value" binding:"required"`
		TTL   int         `json:"ttl"` // 秒
	}

	if err := c.ShouldBindJSON(&request); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"error":   "Invalid request",
			"details": err.Error(),
		})
		return
	}

	service := cache.NewCacheService()

	// 设置缓存
	ttl := time.Duration(request.TTL) * time.Second
	if ttl == 0 {
		ttl = 5 * time.Minute // 默认5分钟
	}

	err := service.SetJSON(c.Request.Context(), request.Key, request.Value, ttl)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to set cache",
			"details": err.Error(),
		})
		return
	}

	// 读取缓存验证
	var result interface{}
	err = service.GetJSON(c.Request.Context(), request.Key, &result)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to get cache",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Cache test successful",
		"data": gin.H{
			"key":         request.Key,
			"value":       result,
			"ttl_seconds": int(ttl.Seconds()),
		},
	})
}

// clearCache 清空缓存
func (s *Server) clearCache(c *gin.Context) {
	if !cache.IsHealthy() {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error":   "Cache not available",
			"message": "Redis is not connected",
		})
		return
	}

	service := cache.NewCacheService()

	err := service.FlushDB(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to clear cache",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Cache cleared successfully",
	})
}

// getCacheHealth 获取缓存健康状态
func (s *Server) getCacheHealth(c *gin.Context) {
	health := cache.CheckHealth()

	status := http.StatusOK
	if !health.Available {
		status = http.StatusServiceUnavailable
	}

	c.JSON(status, gin.H{
		"status": "success",
		"data":   health,
	})
}

// getCacheMetrics 获取缓存指标
func (s *Server) getCacheMetrics(c *gin.Context) {
	metrics := cache.GetCacheMetrics()

	c.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data":   metrics,
	})
}

// warmupCache 预热缓存
func (s *Server) warmupCache(c *gin.Context) {
	if !cache.IsHealthy() {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error":   "Cache not available",
			"message": "Redis is not connected",
		})
		return
	}

	err := cache.WarmupCache()
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to warmup cache",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": "Cache warmed up successfully",
	})
}

// cleanupCache 清理缓存
func (s *Server) cleanupCache(c *gin.Context) {
	if !cache.IsHealthy() {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"error":   "Cache not available",
			"message": "Redis is not connected",
		})
		return
	}

	pattern := c.Param("pattern")
	if pattern == "" {
		c.JSON(http.StatusBadRequest, gin.H{
			"error": "Pattern is required",
		})
		return
	}

	deleted, err := cache.CleanupExpiredKeys(pattern)
	if err != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"error":   "Failed to cleanup cache",
			"details": err.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"status":  "success",
		"message": fmt.Sprintf("Cleaned up %d keys matching pattern: %s", deleted, pattern),
		"data": gin.H{
			"pattern":       pattern,
			"deleted_count": deleted,
		},
	})
}

// getDashboardOverview 获取仪表板概览数据
func (s *Server) getDashboardOverview(c *gin.Context) {
	db := database.GetDB()

	// 基础统计
	var userCount, licenseCount, activeDeviceCount int64
	db.Model(&model.User{}).Count(&userCount)
	db.Model(&model.License{}).Count(&licenseCount)
	db.Model(&model.DeviceBinding{}).Where("last_seen_at > ?", time.Now().AddDate(0, 0, -30)).Count(&activeDeviceCount)

	// 许可证状态统计
	var activeLicenses, expiredLicenses, suspendedLicenses int64
	db.Model(&model.License{}).Where("status = ?", "active").Count(&activeLicenses)
	db.Model(&model.License{}).Where("status = ?", "expired").Count(&expiredLicenses)
	db.Model(&model.License{}).Where("status = ?", "suspended").Count(&suspendedLicenses)

	// 近期活动统计（最近7天）
	sevenDaysAgo := time.Now().AddDate(0, 0, -7)
	var recentVerifications, recentLogins int64
	db.Model(&model.VerificationLog{}).Where("created_at > ?", sevenDaysAgo).Count(&recentVerifications)
	db.Model(&model.AuditLog{}).Where("action = ? AND created_at > ?", "LOGIN", sevenDaysAgo).Count(&recentLogins)

	// 即将过期的许可证（30天内）
	thirtyDaysLater := time.Now().AddDate(0, 0, 30)
	var expiringLicenses int64
	db.Model(&model.License{}).Where("status = ? AND expires_at <= ?", "active", thirtyDaysLater).Count(&expiringLicenses)

	c.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data": gin.H{
			"overview": gin.H{
				"total_users":    userCount,
				"total_licenses": licenseCount,
				"active_devices": activeDeviceCount,
				"expiring_soon":  expiringLicenses,
			},
			"license_status": gin.H{
				"active":    activeLicenses,
				"expired":   expiredLicenses,
				"suspended": suspendedLicenses,
			},
			"recent_activity": gin.H{
				"verifications": recentVerifications,
				"logins":        recentLogins,
			},
		},
	})
}

// getDashboardStats 获取仪表板统计数据
func (s *Server) getDashboardStats(c *gin.Context) {
	db := database.GetDB()

	// 获取时间范围参数
	days := c.DefaultQuery("days", "30")
	daysInt, err := strconv.Atoi(days)
	if err != nil || daysInt <= 0 {
		daysInt = 30
	}

	startDate := time.Now().AddDate(0, 0, -daysInt)

	// 每日验证统计
	var dailyVerifications []struct {
		Date  string `json:"date"`
		Count int64  `json:"count"`
	}
	db.Raw(`
		SELECT DATE(created_at) as date, COUNT(*) as count
		FROM verification_logs
		WHERE created_at >= ?
		GROUP BY DATE(created_at)
		ORDER BY date
	`, startDate).Find(&dailyVerifications)

	// 每日登录统计
	var dailyLogins []struct {
		Date  string `json:"date"`
		Count int64  `json:"count"`
	}
	db.Raw(`
		SELECT DATE(created_at) as date, COUNT(*) as count
		FROM audit_logs
		WHERE action = 'LOGIN' AND created_at >= ?
		GROUP BY DATE(created_at)
		ORDER BY date
	`, startDate).Find(&dailyLogins)

	// 产品使用统计
	var productStats []struct {
		ProductName string `json:"product_name"`
		Count       int64  `json:"count"`
	}
	db.Raw(`
		SELECT product_name, COUNT(*) as count
		FROM licenses
		WHERE created_at >= ?
		GROUP BY product_name
		ORDER BY count DESC
		LIMIT 10
	`, startDate).Find(&productStats)

	c.JSON(http.StatusOK, gin.H{
		"status": "success",
		"data": gin.H{
			"daily_verifications": dailyVerifications,
			"daily_logins":        dailyLogins,
			"product_stats":       productStats,
			"period_days":         daysInt,
		},
	})
}
