package main

import (
	"bytes"
	"encoding/json"
	"fmt"
	"io"
	"log"
	"net/http"
	"time"
)

const baseURL = "http://localhost:8080/api/v1"

type GenerateOfflineTokenRequest struct {
	LicenseKey string `json:"license_key"`
	DeviceID   string `json:"device_id"`
	ValidDays  int    `json:"valid_days"`
}

type VerifyOfflineTokenRequest struct {
	Token       string `json:"token"`
	DeviceID    string `json:"device_id"`
	ProductName string `json:"product_name"`
}

type SyncOfflineDataRequest struct {
	DeviceID       string                `json:"device_id"`
	OfflineRecords []OfflineUsageRecord  `json:"offline_records"`
}

type OfflineUsageRecord struct {
	LicenseKey string    `json:"license_key"`
	DeviceID   string    `json:"device_id"`
	UsedAt     time.Time `json:"used_at"`
	Features   []string  `json:"features"`
	Duration   int       `json:"duration"`
}

func main() {
	fmt.Println("Offline Verification API Test Tool")
	fmt.Println("==================================")

	// 测试用的许可证密钥（需要先创建一个许可证并绑定设备）
	licenseKey := "PRO-1234-5678-9ABC-DEF0" // 这个需要替换为实际的许可证密钥
	deviceID := "TEST-DEVICE-001"
	productName := "Test Software"

	fmt.Printf("Testing with License Key: %s\n", licenseKey)
	fmt.Printf("Device ID: %s\n", deviceID)

	// 1. 测试生成离线令牌
	fmt.Println("\n1. Testing Generate Offline Token...")
	offlineToken, err := testGenerateOfflineToken(licenseKey, deviceID, 30)
	if err != nil {
		log.Printf("❌ Generate offline token failed: %v", err)
		fmt.Println("📝 Note: Make sure the license exists and device is bound first")
	} else {
		fmt.Println("✅ Offline token generated successfully")
		fmt.Printf("   Token: %s...\n", offlineToken[:50])
	}

	if offlineToken != "" {
		// 2. 测试获取离线令牌信息
		fmt.Println("\n2. Testing Get Offline Token Info...")
		if err := testGetOfflineTokenInfo(offlineToken); err != nil {
			log.Printf("❌ Get offline token info failed: %v", err)
		} else {
			fmt.Println("✅ Offline token info retrieved successfully")
		}

		// 3. 测试验证离线令牌
		fmt.Println("\n3. Testing Verify Offline Token...")
		if err := testVerifyOfflineToken(offlineToken, deviceID, productName); err != nil {
			log.Printf("❌ Verify offline token failed: %v", err)
		} else {
			fmt.Println("✅ Offline token verification successful")
		}

		// 4. 测试设备不匹配
		fmt.Println("\n4. Testing Device Mismatch...")
		if err := testVerifyOfflineToken(offlineToken, "WRONG-DEVICE", productName); err != nil {
			fmt.Println("✅ Device mismatch correctly rejected")
		} else {
			fmt.Println("❌ Device mismatch should be rejected")
		}

		// 5. 测试产品不匹配
		fmt.Println("\n5. Testing Product Mismatch...")
		if err := testVerifyOfflineToken(offlineToken, deviceID, "Wrong Product"); err != nil {
			fmt.Println("✅ Product mismatch correctly rejected")
		} else {
			fmt.Println("❌ Product mismatch should be rejected")
		}
	}

	// 6. 测试同步离线数据
	fmt.Println("\n6. Testing Sync Offline Data...")
	if err := testSyncOfflineData(licenseKey, deviceID); err != nil {
		log.Printf("❌ Sync offline data failed: %v", err)
	} else {
		fmt.Println("✅ Offline data synchronized successfully")
	}

	// 7. 测试无效令牌
	fmt.Println("\n7. Testing Invalid Token...")
	if err := testVerifyOfflineToken("invalid.token", deviceID, productName); err != nil {
		fmt.Println("✅ Invalid token correctly rejected")
	} else {
		fmt.Println("❌ Invalid token should be rejected")
	}

	fmt.Println("\n🎉 All offline verification API tests completed!")
	fmt.Println("\n📝 Note: Some tests may fail if the license key doesn't exist or device is not bound.")
	fmt.Println("   Please create a license and bind a device first using the verification API.")
}

func testGenerateOfflineToken(licenseKey, deviceID string, validDays int) (string, error) {
	req := GenerateOfflineTokenRequest{
		LicenseKey: licenseKey,
		DeviceID:   deviceID,
		ValidDays:  validDays,
	}

	jsonData, _ := json.Marshal(req)
	resp, err := http.Post(baseURL+"/offline/generate-token", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return "", err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return "", err
	}

	if resp.StatusCode != http.StatusOK {
		return "", fmt.Errorf("generate offline token failed with status %d: %s", resp.StatusCode, string(body))
	}

	var response struct {
		Message string `json:"message"`
		Data    struct {
			Token     string    `json:"token"`
			ExpiresAt time.Time `json:"expires_at"`
			ValidDays int       `json:"valid_days"`
		} `json:"data"`
	}

	if err := json.Unmarshal(body, &response); err != nil {
		return "", err
	}

	fmt.Printf("   Valid Days: %d\n", response.Data.ValidDays)
	fmt.Printf("   Expires At: %s\n", response.Data.ExpiresAt.Format("2006-01-02 15:04:05"))
	return response.Data.Token, nil
}

func testGetOfflineTokenInfo(token string) error {
	url := fmt.Sprintf("%s/offline/token-info?token=%s", baseURL, token)
	resp, err := http.Get(url)
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("get offline token info failed with status %d: %s", resp.StatusCode, string(body))
	}

	var response struct {
		Message string `json:"message"`
		Data    struct {
			LicenseKey     string    `json:"license_key"`
			DeviceID       string    `json:"device_id"`
			CustomerName   string    `json:"customer_name"`
			ProductName    string    `json:"product_name"`
			ProductVersion string    `json:"product_version"`
			LicenseType    string    `json:"license_type"`
			Features       []string  `json:"features"`
			MaxDevices     int       `json:"max_devices"`
			IssuedAt       time.Time `json:"issued_at"`
			ExpiresAt      time.Time `json:"expires_at"`
			DaysRemaining  int       `json:"days_remaining"`
			Status         string    `json:"status"`
			IsOffline      bool      `json:"is_offline"`
		} `json:"data"`
	}

	if err := json.Unmarshal(body, &response); err != nil {
		return err
	}

	fmt.Printf("   License: %s\n", response.Data.LicenseKey)
	fmt.Printf("   Customer: %s\n", response.Data.CustomerName)
	fmt.Printf("   Product: %s\n", response.Data.ProductName)
	fmt.Printf("   License Type: %s\n", response.Data.LicenseType)
	fmt.Printf("   Features: %v\n", response.Data.Features)
	fmt.Printf("   Max Devices: %d\n", response.Data.MaxDevices)
	fmt.Printf("   Status: %s\n", response.Data.Status)
	fmt.Printf("   Days Remaining: %d\n", response.Data.DaysRemaining)
	fmt.Printf("   Issued: %s\n", response.Data.IssuedAt.Format("2006-01-02 15:04:05"))
	fmt.Printf("   Expires: %s\n", response.Data.ExpiresAt.Format("2006-01-02 15:04:05"))
	return nil
}

func testVerifyOfflineToken(token, deviceID, productName string) error {
	req := VerifyOfflineTokenRequest{
		Token:       token,
		DeviceID:    deviceID,
		ProductName: productName,
	}

	jsonData, _ := json.Marshal(req)
	resp, err := http.Post(baseURL+"/offline/verify", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("verify offline token failed with status %d: %s", resp.StatusCode, string(body))
	}

	var response struct {
		Valid          bool      `json:"valid"`
		LicenseKey     string    `json:"license_key"`
		CustomerName   string    `json:"customer_name"`
		ProductName    string    `json:"product_name"`
		ProductVersion string    `json:"product_version"`
		LicenseType    string    `json:"license_type"`
		Features       []string  `json:"features"`
		MaxDevices     int       `json:"max_devices"`
		ExpiresAt      time.Time `json:"expires_at"`
		DaysRemaining  int       `json:"days_remaining"`
		Status         string    `json:"status"`
		Message        string    `json:"message"`
		IsOffline      bool      `json:"is_offline"`
	}

	if err := json.Unmarshal(body, &response); err != nil {
		return err
	}

	fmt.Printf("   Valid: %v\n", response.Valid)
	fmt.Printf("   Status: %s\n", response.Status)
	fmt.Printf("   Message: %s\n", response.Message)
	fmt.Printf("   Is Offline: %v\n", response.IsOffline)

	if response.Valid {
		fmt.Printf("   License: %s\n", response.LicenseKey)
		fmt.Printf("   Customer: %s\n", response.CustomerName)
		fmt.Printf("   License Type: %s\n", response.LicenseType)
		fmt.Printf("   Features: %v\n", response.Features)
		fmt.Printf("   Max Devices: %d\n", response.MaxDevices)
		fmt.Printf("   Days Remaining: %d\n", response.DaysRemaining)
	}

	if !response.Valid {
		return fmt.Errorf("offline token verification failed: %s", response.Message)
	}

	return nil
}

func testSyncOfflineData(licenseKey, deviceID string) error {
	// 创建一些模拟的离线使用记录
	records := []OfflineUsageRecord{
		{
			LicenseKey: licenseKey,
			DeviceID:   deviceID,
			UsedAt:     time.Now().Add(-2 * time.Hour),
			Features:   []string{"feature1", "feature2"},
			Duration:   120, // 2小时
		},
		{
			LicenseKey: licenseKey,
			DeviceID:   deviceID,
			UsedAt:     time.Now().Add(-1 * time.Hour),
			Features:   []string{"feature1", "advanced_reporting"},
			Duration:   60, // 1小时
		},
		{
			LicenseKey: licenseKey,
			DeviceID:   deviceID,
			UsedAt:     time.Now().Add(-30 * time.Minute),
			Features:   []string{"feature2"},
			Duration:   30, // 30分钟
		},
	}

	req := SyncOfflineDataRequest{
		DeviceID:       deviceID,
		OfflineRecords: records,
	}

	jsonData, _ := json.Marshal(req)
	resp, err := http.Post(baseURL+"/offline/sync", "application/json", bytes.NewBuffer(jsonData))
	if err != nil {
		return err
	}
	defer resp.Body.Close()

	body, err := io.ReadAll(resp.Body)
	if err != nil {
		return err
	}

	if resp.StatusCode != http.StatusOK {
		return fmt.Errorf("sync offline data failed with status %d: %s", resp.StatusCode, string(body))
	}

	var response struct {
		Message string `json:"message"`
		Data    struct {
			TotalRecords  int    `json:"total_records"`
			SyncedRecords int    `json:"synced_records"`
			DeviceID      string `json:"device_id"`
		} `json:"data"`
	}

	if err := json.Unmarshal(body, &response); err != nil {
		return err
	}

	fmt.Printf("   Total Records: %d\n", response.Data.TotalRecords)
	fmt.Printf("   Synced Records: %d\n", response.Data.SyncedRecords)
	fmt.Printf("   Device ID: %s\n", response.Data.DeviceID)
	return nil
}