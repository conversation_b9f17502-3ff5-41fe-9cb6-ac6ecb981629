package database

import (
	"log"
	"time"
	"software-auth/internal/auth"
	"software-auth/internal/model"
)

// SeedData 填充测试数据
func SeedData() error {
	log.Println("Seeding test data...")

	// 创建测试用户
	if err := seedUsers(); err != nil {
		return err
	}

	// 创建测试许可证
	if err := seedLicenses(); err != nil {
		return err
	}

	log.Println("Test data seeding completed")
	return nil
}

// seedUsers 创建测试用户
func seedUsers() error {
	passwordService := auth.NewPasswordService()
	
	users := []model.User{
		{
			Username: "testuser",
			Password: hashPassword(passwordService, "TestPass123!"),
			Email:    "<EMAIL>",
			Role:     model.UserRoleUser,
			Status:   model.UserStatusActive,
		},
		{
			Username: "apiuser",
			Password: hashPassword(passwordService, "ApiPass123!"),
			Email:    "<EMAIL>",
			Role:     model.UserRoleAPI,
			Status:   model.UserStatusActive,
		},
	}

	for _, user := range users {
		var count int64
		DB.Model(&model.User{}).Where("username = ?", user.Username).Count(&count)
		
		if count == 0 {
			if err := DB.Create(&user).Error; err != nil {
				return err
			}
			log.Printf("Created test user: %s", user.Username)
		}
	}

	return nil
}

// seedLicenses 创建测试许可证
func seedLicenses() error {
	// 获取管理员用户ID
	var admin model.User
	if err := DB.Where("role = ?", model.UserRoleAdmin).First(&admin).Error; err != nil {
		return err
	}

	expiresAt := time.Now().AddDate(1, 0, 0) // 1年后过期

	licenses := []model.License{
		{
			LicenseKey:    "TEST-DEMO-LICENSE-001",
			ProductName:   "Demo Software",
			CustomerName:  "Test Customer 1",
			CustomerEmail: "<EMAIL>",
			MaxDevices:    3,
			FeaturesJSON:  []string{"basic", "advanced"},
			ExpiresAt:     &expiresAt,
			Status:        model.LicenseStatusActive,
			CreatedBy:     admin.ID,
		},
		{
			LicenseKey:    "TEST-DEMO-LICENSE-002",
			ProductName:   "Demo Software Pro",
			CustomerName:  "Test Customer 2",
			CustomerEmail: "<EMAIL>",
			MaxDevices:    5,
			FeaturesJSON:  []string{"basic", "advanced", "premium"},
			ExpiresAt:     &expiresAt,
			Status:        model.LicenseStatusActive,
			CreatedBy:     admin.ID,
		},
	}

	for _, license := range licenses {
		var count int64
		DB.Model(&model.License{}).Where("license_key = ?", license.LicenseKey).Count(&count)
		
		if count == 0 {
			if err := DB.Create(&license).Error; err != nil {
				return err
			}
			log.Printf("Created test license: %s", license.LicenseKey)
		}
	}

	return nil
}

// hashPassword 加密密码
func hashPassword(service auth.PasswordService, password string) string {
	hashed, err := service.HashPassword(password)
	if err != nil {
		log.Printf("Failed to hash password: %v", err)
		return password // 这里应该处理错误，但为了简化示例
	}
	return hashed
}