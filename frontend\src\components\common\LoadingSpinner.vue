<template>
  <div class="loading-spinner" :class="{ 'full-screen': fullScreen }">
    <a-spin :size="size" :loading="true">
      <template #icon>
        <icon-loading />
      </template>
      <div v-if="text" class="loading-text">{{ text }}</div>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
interface Props {
  size?: number | 'small' | 'medium' | 'large'
  text?: string
  fullScreen?: boolean
}

withDefaults(defineProps<Props>(), {
  size: 'medium',
  fullScreen: false
})
</script>

<style scoped>
.loading-spinner {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 40px;
}

.loading-spinner.full-screen {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.8);
  z-index: 9999;
  padding: 0;
}

.loading-text {
  margin-top: 12px;
  color: #666;
  font-size: 14px;
}
</style>