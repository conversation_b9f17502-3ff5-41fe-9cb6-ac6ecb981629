package cache

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"time"

	"software-auth/internal/config"

	"github.com/redis/go-redis/v9"
)

// RedisClient Redis客户端实例
var RedisClient *redis.Client

// CacheService 缓存服务接口
type CacheService interface {
	Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error
	Get(ctx context.Context, key string) (string, error)
	GetJSON(ctx context.Context, key string, dest interface{}) error
	SetJSON(ctx context.Context, key string, value interface{}, expiration time.Duration) error
	Delete(ctx context.Context, keys ...string) error
	Exists(ctx context.Context, key string) (bool, error)
	Expire(ctx context.Context, key string, expiration time.Duration) error
	Increment(ctx context.Context, key string) (int64, error)
	IncrementBy(ctx context.Context, key string, value int64) (int64, error)
	SetNX(ctx context.Context, key string, value interface{}, expiration time.Duration) (bool, error)
	GetTTL(ctx context.Context, key string) (time.Duration, error)
	FlushDB(ctx context.Context) error
	Ping(ctx context.Context) error
}

// redisService Redis缓存服务实现
type redisService struct {
	client *redis.Client
}

// Initialize 初始化Redis连接
func Initialize(cfg *config.RedisConfig) error {
	// 创建Redis客户端
	RedisClient = redis.NewClient(&redis.Options{
		Addr:         cfg.Addr,
		Password:     cfg.Password,
		DB:           cfg.DB,
		PoolSize:     cfg.PoolSize,
		MinIdleConns: cfg.MinIdleConns,
		DialTimeout:  cfg.DialTimeout,
		ReadTimeout:  cfg.ReadTimeout,
		WriteTimeout: cfg.WriteTimeout,
	})

	// 测试连接
	ctx, cancel := context.WithTimeout(context.Background(), 5*time.Second)
	defer cancel()

	if err := RedisClient.Ping(ctx).Err(); err != nil {
		return fmt.Errorf("failed to connect to Redis: %v", err)
	}

	log.Println("Redis connected successfully")
	return nil
}

// NewCacheService 创建缓存服务实例
func NewCacheService() CacheService {
	if RedisClient == nil {
		return &noOpCacheService{}
	}
	return &redisService{
		client: RedisClient,
	}
}

// Close 关闭Redis连接
func Close() error {
	if RedisClient != nil {
		return RedisClient.Close()
	}
	return nil
}

// GetClient 获取Redis客户端
func GetClient() *redis.Client {
	return RedisClient
}

// Set 设置缓存
func (r *redisService) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	return r.client.Set(ctx, key, value, expiration).Err()
}

// Get 获取缓存
func (r *redisService) Get(ctx context.Context, key string) (string, error) {
	result, err := r.client.Get(ctx, key).Result()
	if err == redis.Nil {
		return "", ErrCacheNotFound
	}
	return result, err
}

// GetJSON 获取JSON格式缓存
func (r *redisService) GetJSON(ctx context.Context, key string, dest interface{}) error {
	result, err := r.Get(ctx, key)
	if err != nil {
		return err
	}
	return json.Unmarshal([]byte(result), dest)
}

// SetJSON 设置JSON格式缓存
func (r *redisService) SetJSON(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	jsonData, err := json.Marshal(value)
	if err != nil {
		return err
	}
	return r.Set(ctx, key, string(jsonData), expiration)
}

// Delete 删除缓存
func (r *redisService) Delete(ctx context.Context, keys ...string) error {
	if len(keys) == 0 {
		return nil
	}
	return r.client.Del(ctx, keys...).Err()
}

// Exists 检查缓存是否存在
func (r *redisService) Exists(ctx context.Context, key string) (bool, error) {
	result, err := r.client.Exists(ctx, key).Result()
	return result > 0, err
}

// Expire 设置过期时间
func (r *redisService) Expire(ctx context.Context, key string, expiration time.Duration) error {
	return r.client.Expire(ctx, key, expiration).Err()
}

// Increment 递增
func (r *redisService) Increment(ctx context.Context, key string) (int64, error) {
	return r.client.Incr(ctx, key).Result()
}

// IncrementBy 按指定值递增
func (r *redisService) IncrementBy(ctx context.Context, key string, value int64) (int64, error) {
	return r.client.IncrBy(ctx, key, value).Result()
}

// SetNX 仅当键不存在时设置
func (r *redisService) SetNX(ctx context.Context, key string, value interface{}, expiration time.Duration) (bool, error) {
	return r.client.SetNX(ctx, key, value, expiration).Result()
}

// GetTTL 获取键的剩余生存时间
func (r *redisService) GetTTL(ctx context.Context, key string) (time.Duration, error) {
	return r.client.TTL(ctx, key).Result()
}

// FlushDB 清空当前数据库
func (r *redisService) FlushDB(ctx context.Context) error {
	return r.client.FlushDB(ctx).Err()
}

// noOpCacheService NoOp缓存服务实现（当Redis不可用时使用）
type noOpCacheService struct{}

func (n *noOpCacheService) Set(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	return nil // 静默忽略
}

func (n *noOpCacheService) Get(ctx context.Context, key string) (string, error) {
	return "", ErrCacheNotFound
}

func (n *noOpCacheService) GetJSON(ctx context.Context, key string, dest interface{}) error {
	return ErrCacheNotFound
}

func (n *noOpCacheService) SetJSON(ctx context.Context, key string, value interface{}, expiration time.Duration) error {
	return nil // 静默忽略
}

func (n *noOpCacheService) Delete(ctx context.Context, keys ...string) error {
	return nil // 静默忽略
}

func (n *noOpCacheService) Exists(ctx context.Context, key string) (bool, error) {
	return false, nil
}

func (n *noOpCacheService) Expire(ctx context.Context, key string, expiration time.Duration) error {
	return nil // 静默忽略
}

func (n *noOpCacheService) Increment(ctx context.Context, key string) (int64, error) {
	return 0, nil
}

func (n *noOpCacheService) IncrementBy(ctx context.Context, key string, value int64) (int64, error) {
	return 0, nil
}

func (n *noOpCacheService) SetNX(ctx context.Context, key string, value interface{}, expiration time.Duration) (bool, error) {
	return true, nil // 假装设置成功
}

func (n *noOpCacheService) GetTTL(ctx context.Context, key string) (time.Duration, error) {
	return 0, nil
}

func (n *noOpCacheService) FlushDB(ctx context.Context) error {
	return nil // 静默忽略
}

func (n *noOpCacheService) Ping(ctx context.Context) error {
	return fmt.Errorf("cache service not available (NoOp mode)")
}

// Ping 测试连接
func (r *redisService) Ping(ctx context.Context) error {
	return r.client.Ping(ctx).Err()
}
