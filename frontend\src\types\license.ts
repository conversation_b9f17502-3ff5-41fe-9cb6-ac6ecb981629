// 许可证类型
export interface License {
  id: number
  license_key: string
  product_name: string
  customer_name: string
  customer_email: string
  max_devices: number
  features: string[]
  expires_at?: string
  status: 'active' | 'expired' | 'disabled' | 'suspended'
  created_by: number
  created_at: string
  updated_at: string
  creator?: {
    id: number
    username: string
  }
  device_bindings?: DeviceBinding[]
}

// 设备绑定类型
export interface DeviceBinding {
  id: number
  license_key: string
  device_id: string
  device_name: string
  first_seen_at: string
  last_seen_at: string
  status: 'active' | 'inactive' | 'blocked'
  created_at: string
  updated_at: string
}

// 验证日志类型
export interface VerificationLog {
  id: number
  license_key: string
  device_id: string
  ip_address: string
  user_agent: string
  verification_result: string
  error_message?: string
  response_time: number
  created_at: string
}

// 许可证创建请求类型
export interface CreateLicenseRequest {
  product_name: string
  customer_name: string
  customer_email: string
  max_devices: number
  features: string[]
  expires_at?: string
}

// 许可证更新请求类型
export interface UpdateLicenseRequest {
  product_name?: string
  customer_name?: string
  customer_email?: string
  max_devices?: number
  features?: string[]
  expires_at?: string
  status?: string
}

// 许可证验证请求类型
export interface VerifyLicenseRequest {
  license_key: string
  device_id: string
  device_name?: string
  features?: string[]
}

// 许可证验证响应类型
export interface VerifyLicenseResponse {
  valid: boolean
  license_key: string
  product_name: string
  max_devices: number
  features: string[]
  expires_at?: string
  device_count: number
  error_message?: string
}

// 许可证列表查询参数
export interface LicenseListParams {
  page?: number
  limit?: number
  search?: string
  status?: string
  product_name?: string
  customer_name?: string
  sort_by?: string
  sort_order?: 'asc' | 'desc'
}

// 分页响应类型
export interface PaginatedResponse<T> {
  message: string
  data: T[]
  pagination: {
    page: number
    limit: number
    total: number
    total_pages: number
    has_next: boolean
    has_prev: boolean
  }
  timestamp: number
}