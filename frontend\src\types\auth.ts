// 用户信息类型
export interface UserInfo {
  id: number
  username: string
  email: string
  role: 'admin' | 'user' | 'api'
  status: 'active' | 'inactive' | 'blocked'
}

// 令牌对类型
export interface TokenPair {
  access_token: string
  refresh_token: string
  token_type: string
  expires_in: number
}

// 登录响应类型
export interface LoginResponse {
  user: UserInfo
  tokens: TokenPair
}

// 登录请求类型
export interface LoginRequest {
  username: string
  password: string
}

// 刷新令牌请求类型
export interface RefreshTokenRequest {
  refresh_token: string
}

// 修改密码请求类型
export interface ChangePasswordRequest {
  old_password: string
  new_password: string
}

// API响应基础类型
export interface ApiResponse<T = any> {
  message: string
  data?: T
  timestamp: number
}

// API错误响应类型
export interface ApiErrorResponse {
  error: string
  message?: string
  code?: string
  details?: any
  timestamp: number
}