<template>
  <div class="edit-license-page">
    <PageHeader 
      :title="`编辑许可证 - ${licenseData?.license_key || ''}`"
      description="修改许可证的配置信息"
    >
      <template #extra>
        <a-space>
          <a-button @click="$router.back()">
            <template #icon>
              <icon-left />
            </template>
            返回
          </a-button>
          <a-button @click="handleReset" :disabled="!hasChanges">
            <template #icon>
              <icon-refresh />
            </template>
            重置
          </a-button>
          <a-button type="primary" @click="handleSubmit" :loading="loading">
            <template #icon>
              <icon-check />
            </template>
            保存更改
          </a-button>
        </a-space>
      </template>
    </PageHeader>

    <div v-if="pageLoading" class="loading-container">
      <LoadingSpinner text="加载许可证信息..." />
    </div>

    <div v-else-if="licenseData" class="form-container">
      <a-row :gutter="24">
        <!-- 左侧：编辑表单 -->
        <a-col :span="16">
          <a-card title="基本信息" class="form-card">
            <a-form
              ref="formRef"
              :model="form"
              :rules="rules"
              layout="vertical"
              @submit="handleSubmit"
            >
              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item field="license_key" label="许可证密钥">
                    <a-input
                      v-model="form.license_key"
                      disabled
                      class="license-key-input"
                    >
                      <template #suffix>
                        <a-button 
                          type="text" 
                          size="small"
                          @click="handleCopyKey"
                        >
                          <icon-copy />
                        </a-button>
                      </template>
                    </a-input>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item field="status" label="状态">
                    <a-select v-model="form.status">
                      <a-option value="active">有效</a-option>
                      <a-option value="disabled">已禁用</a-option>
                      <a-option value="suspended">已暂停</a-option>
                    </a-select>
                  </a-form-item>
                </a-col>
              </a-row>

              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item field="product_name" label="产品名称">
                    <a-select
                      v-model="form.product_name"
                      placeholder="选择或输入产品名称"
                      allow-create
                      allow-search
                    >
                      <a-option v-for="product in productOptions" :key="product" :value="product">
                        {{ product }}
                      </a-option>
                    </a-select>
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item field="max_devices" label="最大设备数">
                    <a-input-number
                      v-model="form.max_devices"
                      :min="1"
                      :max="100"
                      placeholder="设备数量限制"
                    />
                    <template #help>
                      <span>当前已绑定 {{ currentDeviceCount }} 台设备</span>
                    </template>
                  </a-form-item>
                </a-col>
              </a-row>

              <a-row :gutter="16">
                <a-col :span="12">
                  <a-form-item field="customer_name" label="客户名称">
                    <a-input
                      v-model="form.customer_name"
                      placeholder="请输入客户名称"
                    />
                  </a-form-item>
                </a-col>
                <a-col :span="12">
                  <a-form-item field="customer_email" label="客户邮箱">
                    <a-input
                      v-model="form.customer_email"
                      placeholder="请输入客户邮箱"
                    />
                  </a-form-item>
                </a-col>
              </a-row>

              <a-form-item field="expires_at" label="过期时间">
                <a-radio-group v-model="expireType" @change="handleExpireTypeChange">
                  <a-radio value="never">永不过期</a-radio>
                  <a-radio value="custom">自定义时间</a-radio>
                  <a-radio value="extend">延长时间</a-radio>
                </a-radio-group>
                
                <div v-if="expireType === 'custom'" class="expire-input">
                  <a-date-picker
                    v-model="form.expires_at"
                    show-time
                    format="YYYY-MM-DD HH:mm:ss"
                    placeholder="选择过期时间"
                    :disabled-date="(date) => date.isBefore(dayjs(), 'day')"
                  />
                </div>
                
                <div v-if="expireType === 'extend'" class="expire-input">
                  <a-space>
                    <span>延长</span>
                    <a-input-number
                      v-model="extendDuration.value"
                      :min="1"
                      placeholder="时长"
                      style="width: 120px"
                    />
                    <a-select
                      v-model="extendDuration.unit"
                      style="width: 100px"
                    >
                      <a-option value="days">天</a-option>
                      <a-option value="months">月</a-option>
                      <a-option value="years">年</a-option>
                    </a-select>
                  </a-space>
                  <div v-if="getExtendedExpireTime()" class="extend-preview">
                    延长后过期时间：{{ dayjs(getExtendedExpireTime()).format('YYYY-MM-DD HH:mm:ss') }}
                  </div>
                </div>
                
                <div v-if="originalExpireTime" class="current-expire">
                  当前过期时间：{{ dayjs(originalExpireTime).format('YYYY-MM-DD HH:mm:ss') }}
                </div>
              </a-form-item>

              <a-form-item field="features" label="功能特性">
                <div class="features-section">
                  <div class="feature-checkboxes">
                    <a-checkbox-group v-model="form.features">
                      <a-checkbox 
                        v-for="feature in availableFeatures" 
                        :key="feature.value" 
                        :value="feature.value"
                      >
                        {{ feature.label }}
                      </a-checkbox>
                    </a-checkbox-group>
                  </div>
                  
                  <div class="custom-feature">
                    <a-input
                      v-model="customFeature"
                      placeholder="添加自定义功能"
                      @keyup.enter="handleAddCustomFeature"
                    >
                      <template #suffix>
                        <a-button 
                          type="text" 
                          size="small"
                          @click="handleAddCustomFeature"
                          :disabled="!customFeature.trim()"
                        >
                          添加
                        </a-button>
                      </template>
                    </a-input>
                  </div>
                  
                  <div v-if="customFeatures.length > 0" class="custom-features-list">
                    <a-tag
                      v-for="feature in customFeatures"
                      :key="feature"
                      closable
                      @close="handleRemoveCustomFeature(feature)"
                    >
                      {{ feature }}
                    </a-tag>
                  </div>
                </div>
              </a-form-item>
            </a-form>
          </a-card>
        </a-col>

        <!-- 右侧：信息面板 -->
        <a-col :span="8">
          <!-- 许可证状态 -->
          <a-card title="许可证状态" class="status-card">
            <div class="status-info">
              <div class="status-item">
                <span class="status-label">当前状态：</span>
                <StatusTag :status="licenseData.status" type="license" />
              </div>
              
              <div class="status-item">
                <span class="status-label">创建时间：</span>
                <span class="status-value">{{ formatDate(licenseData.created_at) }}</span>
              </div>
              
              <div class="status-item">
                <span class="status-label">最后更新：</span>
                <span class="status-value">{{ formatDate(licenseData.updated_at) }}</span>
              </div>
              
              <div class="status-item">
                <span class="status-label">创建者：</span>
                <span class="status-value">{{ licenseData.creator?.username || '-' }}</span>
              </div>
            </div>
          </a-card>

          <!-- 设备绑定信息 -->
          <a-card title="设备绑定" class="device-card">
            <div class="device-info">
              <div class="device-summary">
                <span class="device-count">{{ currentDeviceCount }} / {{ form.max_devices }}</span>
                <span class="device-label">已绑定设备</span>
              </div>
              
              <a-progress
                :percent="getDeviceUsagePercent()"
                :color="getDeviceUsageColor()"
                :show-text="false"
              />
            </div>
            
            <div v-if="licenseData.device_bindings?.length" class="device-list">
              <div 
                v-for="device in licenseData.device_bindings" 
                :key="device.id"
                class="device-item"
              >
                <div class="device-name">{{ device.device_name || device.device_id }}</div>
                <div class="device-time">{{ formatDate(device.last_seen_at) }}</div>
                <StatusTag :status="device.status" type="device" size="small" />
              </div>
            </div>
            
            <EmptyState 
              v-else
              type="no-data"
              title="暂无绑定设备"
              description="还没有设备使用此许可证"
            />
          </a-card>

          <!-- 操作历史 -->
          <a-card title="最近操作" class="history-card">
            <div class="history-list">
              <div class="history-item">
                <div class="history-action">许可证创建</div>
                <div class="history-time">{{ formatDate(licenseData.created_at) }}</div>
              </div>
              <div v-if="licenseData.updated_at !== licenseData.created_at" class="history-item">
                <div class="history-action">信息更新</div>
                <div class="history-time">{{ formatDate(licenseData.updated_at) }}</div>
              </div>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </div>

    <EmptyState 
      v-else
      type="error"
      title="许可证不存在"
      description="找不到指定的许可证信息"
    >
      <template #action>
        <a-button type="primary" @click="$router.push('/licenses')">
          返回列表
        </a-button>
      </template>
    </EmptyState>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { Message } from '@arco-design/web-vue'
import type { FormInstance } from '@arco-design/web-vue'
import PageHeader from '@/components/common/PageHeader.vue'
import StatusTag from '@/components/common/StatusTag.vue'
import LoadingSpinner from '@/components/common/LoadingSpinner.vue'
import EmptyState from '@/components/common/EmptyState.vue'
import type { License, UpdateLicenseRequest } from '@/types/license'
import { licenseApi } from '@/api/license'
import dayjs from 'dayjs'

const router = useRouter()
const route = useRoute()
const formRef = ref<FormInstance>()
const pageLoading = ref(true)
const loading = ref(false)

// 许可证数据
const licenseData = ref<License | null>(null)
const originalData = ref<License | null>(null)

// 过期类型
const expireType = ref<'never' | 'custom' | 'extend'>('never')
const originalExpireTime = ref<string | null>(null)

// 延长时长
const extendDuration = reactive({
  value: 1,
  unit: 'months' as 'days' | 'months' | 'years'
})

// 自定义功能
const customFeature = ref('')
const customFeatures = ref<string[]>([])

// 表单数据
const form = reactive<UpdateLicenseRequest & { license_key: string }>({
  license_key: '',
  product_name: '',
  customer_name: '',
  customer_email: '',
  max_devices: 1,
  features: [],
  expires_at: undefined,
  status: 'active'
})

// 表单验证规则
const rules = {
  product_name: [
    { required: true, message: '请输入产品名称' }
  ],
  customer_name: [
    { required: true, message: '请输入客户名称' }
  ],
  customer_email: [
    { required: true, message: '请输入客户邮箱' },
    { type: 'email', message: '请输入有效的邮箱地址' }
  ],
  max_devices: [
    { required: true, message: '请设置设备数量限制' },
    { type: 'number', min: 1, message: '设备数量至少为1' }
  ]
}

// 产品选项
const productOptions = [
  'Demo Software',
  'Pro Software',
  'Enterprise Suite',
  'Mobile App',
  'Web Service'
]

// 可用功能特性
const availableFeatures = [
  { label: '基础功能', value: 'basic' },
  { label: '高级功能', value: 'advanced' },
  { label: '专业功能', value: 'professional' },
  { label: '企业功能', value: 'enterprise' },
  { label: 'API访问', value: 'api_access' },
  { label: '数据导出', value: 'data_export' },
  { label: '多用户支持', value: 'multi_user' },
  { label: '云同步', value: 'cloud_sync' }
]

// 当前设备数量
const currentDeviceCount = computed(() => {
  return licenseData.value?.device_bindings?.filter(d => d.status === 'active').length || 0
})

// 是否有更改
const hasChanges = computed(() => {
  if (!originalData.value) return false
  
  return (
    form.product_name !== originalData.value.product_name ||
    form.customer_name !== originalData.value.customer_name ||
    form.customer_email !== originalData.value.customer_email ||
    form.max_devices !== originalData.value.max_devices ||
    form.status !== originalData.value.status ||
    form.expires_at !== originalData.value.expires_at ||
    JSON.stringify(getAllFeatures()) !== JSON.stringify(originalData.value.features)
  )
})

// 格式化日期
const formatDate = (date: string) => {
  return dayjs(date).format('YYYY-MM-DD HH:mm:ss')
}

// 获取设备使用百分比
const getDeviceUsagePercent = () => {
  return Math.round((currentDeviceCount.value / form.max_devices) * 100)
}

// 获取设备使用颜色
const getDeviceUsageColor = () => {
  const percent = getDeviceUsagePercent()
  if (percent >= 90) return '#ff4d4f'
  if (percent >= 70) return '#faad14'
  return '#52c41a'
}

// 获取所有功能（包括自定义）
const getAllFeatures = () => {
  return [...form.features, ...customFeatures.value]
}

// 获取延长后的过期时间
const getExtendedExpireTime = () => {
  if (!originalExpireTime.value) return null
  
  const baseTime = dayjs(originalExpireTime.value)
  let extendedTime = baseTime
  
  switch (extendDuration.unit) {
    case 'days':
      extendedTime = baseTime.add(extendDuration.value, 'day')
      break
    case 'months':
      extendedTime = baseTime.add(extendDuration.value, 'month')
      break
    case 'years':
      extendedTime = baseTime.add(extendDuration.value, 'year')
      break
  }
  
  return extendedTime.toISOString()
}

// 过期类型变化处理
const handleExpireTypeChange = () => {
  if (expireType.value === 'never') {
    form.expires_at = undefined
  } else if (expireType.value === 'custom') {
    form.expires_at = originalExpireTime.value
  } else if (expireType.value === 'extend') {
    form.expires_at = getExtendedExpireTime()
  }
}

// 监听延长时长变化
watch([() => extendDuration.value, () => extendDuration.unit], () => {
  if (expireType.value === 'extend') {
    form.expires_at = getExtendedExpireTime()
  }
})

// 添加自定义功能
const handleAddCustomFeature = () => {
  const feature = customFeature.value.trim()
  if (feature && !customFeatures.value.includes(feature)) {
    customFeatures.value.push(feature)
    customFeature.value = ''
  }
}

// 移除自定义功能
const handleRemoveCustomFeature = (feature: string) => {
  const index = customFeatures.value.indexOf(feature)
  if (index > -1) {
    customFeatures.value.splice(index, 1)
  }
}

// 复制许可证密钥
const handleCopyKey = async () => {
  try {
    await navigator.clipboard.writeText(form.license_key)
    Message.success('许可证密钥已复制到剪贴板')
  } catch (error) {
    console.error('Copy error:', error)
    Message.error('复制失败')
  }
}

// 重置表单
const handleReset = () => {
  if (originalData.value) {
    initializeForm(originalData.value)
    Message.info('已重置为原始数据')
  }
}

// 提交表单
const handleSubmit = async () => {
  try {
    const valid = await formRef.value?.validate()
    if (!valid) return

    loading.value = true

    // 准备提交数据
    const submitData: UpdateLicenseRequest = {
      product_name: form.product_name,
      customer_name: form.customer_name,
      customer_email: form.customer_email,
      max_devices: form.max_devices,
      features: getAllFeatures(),
      expires_at: form.expires_at,
      status: form.status
    }

    // TODO: 调用实际API
    // const response = await licenseApi.update(licenseData.value!.id, submitData)
    
    // 模拟API调用
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    Message.success('许可证更新成功')
    
    // 重新加载数据
    await loadLicenseData()
    
  } catch (error: any) {
    console.error('Update license error:', error)
    Message.error(error.message || '更新许可证失败')
  } finally {
    loading.value = false
  }
}

// 初始化表单数据
const initializeForm = (data: License) => {
  form.license_key = data.license_key
  form.product_name = data.product_name
  form.customer_name = data.customer_name
  form.customer_email = data.customer_email
  form.max_devices = data.max_devices
  form.status = data.status
  form.expires_at = data.expires_at

  // 分离标准功能和自定义功能
  const standardFeatures = data.features.filter(f => 
    availableFeatures.some(af => af.value === f)
  )
  const customFeaturesList = data.features.filter(f => 
    !availableFeatures.some(af => af.value === f)
  )

  form.features = standardFeatures
  customFeatures.value = customFeaturesList

  // 设置过期类型
  if (!data.expires_at) {
    expireType.value = 'never'
  } else {
    expireType.value = 'custom'
    originalExpireTime.value = data.expires_at
  }
}

// 加载许可证数据
const loadLicenseData = async () => {
  try {
    pageLoading.value = true
    
    const licenseId = Number(route.params.id)
    if (!licenseId) {
      throw new Error('Invalid license ID')
    }

    // TODO: 调用实际API
    // const response = await licenseApi.getById(licenseId)
    
    // 模拟数据
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    const mockData: License = {
      id: licenseId,
      license_key: 'TEST-DEMO-LICENSE-001',
      product_name: 'Demo Software',
      customer_name: 'Test Customer 1',
      customer_email: '<EMAIL>',
      max_devices: 3,
      features: ['basic', 'advanced'],
      expires_at: dayjs().add(30, 'day').toISOString(),
      status: 'active',
      created_by: 1,
      created_at: dayjs().subtract(10, 'day').toISOString(),
      updated_at: dayjs().subtract(1, 'day').toISOString(),
      creator: {
        id: 1,
        username: 'admin'
      },
      device_bindings: [
        {
          id: 1,
          license_key: 'TEST-DEMO-LICENSE-001',
          device_id: 'device-001',
          device_name: 'PC-001',
          first_seen_at: dayjs().subtract(5, 'day').toISOString(),
          last_seen_at: dayjs().subtract(1, 'hour').toISOString(),
          status: 'active',
          created_at: dayjs().subtract(5, 'day').toISOString(),
          updated_at: dayjs().subtract(1, 'hour').toISOString()
        }
      ]
    }
    
    licenseData.value = mockData
    originalData.value = JSON.parse(JSON.stringify(mockData))
    
    initializeForm(mockData)
    
  } catch (error) {
    console.error('Failed to load license data:', error)
    Message.error('加载许可证信息失败')
  } finally {
    pageLoading.value = false
  }
}

onMounted(() => {
  loadLicenseData()
})
</script>

<style scoped>
.edit-license-page {
  padding: 24px;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.form-container {
  margin-top: 24px;
}

.form-card,
.status-card,
.device-card,
.history-card {
  margin-bottom: 24px;
}

.license-key-input {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 12px;
}

.expire-input {
  margin-top: 12px;
}

.extend-preview {
  margin-top: 8px;
  font-size: 12px;
  color: #1890ff;
}

.current-expire {
  margin-top: 8px;
  font-size: 12px;
  color: #8c8c8c;
}

.features-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.feature-checkboxes {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 8px;
}

.custom-features-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.status-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.status-label {
  font-size: 14px;
  color: #8c8c8c;
}

.status-value {
  font-size: 14px;
  color: #262626;
}

.device-info {
  margin-bottom: 16px;
}

.device-summary {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.device-count {
  font-size: 18px;
  font-weight: 600;
  color: #1890ff;
}

.device-label {
  font-size: 12px;
  color: #8c8c8c;
}

.device-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  max-height: 200px;
  overflow-y: auto;
}

.device-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px;
  background: #fafafa;
  border-radius: 4px;
}

.device-name {
  font-size: 14px;
  font-weight: 500;
  color: #262626;
}

.device-time {
  font-size: 12px;
  color: #8c8c8c;
}

.history-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.history-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
  border-bottom: 1px solid #f0f0f0;
}

.history-item:last-child {
  border-bottom: none;
}

.history-action {
  font-size: 14px;
  color: #262626;
}

.history-time {
  font-size: 12px;
  color: #8c8c8c;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .form-container .arco-row {
    flex-direction: column;
  }
  
  .form-container .arco-col {
    width: 100%;
  }
  
  .status-card,
  .device-card,
  .history-card {
    margin-top: 24px;
  }
}

@media (max-width: 768px) {
  .edit-license-page {
    padding: 16px;
  }
  
  .feature-checkboxes {
    grid-template-columns: 1fr;
  }
}
</style>