<template>
  <div class="simple-login">
    <div class="login-card">
      <h1>软件网络授权系统</h1>
      <div class="form-group">
        <label>用户名:</label>
        <input v-model="username" type="text" placeholder="admin" />
      </div>
      <div class="form-group">
        <label>密码:</label>
        <input v-model="password" type="password" placeholder="AdminPass123!" />
      </div>
      <button @click="login" :disabled="loading">
        {{ loading ? '登录中...' : '登录' }}
      </button>
      <div class="tips">
        <p>默认账户: admin / AdminPass123!</p>
        <p><a href="/test-dashboard.html" target="_blank">测试静态页面</a></p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const username = ref('admin')
const password = ref('AdminPass123!')
const loading = ref(false)

const login = async () => {
  console.log('=== 登录流程开始 ===')
  console.log('用户名:', username.value)
  console.log('密码长度:', password.value.length)
  
  loading.value = true
  
  try {
    // 简单验证
    if (username.value === 'admin' && password.value === 'AdminPass123!') {
      console.log('✅ 验证成功')
      
      // 保存登录状态
      localStorage.setItem('isLoggedIn', 'true')
      localStorage.setItem('username', username.value)
      console.log('✅ 本地存储已保存')
      
      // 验证存储
      const stored = localStorage.getItem('isLoggedIn')
      console.log('验证存储结果:', stored)
      
      alert('登录成功！即将跳转...')
      
      // 跳转到仪表板
      console.log('🔄 准备跳转到仪表板')
      console.log('当前路由:', router.currentRoute.value.path)
      
      // 延迟一下确保状态保存
      await new Promise(resolve => setTimeout(resolve, 100))
      
      try {
        const result = await router.push('/dashboard')
        console.log('✅ 路由跳转结果:', result)
        console.log('新路由:', router.currentRoute.value.path)
      } catch (error) {
        console.error('❌ 路由跳转失败:', error)
        alert('页面跳转失败，请手动刷新页面')
      }
    } else {
      console.log('❌ 验证失败')
      alert('用户名或密码错误')
    }
  } catch (error) {
    console.error('❌ 登录过程出错:', error)
    alert('登录失败: ' + error.message)
  } finally {
    loading.value = false
    console.log('=== 登录流程结束 ===')
  }
}
</script>

<style scoped>
.simple-login {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
}

.login-card {
  background: white;
  padding: 40px;
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  width: 400px;
}

h1 {
  text-align: center;
  color: #1890ff;
  margin-bottom: 30px;
}

.form-group {
  margin-bottom: 20px;
}

label {
  display: block;
  margin-bottom: 5px;
  font-weight: 500;
}

input {
  width: 100%;
  padding: 12px;
  border: 1px solid #d9d9d9;
  border-radius: 4px;
  font-size: 14px;
}

button {
  width: 100%;
  padding: 12px;
  background: #1890ff;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
}

button:hover {
  background: #40a9ff;
}

button:disabled {
  background: #d9d9d9;
  cursor: not-allowed;
}

.tips {
  margin-top: 20px;
  text-align: center;
  color: #666;
  font-size: 12px;
}
</style>