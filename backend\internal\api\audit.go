package api

import (
	"fmt"
	"net/http"
	"strconv"
	"time"

	"github.com/gin-gonic/gin"
	"software-auth/internal/database"
	"software-auth/internal/model"
)

// AuditHandler 审计日志处理器
type AuditHandler struct{}

// NewAuditHandler 创建审计日志处理器
func NewAuditHandler() *AuditHandler {
	return &AuditHandler{}
}

// AuditLogListRequest 审计日志列表请求
type AuditLogListRequest struct {
	Page       int    `form:"page,default=1" binding:"min=1"`
	PageSize   int    `form:"page_size,default=20" binding:"min=1,max=100"`
	UserID     uint   `form:"user_id"`
	Username   string `form:"username"`
	Action     string `form:"action"`
	Resource   string `form:"resource"`
	ClientIP   string `form:"client_ip"`
	Success    string `form:"success" binding:"omitempty,oneof=true false"`
	StartDate  string `form:"start_date"` // YYYY-MM-DD
	EndDate    string `form:"end_date"`   // YYYY-MM-DD
	SortBy     string `form:"sort_by,default=created_at" binding:"omitempty,oneof=created_at action resource client_ip status_code duration"`
	SortOrder  string `form:"sort_order,default=desc" binding:"omitempty,oneof=asc desc"`
}

// AuditLogResponse 审计日志响应
type AuditLogResponse struct {
	ID          uint      `json:"id"`
	UserID      uint      `json:"user_id"`
	Username    string    `json:"username"`
	Action      string    `json:"action"`
	Resource    string    `json:"resource"`
	ResourceID  string    `json:"resource_id"`
	Method      string    `json:"method"`
	Path        string    `json:"path"`
	ClientIP    string    `json:"client_ip"`
	UserAgent   string    `json:"user_agent"`
	StatusCode  int       `json:"status_code"`
	Success     bool      `json:"success"`
	Message     string    `json:"message"`
	RequestData string    `json:"request_data,omitempty"`
	Duration    int64     `json:"duration"`
	CreatedAt   time.Time `json:"created_at"`
}

// AuditLogListResponse 审计日志列表响应
type AuditLogListResponse struct {
	Logs       []AuditLogResponse `json:"logs"`
	Total      int64              `json:"total"`
	Page       int                `json:"page"`
	PageSize   int                `json:"page_size"`
	TotalPages int                `json:"total_pages"`
}

// AuditStatsResponse 审计统计响应
type AuditStatsResponse struct {
	TotalLogs       int64                    `json:"total_logs"`
	SuccessfulLogs  int64                    `json:"successful_logs"`
	FailedLogs      int64                    `json:"failed_logs"`
	UniqueUsers     int64                    `json:"unique_users"`
	UniqueIPs       int64                    `json:"unique_ips"`
	TopActions      []ActionStat             `json:"top_actions"`
	TopUsers        []UserStat               `json:"top_users"`
	TopIPs          []IPStat                 `json:"top_ips"`
	HourlyActivity  []HourlyActivityStat     `json:"hourly_activity"`
	DailyActivity   []DailyActivityStat      `json:"daily_activity"`
}

// ActionStat 操作统计
type ActionStat struct {
	Action string `json:"action"`
	Count  int64  `json:"count"`
}

// UserStat 用户统计
type UserStat struct {
	UserID   uint   `json:"user_id"`
	Username string `json:"username"`
	Count    int64  `json:"count"`
}

// IPStat IP统计
type IPStat struct {
	ClientIP string `json:"client_ip"`
	Count    int64  `json:"count"`
}

// HourlyActivityStat 小时活动统计
type HourlyActivityStat struct {
	Hour  int   `json:"hour"`
	Count int64 `json:"count"`
}

// DailyActivityStat 日活动统计
type DailyActivityStat struct {
	Date  string `json:"date"`
	Count int64  `json:"count"`
}

// GetAuditLogs 获取审计日志列表
func (h *AuditHandler) GetAuditLogs(c *gin.Context) {
	// 检查权限
	userRole, exists := c.Get("user_role")
	if !exists || userRole != "admin" {
		ErrorResponse(c, http.StatusForbidden, "Insufficient permissions", "ADMIN_REQUIRED")
		return
	}

	var req AuditLogListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		ErrorResponse(c, http.StatusBadRequest, "Invalid query parameters", err.Error())
		return
	}

	db := database.GetDB()
	query := db.Model(&model.AuditLog{})

	// 应用筛选条件
	if req.UserID > 0 {
		query = query.Where("user_id = ?", req.UserID)
	}

	if req.Username != "" {
		query = query.Where("username LIKE ?", "%"+req.Username+"%")
	}

	if req.Action != "" {
		query = query.Where("action = ?", req.Action)
	}

	if req.Resource != "" {
		query = query.Where("resource = ?", req.Resource)
	}

	if req.ClientIP != "" {
		query = query.Where("client_ip = ?", req.ClientIP)
	}

	if req.Success != "" {
		success := req.Success == "true"
		query = query.Where("success = ?", success)
	}

	// 日期范围筛选
	if req.StartDate != "" {
		startDate, err := time.Parse("2006-01-02", req.StartDate)
		if err == nil {
			query = query.Where("created_at >= ?", startDate)
		}
	}

	if req.EndDate != "" {
		endDate, err := time.Parse("2006-01-02", req.EndDate)
		if err == nil {
			// 包含整个结束日期
			endDate = endDate.Add(24 * time.Hour)
			query = query.Where("created_at < ?", endDate)
		}
	}

	// 计算总数
	var total int64
	query.Count(&total)

	// 应用排序
	orderClause := req.SortBy + " " + req.SortOrder
	query = query.Order(orderClause)

	// 应用分页
	offset := (req.Page - 1) * req.PageSize
	query = query.Offset(offset).Limit(req.PageSize)

	// 查询数据
	var logs []model.AuditLog
	if err := query.Find(&logs).Error; err != nil {
		ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch audit logs", err.Error())
		return
	}

	// 构建响应
	logResponses := make([]AuditLogResponse, len(logs))
	for i, log := range logs {
		logResponses[i] = buildAuditLogResponse(&log, false) // 不包含请求数据
	}

	totalPages := int((total + int64(req.PageSize) - 1) / int64(req.PageSize))

	response := AuditLogListResponse{
		Logs:       logResponses,
		Total:      total,
		Page:       req.Page,
		PageSize:   req.PageSize,
		TotalPages: totalPages,
	}

	SuccessResponse(c, "Audit logs retrieved successfully", response)
}

// GetAuditLog 获取单个审计日志详情
func (h *AuditHandler) GetAuditLog(c *gin.Context) {
	// 检查权限
	userRole, exists := c.Get("user_role")
	if !exists || userRole != "admin" {
		ErrorResponse(c, http.StatusForbidden, "Insufficient permissions", "ADMIN_REQUIRED")
		return
	}

	idParam := c.Param("id")
	id, err := strconv.ParseUint(idParam, 10, 32)
	if err != nil {
		ErrorResponse(c, http.StatusBadRequest, "Invalid audit log ID", "INVALID_ID")
		return
	}

	db := database.GetDB()
	var log model.AuditLog
	if err := db.First(&log, uint(id)).Error; err != nil {
		ErrorResponse(c, http.StatusNotFound, "Audit log not found", "LOG_NOT_FOUND")
		return
	}

	response := buildAuditLogResponse(&log, true) // 包含请求数据
	SuccessResponse(c, "Audit log retrieved successfully", response)
}

// GetAuditStats 获取审计统计
func (h *AuditHandler) GetAuditStats(c *gin.Context) {
	// 检查权限
	userRole, exists := c.Get("user_role")
	if !exists || userRole != "admin" {
		ErrorResponse(c, http.StatusForbidden, "Insufficient permissions", "ADMIN_REQUIRED")
		return
	}

	// 获取时间范围参数
	days := c.DefaultQuery("days", "30")
	daysInt, err := strconv.Atoi(days)
	if err != nil || daysInt < 1 || daysInt > 365 {
		daysInt = 30
	}

	startDate := time.Now().AddDate(0, 0, -daysInt)
	db := database.GetDB()

	var stats AuditStatsResponse

	// 基础统计
	db.Model(&model.AuditLog{}).Where("created_at >= ?", startDate).Count(&stats.TotalLogs)
	db.Model(&model.AuditLog{}).Where("created_at >= ? AND success = ?", startDate, true).Count(&stats.SuccessfulLogs)
	db.Model(&model.AuditLog{}).Where("created_at >= ? AND success = ?", startDate, false).Count(&stats.FailedLogs)

	// 唯一用户数
	db.Model(&model.AuditLog{}).Where("created_at >= ? AND user_id > 0", startDate).Distinct("user_id").Count(&stats.UniqueUsers)

	// 唯一IP数
	db.Model(&model.AuditLog{}).Where("created_at >= ?", startDate).Distinct("client_ip").Count(&stats.UniqueIPs)

	// 热门操作
	var topActions []ActionStat
	db.Model(&model.AuditLog{}).
		Select("action, COUNT(*) as count").
		Where("created_at >= ?", startDate).
		Group("action").
		Order("count DESC").
		Limit(10).
		Find(&topActions)
	stats.TopActions = topActions

	// 热门用户
	var topUsers []UserStat
	db.Model(&model.AuditLog{}).
		Select("user_id, username, COUNT(*) as count").
		Where("created_at >= ? AND user_id > 0", startDate).
		Group("user_id, username").
		Order("count DESC").
		Limit(10).
		Find(&topUsers)
	stats.TopUsers = topUsers

	// 热门IP
	var topIPs []IPStat
	db.Model(&model.AuditLog{}).
		Select("client_ip, COUNT(*) as count").
		Where("created_at >= ?", startDate).
		Group("client_ip").
		Order("count DESC").
		Limit(10).
		Find(&topIPs)
	stats.TopIPs = topIPs

	// 小时活动统计
	var hourlyActivity []HourlyActivityStat
	db.Raw(`
		SELECT EXTRACT(HOUR FROM created_at) as hour, COUNT(*) as count
		FROM audit_logs 
		WHERE created_at >= ?
		GROUP BY EXTRACT(HOUR FROM created_at)
		ORDER BY hour
	`, startDate).Find(&hourlyActivity)
	stats.HourlyActivity = hourlyActivity

	// 日活动统计
	var dailyActivity []DailyActivityStat
	db.Raw(`
		SELECT DATE(created_at) as date, COUNT(*) as count
		FROM audit_logs 
		WHERE created_at >= ?
		GROUP BY DATE(created_at)
		ORDER BY date
	`, startDate).Find(&dailyActivity)
	stats.DailyActivity = dailyActivity

	SuccessResponse(c, "Audit statistics retrieved successfully", stats)
}

// ExportAuditLogs 导出审计日志
func (h *AuditHandler) ExportAuditLogs(c *gin.Context) {
	// 检查权限
	userRole, exists := c.Get("user_role")
	if !exists || userRole != "admin" {
		ErrorResponse(c, http.StatusForbidden, "Insufficient permissions", "ADMIN_REQUIRED")
		return
	}

	// 获取导出格式
	format := c.DefaultQuery("format", "csv")
	if format != "csv" && format != "json" {
		ErrorResponse(c, http.StatusBadRequest, "Invalid export format", "INVALID_FORMAT")
		return
	}

	// 获取筛选参数（复用列表请求的参数）
	var req AuditLogListRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		ErrorResponse(c, http.StatusBadRequest, "Invalid query parameters", err.Error())
		return
	}

	// 限制导出数量
	if req.PageSize > 10000 {
		req.PageSize = 10000
	}

	db := database.GetDB()
	query := db.Model(&model.AuditLog{})

	// 应用相同的筛选条件（省略重复代码，与GetAuditLogs相同）
	// ... 筛选逻辑 ...

	// 查询数据
	var logs []model.AuditLog
	if err := query.Limit(req.PageSize).Find(&logs).Error; err != nil {
		ErrorResponse(c, http.StatusInternalServerError, "Failed to fetch audit logs", err.Error())
		return
	}

	// 根据格式导出
	if format == "csv" {
		h.exportCSV(c, logs)
	} else {
		h.exportJSON(c, logs)
	}
}

// exportCSV 导出CSV格式
func (h *AuditHandler) exportCSV(c *gin.Context, logs []model.AuditLog) {
	c.Header("Content-Type", "text/csv")
	c.Header("Content-Disposition", "attachment; filename=audit_logs.csv")

	// CSV头部
	csvHeader := "ID,UserID,Username,Action,Resource,ResourceID,Method,Path,ClientIP,StatusCode,Success,Message,Duration,CreatedAt\n"
	c.Writer.WriteString(csvHeader)

	// CSV数据
	for _, log := range logs {
		line := fmt.Sprintf("%d,%d,%s,%s,%s,%s,%s,%s,%s,%d,%t,%s,%d,%s\n",
			log.ID, log.UserID, log.Username, log.Action, log.Resource, log.ResourceID,
			log.Method, log.Path, log.ClientIP, log.StatusCode, log.Success,
			log.Message, log.Duration, log.CreatedAt.Format("2006-01-02 15:04:05"))
		c.Writer.WriteString(line)
	}
}

// exportJSON 导出JSON格式
func (h *AuditHandler) exportJSON(c *gin.Context, logs []model.AuditLog) {
	c.Header("Content-Type", "application/json")
	c.Header("Content-Disposition", "attachment; filename=audit_logs.json")

	// 构建响应
	logResponses := make([]AuditLogResponse, len(logs))
	for i, log := range logs {
		logResponses[i] = buildAuditLogResponse(&log, false)
	}

	c.JSON(http.StatusOK, gin.H{
		"logs":        logResponses,
		"total":       len(logResponses),
		"exported_at": time.Now(),
	})
}

// buildAuditLogResponse 构建审计日志响应
func buildAuditLogResponse(log *model.AuditLog, includeRequestData bool) AuditLogResponse {
	response := AuditLogResponse{
		ID:         log.ID,
		UserID:     log.UserID,
		Username:   log.Username,
		Action:     log.Action,
		Resource:   log.Resource,
		ResourceID: log.ResourceID,
		Method:     log.Method,
		Path:       log.Path,
		ClientIP:   log.ClientIP,
		UserAgent:  log.UserAgent,
		StatusCode: log.StatusCode,
		Success:    log.Success,
		Message:    log.Message,
		Duration:   log.Duration,
		CreatedAt:  log.CreatedAt,
	}

	if includeRequestData {
		response.RequestData = log.RequestData
	}

	return response
}