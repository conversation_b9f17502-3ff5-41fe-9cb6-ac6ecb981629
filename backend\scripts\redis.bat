@echo off
chcp 65001 >nul
echo ========================================
echo Redis Management Tool
echo ========================================

echo.
echo 1. Test Redis Connection
echo 2. View Redis Info
echo 3. Clear Redis Cache
echo 4. Start Local Redis Server
echo 5. Install Redis for Windows
echo 6. Exit
echo.

set /p choice=Please select operation (1-6): 

if "%choice%"=="1" (
    echo Testing Redis connection...
    go run cmd/redis-test/main.go
) else if "%choice%"=="2" (
    echo Viewing Redis info...
    echo Please ensure Redis server is running
    redis-cli info server 2>nul || echo Redis CLI not available or server not running
) else if "%choice%"=="3" (
    echo WARNING: This will clear all Redis cache data!
    set /p confirm=Confirm clear cache? (y/N): 
    if /i "%confirm%"=="y" (
        redis-cli flushdb 2>nul && echo Cache cleared || echo Clear failed, please check Redis connection
    ) else (
        echo Operation cancelled
    )
) else if "%choice%"=="4" (
    echo Starting local Redis server...
    echo Note: Redis for Windows must be installed first
    echo.
    echo If Redis is installed, manually run: redis-server
    pause
) else if "%choice%"=="5" (
    echo Installing Redis for Windows using Chocolatey...
    echo.
    echo Checking if Chocolatey is installed...
    choco --version >nul 2>&1
    if errorlevel 1 (
        echo Chocolatey not found. Installing Chocolatey first...
        powershell -Command "Set-ExecutionPolicy Bypass -Scope Process -Force; [System.Net.ServicePointManager]::SecurityProtocol = [System.Net.ServicePointManager]::SecurityProtocol -bor 3072; iex ((New-Object System.Net.WebClient).DownloadString('https://community.chocolatey.org/install.ps1'))"
    )
    echo Installing Redis...
    choco install redis-64 -y
    echo.
    echo Redis installation completed. You can now start Redis with: redis-server
    pause
) else if "%choice%"=="6" (
    exit
) else (
    echo Invalid selection, please run the script again
)

pause