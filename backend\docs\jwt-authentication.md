# JWT认证中间件文档

## 概述

软件网络授权系统实现了完整的JWT（JSON Web Token）认证机制，包括令牌生成、验证、刷新和撤销功能。系统支持基于角色的访问控制（RBAC），并与Redis缓存集成实现令牌黑名单功能。

## 功能特性

### ✅ 已实现功能

1. **JWT令牌管理**
   - 访问令牌和刷新令牌生成
   - 令牌验证和解析
   - 令牌刷新机制
   - 令牌撤销和黑名单

2. **密码安全**
   - bcrypt密码加密
   - 密码强度验证
   - 密码验证功能

3. **认证中间件**
   - 强制认证中间件
   - 可选认证中间件
   - 令牌黑名单检查
   - 缓存集成验证

4. **权限控制**
   - 基于角色的访问控制
   - 管理员权限检查
   - 自定义权限验证
   - 资源访问控制

5. **缓存集成**
   - 令牌信息缓存
   - 用户会话缓存
   - 令牌黑名单缓存
   - 优雅降级处理

## 架构设计

### JWT令牌结构

**访问令牌Claims：**
```json
{
  "user_id": 1,
  "username": "admin",
  "role": "admin",
  "token_id": "1754794718493453900",
  "iss": "software-auth",
  "sub": "1",
  "aud": ["software-auth"],
  "exp": 1754881118,
  "nbf": 1754794718,
  "iat": 1754794718,
  "jti": "1754794718493453900"
}
```

**刷新令牌Claims：**
```json
{
  "user_id": 1,
  "username": "admin", 
  "role": "admin",
  "token_id": "1754794718493453900_refresh",
  "iss": "software-auth",
  "sub": "1",
  "aud": ["software-auth-refresh"],
  "exp": 1755399518,
  "nbf": 1754794718,
  "iat": 1754794718,
  "jti": "1754794718493453900_refresh"
}
```

### 令牌生命周期

```mermaid
graph TD
    A[用户登录] --> B[生成令牌对]
    B --> C[访问令牌 24小时]
    B --> D[刷新令牌 7天]
    C --> E{令牌过期?}
    E -->|是| F[使用刷新令牌]
    E -->|否| G[继续使用]
    F --> H[生成新令牌对]
    H --> C
    D --> I{刷新令牌过期?}
    I -->|是| J[重新登录]
    I -->|否| F
    G --> K[用户操作]
    K --> L{用户登出?}
    L -->|是| M[撤销令牌]
    L -->|否| G
    M --> N[添加到黑名单]
```

## 使用方法

### 1. JWT服务

```go
// 创建JWT服务
jwtService := auth.NewJWTService(&config.JWT)

// 生成令牌
user := &model.User{
    ID:       1,
    Username: "admin",
    Role:     "admin",
}
tokens, err := jwtService.GenerateToken(user)

// 验证令牌
claims, err := jwtService.ValidateToken(tokenString)

// 刷新令牌
newTokens, err := jwtService.RefreshToken(refreshToken)

// 撤销令牌
err = jwtService.RevokeToken(tokenID)
```

### 2. 密码服务

```go
// 创建密码服务
passwordService := auth.NewPasswordService()

// 加密密码
hashedPassword, err := passwordService.HashPassword("password123")

// 验证密码
err = passwordService.VerifyPassword(hashedPassword, "password123")

// 检查密码强度
err = passwordService.IsStrongPassword("weakpass")
```

### 3. 认证中间件

```go
// 强制认证中间件
router.Use(middleware.AuthMiddleware(&config.JWT))

// 可选认证中间件
router.Use(middleware.OptionalAuth(&config.JWT))

// 角色权限中间件
router.Use(middleware.RequireRole("admin", "user"))
router.Use(middleware.RequireAdmin())

// 获取当前用户
currentUser := middleware.GetCurrentUser(c)
```

### 4. API路由保护

```go
// 公开路由
public := router.Group("/api/v1")
{
    public.POST("/auth/login", authHandler.Login)
    public.POST("/auth/refresh", authHandler.RefreshToken)
}

// 需要认证的路由
authenticated := router.Group("/api/v1")
authenticated.Use(middleware.AuthMiddleware(&config.JWT))
{
    authenticated.POST("/auth/logout", authHandler.Logout)
    authenticated.GET("/auth/profile", authHandler.GetProfile)
}

// 管理员专用路由
admin := authenticated.Group("/admin")
admin.Use(middleware.RequireAdmin())
{
    admin.GET("/users", userHandler.GetUsers)
    admin.POST("/users", userHandler.CreateUser)
}
```

## API接口

### 认证接口

#### 用户登录
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "username": "admin",
  "password": "AdminPass123!"
}
```

**响应：**
```json
{
  "message": "Login successful",
  "data": {
    "user": {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>",
      "role": "admin",
      "status": "active"
    },
    "tokens": {
      "access_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
      "token_type": "Bearer",
      "expires_in": 86400
    }
  }
}
```

#### 刷新令牌
```http
POST /api/v1/auth/refresh
Content-Type: application/json

{
  "refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

#### 用户登出
```http
POST /api/v1/auth/logout
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### 获取用户资料
```http
GET /api/v1/auth/profile
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
```

#### 修改密码
```http
POST /api/v1/auth/change-password
Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
Content-Type: application/json

{
  "old_password": "OldPass123!",
  "new_password": "NewPass123!"
}
```

## 错误处理

### 错误码定义

| 错误码 | HTTP状态码 | 描述 |
|--------|------------|------|
| MISSING_AUTH_HEADER | 401 | 缺少Authorization头 |
| INVALID_AUTH_FORMAT | 401 | Authorization头格式无效 |
| MISSING_TOKEN | 401 | 缺少令牌 |
| TOKEN_EXPIRED | 401 | 令牌已过期 |
| INVALID_TOKEN | 401 | 令牌无效 |
| TOKEN_REVOKED | 401 | 令牌已被撤销 |
| TOKEN_USER_MISMATCH | 401 | 令牌用户不匹配 |
| INSUFFICIENT_PERMISSIONS | 403 | 权限不足 |
| INVALID_CREDENTIALS | 401 | 凭据无效 |
| USER_INACTIVE | 403 | 用户账户未激活 |
| USER_BLOCKED | 403 | 用户账户被封禁 |

### 错误响应格式

```json
{
  "error": "Token has expired",
  "message": "Authentication failed",
  "code": "TOKEN_EXPIRED"
}
```

## 安全特性

### 1. 密码安全

- **加密算法**：使用bcrypt进行密码哈希
- **密码强度**：要求至少8位，包含3种字符类型
- **盐值**：bcrypt自动生成随机盐值

### 2. 令牌安全

- **签名算法**：使用HMAC-SHA256签名
- **令牌过期**：访问令牌24小时，刷新令牌7天
- **令牌撤销**：支持令牌黑名单机制
- **令牌唯一性**：每个令牌都有唯一ID

### 3. 传输安全

- **HTTPS**：生产环境强制使用HTTPS
- **头部验证**：严格验证Authorization头格式
- **CORS**：配置跨域资源共享策略

### 4. 会话安全

- **会话管理**：Redis缓存用户会话信息
- **并发控制**：支持单用户多会话管理
- **自动清理**：过期会话自动清理

## 配置说明

### JWT配置

```yaml
jwt:
  secret: "your-super-secret-jwt-key-change-in-production"
  expire_hours: 24              # 访问令牌过期时间（小时）
  refresh_expire_hours: 168     # 刷新令牌过期时间（小时，7天）
  issuer: "software-auth"       # 令牌发行者
```

### 安全配置

```yaml
security:
  rate_limit:
    enabled: true
    requests_per_minute: 60     # API限流
  cors:
    enabled: true
    allowed_origins: ["http://localhost:3000"]
    allowed_methods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"]
    allowed_headers: ["Origin", "Content-Type", "Authorization"]
```

## 测试工具

### JWT功能测试

```bash
# 测试JWT令牌生成、验证、刷新和撤销
go run ./cmd/jwt-test/main.go
```

### 认证中间件测试

```bash
# 测试认证中间件和权限控制
go run ./cmd/auth-test/main.go
```

### 测试输出示例

```
JWT Authentication Test Tool
============================
✅ Password hashed successfully
✅ Password verification successful
✅ Tokens generated successfully
✅ Token validation successful
✅ Token refresh successful
✅ Token revocation successful
🎉 All JWT tests completed!
```

## 最佳实践

### 1. 令牌管理

- **短期访问令牌**：使用较短的过期时间（24小时）
- **长期刷新令牌**：用于获取新的访问令牌
- **令牌撤销**：用户登出时撤销令牌
- **黑名单检查**：验证令牌时检查黑名单

### 2. 密码策略

- **强密码要求**：至少8位，包含大小写字母、数字、特殊字符
- **密码加密**：使用bcrypt进行不可逆加密
- **密码更新**：定期提醒用户更新密码
- **密码历史**：防止重复使用最近的密码

### 3. 权限控制

- **最小权限原则**：用户只获得必要的权限
- **角色分离**：明确区分不同角色的权限
- **资源保护**：敏感资源需要额外权限验证
- **权限审计**：记录权限变更和访问日志

### 4. 错误处理

- **统一错误格式**：使用标准的错误响应格式
- **错误码标准化**：定义清晰的错误码体系
- **安全错误信息**：避免泄露敏感信息
- **日志记录**：记录认证失败和异常情况

## 故障排除

### 常见问题

1. **令牌验证失败**
   - 检查JWT密钥配置
   - 验证令牌格式和签名
   - 确认令牌未过期

2. **权限被拒绝**
   - 检查用户角色配置
   - 验证权限中间件设置
   - 确认路由权限要求

3. **缓存连接失败**
   - 检查Redis连接配置
   - 验证Redis服务状态
   - 系统可在无Redis情况下运行

4. **密码验证失败**
   - 检查密码强度要求
   - 验证bcrypt配置
   - 确认密码加密正确

### 调试工具

```bash
# 检查JWT配置
go run ./cmd/jwt-test/main.go

# 测试认证中间件
go run ./cmd/auth-test/main.go

# 检查服务器状态
curl http://localhost:8080/health
```

## 总结

JWT认证中间件已成功实现，具备以下特性：

✅ **完整的JWT功能** - 令牌生成、验证、刷新和撤销
✅ **安全的密码管理** - bcrypt加密和强度验证
✅ **灵活的权限控制** - 基于角色的访问控制
✅ **缓存集成** - Redis黑名单和会话管理
✅ **优雅降级** - 无Redis时仍可正常工作
✅ **完整的测试** - 单元测试和集成测试
✅ **详细的文档** - API文档和使用指南

系统现在具备了企业级的认证和授权能力，可以安全地保护API接口和用户数据。