<template>
  <div class="main-layout">
    <!-- 侧边栏 -->
    <div class="sidebar" :class="{ collapsed: sidebarCollapsed }">
      <div class="sidebar-header">
        <div class="logo">
          <span class="logo-icon">🔐</span>
          <span class="logo-text" v-show="!sidebarCollapsed">软件网络授权系统</span>
        </div>
        <button @click="toggleSidebar" class="collapse-btn">
          {{ sidebarCollapsed ? '→' : '←' }}
        </button>
      </div>
      
      <nav class="nav-menu">
        <router-link to="/dashboard" class="nav-item" active-class="active">
          <span class="nav-icon">📊</span>
          <span class="nav-text" v-show="!sidebarCollapsed">仪表板</span>
          <div class="nav-tooltip" v-show="sidebarCollapsed">仪表板</div>
        </router-link>
        
        <router-link to="/licenses" class="nav-item" active-class="active">
          <span class="nav-icon">📄</span>
          <span class="nav-text" v-show="!sidebarCollapsed">许可证管理</span>
          <div class="nav-tooltip" v-show="sidebarCollapsed">许可证管理</div>
        </router-link>
        
        <router-link to="/users" class="nav-item" active-class="active">
          <span class="nav-icon">👥</span>
          <span class="nav-text" v-show="!sidebarCollapsed">用户管理</span>
          <div class="nav-tooltip" v-show="sidebarCollapsed">用户管理</div>
        </router-link>
        
        <router-link to="/reports" class="nav-item" active-class="active">
          <span class="nav-icon">📈</span>
          <span class="nav-text" v-show="!sidebarCollapsed">统计报告</span>
          <div class="nav-tooltip" v-show="sidebarCollapsed">统计报告</div>
        </router-link>
        
        <div class="nav-divider" v-show="!sidebarCollapsed"></div>
        
        <router-link to="/profile" class="nav-item" active-class="active">
          <span class="nav-icon">👤</span>
          <span class="nav-text" v-show="!sidebarCollapsed">个人资料</span>
          <div class="nav-tooltip" v-show="sidebarCollapsed">个人资料</div>
        </router-link>
      </nav>
      
      <div class="sidebar-footer">
        <div class="user-info" v-show="!sidebarCollapsed">
          <div class="user-avatar">{{ username.charAt(0).toUpperCase() }}</div>
          <div class="user-details">
            <div class="user-name">{{ username }}</div>
            <div class="user-role">系统管理员</div>
          </div>
        </div>
        <button @click="logout" class="logout-btn" :title="sidebarCollapsed ? '退出登录' : ''">
          <span class="logout-icon">🚪</span>
          <span class="logout-text" v-show="!sidebarCollapsed">退出登录</span>
        </button>
      </div>
    </div>
    
    <!-- 主内容区域 -->
    <div class="main-content">
      <!-- 顶部导航栏 -->
      <header class="top-header">
        <div class="header-left">
          <div class="breadcrumb">
            <span class="breadcrumb-item">首页</span>
            <span class="breadcrumb-separator">/</span>
            <span class="breadcrumb-item active">{{ pageTitle }}</span>
          </div>
        </div>
        <div class="header-right">
          <div class="header-actions">
            <button class="header-btn" @click="refreshPage" title="刷新页面">
              🔄
            </button>
            <button class="header-btn" @click="toggleFullscreen" title="全屏">
              📺
            </button>
            <div class="time-display">
              {{ currentTime }}
            </div>
          </div>
        </div>
      </header>
      
      <!-- 页面内容 -->
      <main class="page-content">
        <div class="content-wrapper">
          <router-view />
        </div>
      </main>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'

const route = useRoute()
const router = useRouter()

const username = ref(localStorage.getItem('username') || '管理员')
const sidebarCollapsed = ref(false)
const currentTime = ref('')

const pageTitle = computed(() => {
  return route.meta?.title || '页面'
})

// 更新时间
const updateTime = () => {
  currentTime.value = new Date().toLocaleString('zh-CN', {
    month: '2-digit',
    day: '2-digit',
    hour: '2-digit',
    minute: '2-digit'
  })
}

// 切换侧边栏
const toggleSidebar = () => {
  sidebarCollapsed.value = !sidebarCollapsed.value
  localStorage.setItem('sidebarCollapsed', sidebarCollapsed.value.toString())
}

// 刷新页面
const refreshPage = () => {
  window.location.reload()
}

// 全屏切换
const toggleFullscreen = () => {
  if (!document.fullscreenElement) {
    document.documentElement.requestFullscreen()
  } else {
    document.exitFullscreen()
  }
}

// 退出登录
const logout = () => {
  if (confirm('确定要退出登录吗？')) {
    localStorage.removeItem('isLoggedIn')
    localStorage.removeItem('username')
    router.push('/login')
  }
}

onMounted(() => {
  // 恢复侧边栏状态
  const savedState = localStorage.getItem('sidebarCollapsed')
  if (savedState) {
    sidebarCollapsed.value = savedState === 'true'
  }
  
  // 更新时间
  updateTime()
  setInterval(updateTime, 60000) // 每分钟更新一次
})
</script>

<style scoped>
.main-layout {
  display: flex;
  height: 100vh;
  background: #f0f2f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

/* 侧边栏样式 */
.sidebar {
  width: 260px;
  background: linear-gradient(180deg, #001529 0%, #002140 100%);
  color: white;
  display: flex;
  flex-direction: column;
  transition: width 0.3s ease;
  box-shadow: 2px 0 8px rgba(0, 0, 0, 0.15);
  position: relative;
  z-index: 100;
}

.sidebar.collapsed {
  width: 80px;
}

.sidebar-header {
  padding: 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  min-height: 64px;
}

.logo {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.logo-icon {
  font-size: 28px;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
}

.logo-text {
  font-size: 16px;
  font-weight: 600;
  white-space: nowrap;
  opacity: 1;
  transition: opacity 0.3s ease;
}

.collapse-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  color: white;
  border-radius: 6px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
}

.collapse-btn:hover {
  background: rgba(255, 255, 255, 0.2);
}

.nav-menu {
  flex: 1;
  padding: 20px 0;
  overflow-y: auto;
}

.nav-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 14px 20px;
  color: rgba(255, 255, 255, 0.7);
  text-decoration: none;
  transition: all 0.3s ease;
  position: relative;
  margin: 2px 12px;
  border-radius: 8px;
}

.nav-item:hover {
  background: rgba(24, 144, 255, 0.2);
  color: white;
  transform: translateX(4px);
}

.nav-item.active {
  background: linear-gradient(90deg, #1890ff, #40a9ff);
  color: white;
  box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
}

.nav-icon {
  font-size: 18px;
  min-width: 18px;
  text-align: center;
}

.nav-text {
  font-size: 14px;
  font-weight: 500;
  white-space: nowrap;
}

.nav-tooltip {
  position: absolute;
  left: 70px;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 12px;
  white-space: nowrap;
  opacity: 0;
  pointer-events: none;
  transition: opacity 0.2s;
  z-index: 1000;
}

.nav-item:hover .nav-tooltip {
  opacity: 1;
}

.nav-divider {
  height: 1px;
  background: rgba(255, 255, 255, 0.1);
  margin: 16px 20px;
}

.sidebar-footer {
  padding: 20px;
  border-top: 1px solid rgba(255, 255, 255, 0.1);
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
  padding: 12px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
}

.user-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #1890ff, #722ed1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 16px;
}

.user-details {
  flex: 1;
}

.user-name {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 2px;
}

.user-role {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.6);
}

.logout-btn {
  width: 100%;
  padding: 12px;
  background: rgba(255, 77, 79, 0.2);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  transition: all 0.2s;
  font-size: 14px;
}

.logout-btn:hover {
  background: rgba(255, 77, 79, 0.3);
  transform: translateY(-1px);
}

/* 主内容区域 */
.main-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.top-header {
  background: white;
  padding: 16px 24px;
  border-bottom: 1px solid #f0f0f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  position: relative;
  z-index: 10;
}

.breadcrumb {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
}

.breadcrumb-item {
  color: #8c8c8c;
}

.breadcrumb-item.active {
  color: #1890ff;
  font-weight: 500;
}

.breadcrumb-separator {
  color: #d9d9d9;
}

.header-right {
  display: flex;
  align-items: center;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

.header-btn {
  width: 36px;
  height: 36px;
  border: none;
  background: #f5f5f5;
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s;
  font-size: 16px;
}

.header-btn:hover {
  background: #e6f7ff;
  color: #1890ff;
}

.time-display {
  padding: 8px 16px;
  background: linear-gradient(135deg, #f0f9ff, #e6f7ff);
  border: 1px solid #b3d8ff;
  border-radius: 20px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  color: #1890ff;
  font-weight: 500;
}

.page-content {
  flex: 1;
  overflow-y: auto;
  background: #f0f2f5;
}

.content-wrapper {
  padding: 24px;
  min-height: 100%;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .sidebar {
    position: fixed;
    left: 0;
    top: 0;
    height: 100vh;
    z-index: 1000;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
  }
  
  .sidebar.show {
    transform: translateX(0);
  }
  
  .main-content {
    margin-left: 0;
  }
  
  .top-header {
    padding: 12px 16px;
  }
  
  .content-wrapper {
    padding: 16px;
  }
  
  .header-actions {
    gap: 8px;
  }
  
  .time-display {
    display: none;
  }
}

/* 滚动条样式 */
.nav-menu::-webkit-scrollbar {
  width: 4px;
}

.nav-menu::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.nav-menu::-webkit-scrollbar-thumb {
  background: rgba(255, 255, 255, 0.3);
  border-radius: 2px;
}

.page-content::-webkit-scrollbar {
  width: 6px;
}

.page-content::-webkit-scrollbar-track {
  background: #f0f0f0;
}

.page-content::-webkit-scrollbar-thumb {
  background: #d9d9d9;
  border-radius: 3px;
}

.page-content::-webkit-scrollbar-thumb:hover {
  background: #bfbfbf;
}
</style>